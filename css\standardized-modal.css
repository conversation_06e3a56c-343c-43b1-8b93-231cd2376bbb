/**
 * Standardized Modal Styles
 * Consistent styling for all modals based on the welcome modal design
 */

/* Modal Overlay - Common for all modals */
.standard-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: none;
    /* Ensure the overlay is always visible and covers the entire screen */
    overflow: hidden;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    min-width: 100vw;
    min-height: 100vh;
}

/* Modal Container */
.standard-modal {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    width: 500px; /* Default width - can be overridden */
    max-width: 90%;
    overflow: hidden;
    animation: standardModalFadeIn 0.3s ease-out;
    display: flex;
    flex-direction: column;
}

/* Modal size variations */
.standard-modal.modal-large {
    width: 700px;
    max-width: 90%;
}

.standard-modal.modal-extra-large {
    width: 900px;
    max-width: 90%;
}

.standard-modal.modal-full {
    width: 95%;
    max-width: 1900px;
}

/* Chart modal specific styles */
.standard-modal#chart-modal {
    width: 95%;
    max-width: 1900px;
    height: 90vh;
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin: 0 auto !important;
}

.standard-modal#chart-modal .standard-modal-content {
    max-height: calc(90vh - 150px);
    width: 100% !important;
    overflow-y: auto !important;
}

/* Ensure chart dialog container is properly sized */
.chart-dialog-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Ensure chart dialog content is properly sized */
.chart-dialog-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Ensure all modals have consistent styling */
.chart-dialog .standard-modal-overlay,
.chart-dialog.standard-modal-overlay,
.chart-dialog.excel-modal,
.modal,
.excel-modal,
.welcome-overlay,
.modal-overlay-container,
.excel-modal.chart-dialog,
.standard-modal-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: rgba(0, 0, 0, 0.5) !important;
    z-index: 9999 !important;
    backdrop-filter: none !important;
    overflow: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Export modal specific styles */
.standard-modal#export-modal {
    width: 700px;
    max-width: 90%;
}

/* Modal Header */
.standard-modal-header {
    background: linear-gradient(135deg, #1a73e8, #6c5ce7);
    color: white;
    padding: 20px;
    text-align: center;
    position: relative;
}

/* Close Button */
.standard-modal-close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: transparent;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.standard-modal-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.standard-modal-close:focus {
    outline: none;
}

/* Modal Header Title */
.standard-modal-header h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 500;
}

.standard-modal-header h2 {
    margin: 0;
    font-size: 22px;
    font-weight: 500;
}

.standard-modal-header p {
    margin: 10px 0 0;
    font-size: 14px;
    opacity: 0.9;
}

/* Logo in header (optional) */
.standard-modal-logo {
    width: 80px;
    height: 80px;
    margin: 0 auto 15px;
    background-color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.standard-modal-logo img,
.standard-modal-logo .material-icons {
    width: 50px;
    height: 50px;
    font-size: 36px;
    color: #1a73e8;
}

/* Modal Content */
.standard-modal-content {
    padding: 30px;
    text-align: center;
    overflow-y: auto;
    max-height: 70vh;
}

/* Modal Footer */
.standard-modal-footer {
    padding: 15px;
    text-align: center;
    background-color: #f8f9fa;
    border-top: 1px solid #e8eaed;
    display: flex;
    justify-content: flex-end;
    gap: 10px; /* Add spacing between buttons */
}

/* Standard Button Styles */
.standard-modal-footer button,
.primary-button,
.secondary-button {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
    border: none;
}

.standard-modal-footer button.primary-button,
.primary-button {
    background-color: #1a73e8;
    color: white;
}

.standard-modal-footer button.primary-button:hover,
.primary-button:hover {
    background-color: #1765cc;
}

.standard-modal-footer button.secondary-button,
.secondary-button {
    background-color: #f8f9fa;
    color: #1a73e8;
    border: 1px solid #dadce0;
}

.standard-modal-footer button.secondary-button:hover,
.secondary-button:hover {
    background-color: #f1f3f4;
}

/* Button with icon */
.standard-modal-footer button .material-icons,
.primary-button .material-icons,
.secondary-button .material-icons {
    margin-right: 8px;
    font-size: 18px;
}

/* Animations */
@keyframes standardModalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes standardModalFadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

/* Compatibility classes for existing modals */
/* Welcome Modal */
.welcome-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.welcome-modal {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    width: 500px;
    max-width: 90%;
    overflow: hidden;
    animation: standardModalFadeIn 0.3s ease-out;
    display: flex;
    flex-direction: column;
}

.welcome-header {
    background: linear-gradient(135deg, #1a73e8, #6c5ce7);
    color: white;
    padding: 20px;
    text-align: center;
    position: relative;
}

.welcome-close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: transparent;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.welcome-close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.welcome-content {
    padding: 30px;
    text-align: center;
    overflow-y: auto;
    max-height: 70vh;
}

.welcome-footer {
    padding: 15px;
    text-align: center;
    background-color: #f8f9fa;
    border-top: 1px solid #e8eaed;
}

/* Excel modal compatibility */
.excel-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.excel-modal-content {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    width: 1200px;
    max-width: 95%;
    overflow: hidden;
    animation: standardModalFadeIn 0.3s ease-out;
    display: flex;
    flex-direction: column;
}

.excel-modal-header {
    background: linear-gradient(135deg, #1a73e8, #6c5ce7);
    color: white;
    padding: 20px;
    text-align: center;
    position: relative;
}

.excel-modal-close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: transparent;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.excel-modal-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.excel-modal-body {
    padding: 30px;
    overflow-y: auto;
    max-height: 70vh;
}

.excel-modal-footer {
    padding: 15px;
    background-color: #f8f9fa;
    border-top: 1px solid #e8eaed;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Chart dialog specific */
.chart-dialog .excel-modal-content,
.excel-modal.chart-dialog .excel-modal-content {
    width: 95%;
    max-width: 1900px;
    margin: 0 auto;
    position: relative;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

/* Ensure chart dialog is properly centered and fullscreen */
.excel-modal.chart-dialog,
#chart-modal-overlay,
.standard-modal-overlay.chart-dialog {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100vw !important;
    height: 100vh !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 9999 !important;
    background: rgba(0, 0, 0, 0.5) !important;
    overflow: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
    min-width: 100vw !important;
    min-height: 100vh !important;
}

/* Generic modal compatibility */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    width: 500px;
    max-width: 90%;
    overflow: hidden;
    animation: standardModalFadeIn 0.3s ease-out;
    display: flex;
    flex-direction: column;
}

.modal-header {
    background: linear-gradient(135deg, #1a73e8, #6c5ce7);
    color: white;
    padding: 20px;
    text-align: center;
    position: relative;
}

.modal-body {
    padding: 30px;
    overflow-y: auto;
    max-height: 70vh;
}

.modal-footer {
    padding: 15px;
    background-color: #f8f9fa;
    border-top: 1px solid #e8eaed;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Ensure all modal content has consistent styling */
.modal-content,
.excel-modal-content,
.welcome-modal,
.modal-content-box,
.standard-modal {
    background-color: #fff !important;
    border-radius: 8px !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
    overflow: hidden !important;
    animation: standardModalFadeIn 0.3s ease-out !important;
    display: flex !important;
    flex-direction: column !important;
}

/* Ensure all modal headers have consistent styling */
.modal-header,
.excel-modal-header,
.welcome-header,
.modal-header-content,
.standard-modal-header {
    background: linear-gradient(135deg, #1a73e8, #6c5ce7) !important;
    color: white !important;
    padding: 20px !important;
    text-align: center !important;
    position: relative !important;
}

/* Ensure all modal bodies have consistent styling */
.modal-body,
.excel-modal-body,
.welcome-content,
.modal-body-content,
.standard-modal-content {
    padding: 30px !important;
    overflow-y: auto !important;
}

/* Ensure all modal footers have consistent styling */
.modal-footer,
.excel-modal-footer,
.welcome-footer,
.modal-footer-content,
.standard-modal-footer {
    padding: 15px !important;
    background-color: #f8f9fa !important;
    border-top: 1px solid #e8eaed !important;
    display: flex !important;
    justify-content: flex-end !important;
    gap: 10px !important;
}

/* Close button compatibility */
.close-button,
.standard-modal-close,
.welcome-close-btn,
.excel-modal-close,
.modal-close-button {
    position: absolute !important;
    top: 10px !important;
    right: 10px !important;
    background: transparent !important;
    border: none !important;
    color: white !important;
    font-size: 16px !important;
    cursor: pointer !important;
    width: 30px !important;
    height: 30px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: background-color 0.2s !important;
    z-index: 10 !important;
}

.close-button:hover,
.standard-modal-close:hover,
.welcome-close-btn:hover,
.excel-modal-close:hover,
.modal-close-button:hover {
    background-color: rgba(255, 255, 255, 0.2) !important;
}

/* Modal overlay container compatibility */
.modal-overlay-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
}

.modal-overlay-container.visible {
    opacity: 1;
    pointer-events: auto;
}

.modal-content-box {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    width: 500px;
    max-width: 90%;
    overflow: hidden;
    animation: standardModalFadeIn 0.3s ease-out;
    display: flex;
    flex-direction: column;
}

.modal-header-content {
    background: linear-gradient(135deg, #1a73e8, #6c5ce7);
    color: white;
    padding: 20px;
    text-align: center;
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header-content h2 {
    margin: 0;
    font-size: 22px;
    font-weight: 500;
    color: white;
}

.modal-close-button {
    background: transparent;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.modal-close-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.modal-body-content {
    padding: 30px;
    overflow-y: auto;
    max-height: 70vh;
}

.modal-footer-content {
    padding: 15px;
    background-color: #f8f9fa;
    border-top: 1px solid #e8eaed;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Export options styling */
.export-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    padding: 10px;
}

.export-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: #f8f9fa;
}

.export-option:hover {
    background-color: #f1f3f4;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.export-option .material-icons {
    font-size: 36px;
    margin-bottom: 10px;
    color: #1a73e8;
}

.export-option span:not(.material-icons) {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}
