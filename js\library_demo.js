// js/library_demo.js
// Demonstrates how to use the various libraries

import excelUtils from './excel_utils.js';

/**
 * Library Demo class that provides examples of using the various libraries
 */
class LibraryDemo {
    constructor() {
        this.container = document.getElementById('demoContainer');
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'demoContainer';
            document.body.appendChild(this.container);
        }
    }

    /**
     * Initialize the demo
     */
    initialize() {
        this.createDemoUI();
    }

    /**
     * Create the demo UI
     */
    createDemoUI() {
        this.container.innerHTML = `
            <div class="library-demo-header">
                <h1>Library Demo</h1>
                <p>This demo showcases the various libraries available in the application.</p>
                <button id="closeLibraryDemo" class="close-demo-btn">Close Demo</button>
            </div>

            <div class="demo-section">
                <h2>Excel Library Demo</h2>
                <p>These libraries provide Excel file manipulation capabilities.</p>
                <div class="demo-controls">
                    <button id="demoXlsxPopulate" class="demo-btn">
                        <span class="material-icons">description</span>
                        <span>XlsxPopulate</span>
                    </button>
                    <button id="demoXlsx" class="demo-btn">
                        <span class="material-icons">table_chart</span>
                        <span>XLSX</span>
                    </button>
                    <button id="demoExcelJS" class="demo-btn">
                        <span class="material-icons">grid_on</span>
                        <span>ExcelJS</span>
                    </button>
                    <button id="demoLuckysheet" class="demo-btn">
                        <span class="material-icons">dashboard</span>
                        <span>Luckysheet</span>
                    </button>
                    <button id="demoHandsontable" class="demo-btn">
                        <span class="material-icons">view_comfy</span>
                        <span>Handsontable</span>
                    </button>
                </div>
                <div id="excelDemoResult" class="demo-result"></div>
            </div>

            <div class="demo-section">
                <h2>Chart Library Demo</h2>
                <p>These libraries provide charting and data visualization capabilities.</p>
                <div class="demo-controls">
                    <button id="demoApexCharts" class="demo-btn">
                        <span class="material-icons">bar_chart</span>
                        <span>ApexCharts</span>
                    </button>
                    <button id="demoECharts" class="demo-btn">
                        <span class="material-icons">pie_chart</span>
                        <span>ECharts</span>
                    </button>
                </div>
                <div id="chartDemoResult" class="demo-result"></div>
            </div>

            <div class="demo-section">
                <h2>Export Library Demo</h2>
                <p>These libraries provide export capabilities to different formats.</p>
                <div class="demo-controls">
                    <button id="demoExportCSV" class="demo-btn">
                        <span class="material-icons">insert_drive_file</span>
                        <span>CSV Export</span>
                    </button>
                    <button id="demoExportPDF" class="demo-btn">
                        <span class="material-icons">picture_as_pdf</span>
                        <span>PDF Export</span>
                    </button>
                    <button id="demoExportHTML" class="demo-btn">
                        <span class="material-icons">html</span>
                        <span>HTML Export</span>
                    </button>
                </div>
                <div id="exportDemoResult" class="demo-result"></div>
            </div>

            <div class="demo-section">
                <h2>Utility Library Demo</h2>
                <p>These libraries provide various utility functions.</p>
                <div class="demo-controls">
                    <button id="demoDayjs" class="demo-btn">
                        <span class="material-icons">date_range</span>
                        <span>Day.js</span>
                    </button>
                    <button id="demoPDFLib" class="demo-btn">
                        <span class="material-icons">picture_as_pdf</span>
                        <span>PDF-Lib</span>
                    </button>
                    <button id="demoHtml2Canvas" class="demo-btn">
                        <span class="material-icons">image</span>
                        <span>Html2Canvas</span>
                    </button>
                    <button id="demoTesseract" class="demo-btn">
                        <span class="material-icons">text_fields</span>
                        <span>Tesseract</span>
                    </button>
                    <button id="demoSortable" class="demo-btn">
                        <span class="material-icons">sort</span>
                        <span>Sortable</span>
                    </button>
                </div>
                <div id="utilityDemoResult" class="demo-result"></div>
            </div>
        `;

        // Add event listeners
        document.getElementById('closeLibraryDemo').addEventListener('click', () => {
            // Remove the demo container
            if (this.container && this.container.parentNode) {
                this.container.parentNode.removeChild(this.container);
            }
        });

        // Excel library demos
        document.getElementById('demoXlsxPopulate').addEventListener('click', () => this.demoXlsxPopulate());
        document.getElementById('demoXlsx').addEventListener('click', () => this.demoXlsx());
        document.getElementById('demoExcelJS').addEventListener('click', () => this.demoExcelJS());
        document.getElementById('demoLuckysheet').addEventListener('click', () => this.demoLuckysheet());
        document.getElementById('demoHandsontable').addEventListener('click', () => this.demoHandsontable());

        // Chart library demos
        document.getElementById('demoApexCharts').addEventListener('click', () => this.demoApexCharts());
        document.getElementById('demoECharts').addEventListener('click', () => this.demoECharts());

        // Export library demos
        document.getElementById('demoExportCSV').addEventListener('click', () => this.demoExportCSV());
        document.getElementById('demoExportPDF').addEventListener('click', () => this.demoExportPDF());
        document.getElementById('demoExportHTML').addEventListener('click', () => this.demoExportHTML());

        // Utility library demos
        document.getElementById('demoDayjs').addEventListener('click', () => this.demoDayjs());
        document.getElementById('demoPDFLib').addEventListener('click', () => this.demoPDFLib());
        document.getElementById('demoHtml2Canvas').addEventListener('click', () => this.demoHtml2Canvas());
        document.getElementById('demoTesseract').addEventListener('click', () => this.demoTesseract());
        document.getElementById('demoSortable').addEventListener('click', () => this.demoSortable());
    }

    /**
     * Demo XlsxPopulate
     */
    async demoXlsxPopulate() {
        const resultContainer = document.getElementById('excelDemoResult');
        resultContainer.innerHTML = '<p>Creating workbook with XlsxPopulate...</p>';

        try {
            // Set the active library
            excelUtils.setActiveLibrary('xlsx-populate');

            // Create a new workbook
            const workbook = await excelUtils.createBlankWorkbook();
            const sheet = excelUtils.getFirstSheet(workbook);

            // Add some data
            sheet.cell('A1').value('Name');
            sheet.cell('B1').value('Age');
            sheet.cell('C1').value('City');

            sheet.cell('A2').value('John Doe');
            sheet.cell('B2').value(30);
            sheet.cell('C2').value('New York');

            sheet.cell('A3').value('Jane Smith');
            sheet.cell('B3').value(25);
            sheet.cell('C3').value('Los Angeles');

            // Apply some styles
            sheet.range('A1:C1').style('bold', true);
            sheet.range('A1:C1').style('fill', 'DDDDDD');

            // Save the workbook
            await excelUtils.saveWorkbook(workbook, 'xlsx_populate_demo.xlsx');

            resultContainer.innerHTML = '<p>Workbook created and saved as xlsx_populate_demo.xlsx</p>';
        } catch (error) {
            resultContainer.innerHTML = `<p>Error: ${error.message}</p>`;
        }
    }

    /**
     * Demo XLSX
     */
    async demoXlsx() {
        const resultContainer = document.getElementById('excelDemoResult');
        resultContainer.innerHTML = '<p>Creating workbook with XLSX...</p>';

        try {
            // Set the active library
            excelUtils.setActiveLibrary('xlsx');

            // Create a new workbook
            const workbook = excelUtils.createBlankWorkbook();

            // Create a new worksheet
            const worksheet = XLSX.utils.aoa_to_sheet([
                ['Name', 'Age', 'City'],
                ['John Doe', 30, 'New York'],
                ['Jane Smith', 25, 'Los Angeles']
            ]);

            // Add the worksheet to the workbook
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

            // Save the workbook
            await excelUtils.saveWorkbook(workbook, 'xlsx_demo.xlsx');

            resultContainer.innerHTML = '<p>Workbook created and saved as xlsx_demo.xlsx</p>';
        } catch (error) {
            resultContainer.innerHTML = `<p>Error: ${error.message}</p>`;
        }
    }

    /**
     * Demo ExcelJS
     */
    async demoExcelJS() {
        const resultContainer = document.getElementById('excelDemoResult');
        resultContainer.innerHTML = '<p>Creating workbook with ExcelJS...</p>';

        try {
            // Set the active library
            excelUtils.setActiveLibrary('exceljs');

            // Create a new workbook
            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet('Sheet1');

            // Add column headers
            worksheet.columns = [
                { header: 'Name', key: 'name', width: 20 },
                { header: 'Age', key: 'age', width: 10 },
                { header: 'City', key: 'city', width: 20 }
            ];

            // Add rows
            worksheet.addRow({ name: 'John Doe', age: 30, city: 'New York' });
            worksheet.addRow({ name: 'Jane Smith', age: 25, city: 'Los Angeles' });

            // Style the header row
            worksheet.getRow(1).font = { bold: true };
            worksheet.getRow(1).fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFDDDDDD' }
            };

            // Save the workbook
            await excelUtils.saveWorkbook(workbook, 'exceljs_demo.xlsx');

            resultContainer.innerHTML = '<p>Workbook created and saved as exceljs_demo.xlsx</p>';
        } catch (error) {
            resultContainer.innerHTML = `<p>Error: ${error.message}</p>`;
        }
    }

    /**
     * Demo ApexCharts
     */
    demoApexCharts() {
        const resultContainer = document.getElementById('chartDemoResult');
        resultContainer.innerHTML = '<div id="apexChartDemo" style="width: 100%; height: 350px;"></div>';

        try {
            const options = {
                series: [{
                    name: 'Sales',
                    data: [30, 40, 35, 50, 49, 60, 70, 91, 125]
                }],
                chart: {
                    type: 'bar',
                    height: 350
                },
                xaxis: {
                    categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep']
                },
                title: {
                    text: 'Monthly Sales',
                    align: 'center'
                }
            };

            const chart = new ApexCharts(document.getElementById('apexChartDemo'), options);
            chart.render();
        } catch (error) {
            resultContainer.innerHTML = `<p>Error: ${error.message}</p>`;
        }
    }

    /**
     * Demo ECharts
     */
    demoECharts() {
        const resultContainer = document.getElementById('chartDemoResult');
        resultContainer.innerHTML = '<div id="eChartDemo" style="width: 100%; height: 350px;"></div>';

        try {
            const chart = echarts.init(document.getElementById('eChartDemo'));
            const option = {
                title: {
                    text: 'Monthly Sales'
                },
                tooltip: {},
                legend: {
                    data: ['Sales']
                },
                xAxis: {
                    data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep']
                },
                yAxis: {},
                series: [{
                    name: 'Sales',
                    type: 'bar',
                    data: [30, 40, 35, 50, 49, 60, 70, 91, 125]
                }]
            };

            chart.setOption(option);
        } catch (error) {
            resultContainer.innerHTML = `<p>Error: ${error.message}</p>`;
        }
    }

    /**
     * Demo CSV Export
     */
    demoExportCSV() {
        const resultContainer = document.getElementById('exportDemoResult');
        resultContainer.innerHTML = '<p>Exporting to CSV...</p>';

        try {
            // Create sample data
            const data = [
                ['Name', 'Age', 'City'],
                ['John Doe', 30, 'New York'],
                ['Jane Smith', 25, 'Los Angeles'],
                ['Bob Johnson', 45, 'Chicago'],
                ['Alice Brown', 35, 'Miami']
            ];

            // Import utils
            import('../js/utils.js').then(module => {
                const utils = module.default;

                // Export to CSV
                utils.export.exportToCsv(data, 'demo_export.csv');

                resultContainer.innerHTML = '<p>Data exported as demo_export.csv</p>';
            }).catch(error => {
                resultContainer.innerHTML = `<p>Error loading utils: ${error.message}</p>`;
            });
        } catch (error) {
            resultContainer.innerHTML = `<p>Error: ${error.message}</p>`;
        }
    }

    /**
     * Demo PDF Export
     */
    demoExportPDF() {
        const resultContainer = document.getElementById('exportDemoResult');
        resultContainer.innerHTML = '<p>Exporting to PDF...</p>';

        try {
            // Create content to export
            const content = document.createElement('div');
            content.innerHTML = `
                <h1 style="color: #1a73e8; text-align: center;">PDF Export Demo</h1>
                <p style="margin: 20px 0;">This is a sample PDF export created using PDF-Lib.</p>
                <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
                    <tr style="background-color: #f2f2f2;">
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Name</th>
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Age</th>
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">City</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">John Doe</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">30</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">New York</td>
                    </tr>
                    <tr style="background-color: #f2f2f2;">
                        <td style="border: 1px solid #ddd; padding: 8px;">Jane Smith</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">25</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">Los Angeles</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">Bob Johnson</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">45</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">Chicago</td>
                    </tr>
                </table>
                <p style="margin-top: 20px; font-style: italic; text-align: center;">Generated on ${new Date().toLocaleDateString()}</p>
            `;

            document.body.appendChild(content);

            // Import utils
            import('../js/utils.js').then(async module => {
                const utils = module.default;

                // Define PDF options for better quality
                const pdfOptions = {
                    scale: 2, // Higher scale for better quality
                    pageSize: 'A4',
                    margin: {
                        top: 40,
                        right: 40,
                        bottom: 40,
                        left: 40
                    }
                };

                // Show loading message
                resultContainer.innerHTML = '<p>Generating PDF, please wait...</p>';

                try {
                    // Export to PDF with enhanced options
                    await utils.export.exportToPdf(content, 'demo_export.pdf', pdfOptions);

                    // Remove the temporary content
                    if (document.body.contains(content)) {
                        document.body.removeChild(content);
                    }

                    resultContainer.innerHTML = '<p>Content exported as demo_export.pdf</p>';
                } catch (error) {
                    console.error('PDF export error:', error);

                    // Remove the temporary content if it still exists
                    if (document.body.contains(content)) {
                        document.body.removeChild(content);
                    }

                    resultContainer.innerHTML = `<p>Error exporting to PDF: ${error.message}</p>`;
                }
            }).catch(error => {
                console.error('Module loading error:', error);

                // Remove the temporary content if it still exists
                if (document.body.contains(content)) {
                    document.body.removeChild(content);
                }

                resultContainer.innerHTML = `<p>Error loading utils: ${error.message}</p>`;
            });
        } catch (error) {
            resultContainer.innerHTML = `<p>Error: ${error.message}</p>`;
        }
    }

    /**
     * Demo HTML Export
     */
    demoExportHTML() {
        const resultContainer = document.getElementById('exportDemoResult');
        resultContainer.innerHTML = '<p>Exporting to HTML...</p>';

        try {
            // Create a mock workbook and sheet for the demo
            const mockWorkbook = {
                title: 'Demo Workbook',
                author: 'Library Demo',
                subject: 'HTML Export Demo',
                keywords: 'html, export, demo'
            };

            // Create a mock sheet with sample data
            const mockSheet = {
                usedRange: () => ({
                    startCell: () => ({
                        rowNumber: () => 1,
                        columnNumber: () => 1
                    }),
                    endCell: () => ({
                        rowNumber: () => 4,
                        columnNumber: () => 3
                    })
                }),
                cell: (row, col) => {
                    // Sample data
                    const data = [
                        ['Name', 'Age', 'City'],
                        ['John Doe', 30, 'New York'],
                        ['Jane Smith', 25, 'Los Angeles'],
                        ['Bob Johnson', 45, 'Chicago']
                    ];

                    // Return a cell object with value and style methods
                    return {
                        value: () => {
                            if (row <= data.length && col <= data[0].length) {
                                return data[row-1][col-1];
                            }
                            return '';
                        },
                        style: (prop) => {
                            // Add some styling for the header row
                            if (row === 1) {
                                if (prop === 'bold') return true;
                                if (prop === 'fill') return 'EEEEEE';
                            }
                            return null;
                        }
                    };
                }
            };

            // Show loading message
            resultContainer.innerHTML = '<p>Creating HTML export, please wait...</p>';

            try {
                // Import the HTML export utility
                import('../js/html-export.js').then(async module => {
                    const HtmlExport = module.default;

                    // Export to HTML
                    await HtmlExport.exportToHtml(mockWorkbook, mockSheet, 'demo_export.html');

                    resultContainer.innerHTML = '<p>Data exported as demo_export.html</p>';
                }).catch(error => {
                    console.error('Module loading error:', error);
                    resultContainer.innerHTML = `<p>Error loading HTML export module: ${error.message}</p>`;
                });
            } catch (error) {
                console.error('HTML export error:', error);
                resultContainer.innerHTML = `<p>Error exporting to HTML: ${error.message}</p>`;
            }
        } catch (error) {
            resultContainer.innerHTML = `<p>Error: ${error.message}</p>`;
        }
    }
}

// Create and export a singleton instance
const libraryDemo = new LibraryDemo();
export default libraryDemo;
