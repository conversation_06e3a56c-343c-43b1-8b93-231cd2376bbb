/* Excel Table Styles */
#excelTable {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
    table-layout: fixed;
    background-color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    font-family: '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
    font-size: 11px;
    color: #333;
    position: relative;
}

/* Cell resizing styles */
.column-resizer {
    position: absolute;
    top: 0;
    right: 0;
    width: 5px;
    height: 100%;
    background-color: transparent;
    cursor: col-resize;
    z-index: 10;
}

.row-resizer {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background-color: transparent;
    cursor: row-resize;
    z-index: 10;
    transition: background-color 0.2s ease;
}

/* Top row resizer for resizing from the top */
.row-resizer-top {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background-color: transparent;
    cursor: row-resize;
    z-index: 10;
    transition: background-color 0.2s ease;
}

/* Improved visibility for row resizers in row headers */
th.row-header .row-resizer,
th.row-header .row-resizer-top {
    height: 6px; /* Slightly larger for better usability */
}

th.row-header:hover .row-resizer,
th.row-header:hover .row-resizer-top {
    background-color: rgba(26, 115, 232, 0.3); /* Light blue when hovering over row header */
}

.column-resizer:hover,
.row-resizer:hover,
.row-resizer-top:hover {
    background-color: #1a73e8;
}

.resizing {
    user-select: none;
    cursor: col-resize;
    background-color: #f1f8ff;
}

/* Disable text selection during resize */
.no-select {
    user-select: none;
}

#excelTable th {
    background-color: #f8f9fa;
    padding: 0;
    text-align: center;
    font-weight: normal;
    position: sticky;
    top: 0;
    z-index: 2;
    border: 1px solid #e2e2e2;
    user-select: none;
    font-size: 11px;
    font-family: 'Calibri', Arial, sans-serif;
    color: #333;
    height: 20px; /* Match row height */
    line-height: 20px;
}

#excelTable th.column-header,
#excelTable th.excel-col-header,
.excel-table th.column-header,
.excel-table th.excel-col-header {
    width: 64px; /* Standard Excel column width */
    min-width: 64px;
    cursor: pointer; /* Show pointer cursor on column headers */
}

#excelTable th.column-header:hover,
#excelTable th.excel-col-header:hover,
.excel-table th.column-header:hover,
.excel-table th.excel-col-header:hover {
    background-color: #e8f0fe; /* Light blue on hover */
}

#excelTable th.row-header,
#excelTable th.excel-row-header,
.excel-table th.row-header,
.excel-table th.excel-row-header {
    width: 40px; /* Excel's standard row header width */
    min-width: 40px;
    cursor: pointer; /* Show pointer cursor on row headers */
}

#excelTable th.row-header:hover,
#excelTable th.excel-row-header:hover,
.excel-table th.row-header:hover,
.excel-table th.excel-row-header:hover {
    background-color: #e8f0fe; /* Light blue on hover */
}

#excelTable th.corner-cell {
    background-color: #f8f9fa;
    border-right: 1px solid #e2e2e2;
    border-bottom: 1px solid #e2e2e2;
    width: 40px;
    min-width: 40px;
}

/* Excel table layout properties already defined above */

#excelTable td {
    border: 1px solid #e2e2e2;
    padding: 0;
    width: 64px; /* Standard Excel column width (8.43 characters) */
    height: 20px; /* Standard Excel row height (15 points) */
    position: relative;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    user-select: none;
    box-sizing: border-box;
    font-size: 11px; /* Calibri 11pt is Excel's default */
    font-family: 'Calibri', Arial, sans-serif;
    color: #333;
    background-color: #fff;
    transition: none; /* Remove transitions for snappier feel */
}

#excelTable td:hover {
    background-color: rgba(26, 115, 232, 0.05);
}

/* Active cell - single blue border (Excel style) */
#excelTable td.active-cell {
    outline: 1.5px solid #1a73e8;
    outline-offset: -1.5px;
    border-color: transparent;
    position: relative;
    z-index: 2;
}

/* Selected range - light blue background */
#excelTable td.selected {
    background-color: rgba(26, 115, 232, 0.1);
    border-color: #e2e2e2;
}

/* Selected range with active cell */
#excelTable td.selected.active-cell {
    background-color: rgba(26, 115, 232, 0.1);
    outline: 1.5px solid #1a73e8;
    outline-offset: -1.5px;
    border-color: transparent;
}

/* Merged cells */
#excelTable td.merged {
    background-color: #f8f9fa;
}

/* Cell input styles - Exact Excel match */
.cell-input {
    width: 100%;
    height: 100%;
    border: none;
    padding: 0 2px;
    margin: 0;
    box-sizing: border-box;
    font-family: 'Calibri', Arial, sans-serif;
    font-size: 11px;
    outline: none;
    background-color: transparent;
    transition: none;
    cursor: default;
    color: #333;
    line-height: 19px; /* Match Excel's internal content height */
    text-align: left;
}

/* Editing state */
.cell-input:not([readonly]) {
    cursor: text;
    background-color: #ffffff;
    outline: none;
    box-shadow: none;
}

/* Focus state - no outline, Excel uses the cell's outline */
.cell-input:focus {
    outline: none;
    box-shadow: none;
    position: relative;
    z-index: 1;
    background-color: transparent;
}

/* Hover state */
.cell-input:hover:not(:focus) {
    background-color: transparent; /* Excel doesn't change input background on hover */
}

/* Style classes */
.cell-input.bold { font-weight: bold; }
.cell-input.italic { font-style: italic; }
.cell-input.underline { text-decoration: underline; }

/* Selection overlay - Exact Excel match */
.selection-overlay {
    position: absolute;
    background-color: transparent;
    border: none;
    outline: 1.5px solid #1a73e8;
    outline-offset: -1.5px;
    pointer-events: none;
    z-index: 10;
    display: none;
    box-sizing: border-box;
}

/* Range selection overlay - Exact Excel match */
.range-selection-overlay {
    position: absolute;
    background-color: rgba(26, 115, 232, 0.1);
    border: none;
    outline: 1.5px solid #1a73e8;
    outline-offset: -1.5px;
    pointer-events: none;
    z-index: 10;
    display: none;
    box-sizing: border-box;
}

/* Selection handle for drag-to-fill - Exact Excel match */
.selection-handle {
    position: absolute;
    width: 6px;
    height: 6px;
    background-color: #1a73e8;
    border: 1px solid white;
    right: -3px;
    bottom: -3px;
    cursor: crosshair;
    z-index: 11;
    border-radius: 0;
}

/* Cell drag-drop styles */
.cell-drag-source {
    opacity: 0.5;
}

.cell-drag-target {
    background-color: rgba(26, 115, 232, 0.2);
    border: 2px dashed #1a73e8;
}

/* Column and row selection styles */
.excel-table th.excel-col-header.selected,
th.excel-col-header.selected,
.excel-table th.column-header.selected,
th.column-header.selected {
    background-color: #d0e3fc;
    border-top: 3px solid #1a73e8;
    border-bottom: 1px solid #1a73e8;
    font-weight: bold;
    color: #1a73e8;
}

.excel-table th.excel-row-header.selected,
.excel-table th.row-header.selected,
th.excel-row-header.selected,
th.row-header.selected,
td.excel-row-header.selected,
td.row-header.selected {
    background-color: #d0e3fc;
    border-left: 3px solid #1a73e8;
    border-right: 1px solid #1a73e8;
    font-weight: bold;
    color: #1a73e8;
}

/* Selected column cells */
.excel-table td.selected-column,
td.selected-column,
.excel-table td input.selected-column,
td input.selected-column {
    background-color: rgba(26, 115, 232, 0.15);
    border: 1px solid rgba(26, 115, 232, 0.5);
}

/* Selected row cells */
.excel-table td.selected-row,
td.selected-row,
.excel-table td input.selected-row,
td input.selected-row {
    background-color: rgba(26, 115, 232, 0.15);
    border: 1px solid rgba(26, 115, 232, 0.5);
}

/* Active cell in column/row selection */
.excel-table td.selected-column.active-cell,
.excel-table td.selected-row.active-cell,
td.selected-column.active-cell,
td.selected-row.active-cell {
    background-color: rgba(26, 115, 232, 0.25);
    outline: 2px solid #1a73e8;
    outline-offset: -2px;
    border-color: transparent;
}

/* Make sure inputs inside selected cells also show the selection */
.excel-table td.selected-column input,
td.selected-column input,
.excel-table td.selected-row input,
td.selected-row input {
    background-color: rgba(26, 115, 232, 0.15);
}

/* Make sure inputs inside active cells in a selection stand out */
.excel-table td.selected-column.active-cell input,
.excel-table td.selected-row.active-cell input,
td.selected-column.active-cell input,
td.selected-row.active-cell input {
    background-color: rgba(26, 115, 232, 0.25);
}
