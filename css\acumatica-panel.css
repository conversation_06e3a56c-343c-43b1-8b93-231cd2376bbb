/* css/acumatica-panel.css */

/* Acumatica Panel Styles */
.side-panel {
    position: fixed;
    top: 60px;
    right: 0;
    width: 300px;
    height: calc(100vh - 60px);
    background-color: #ffffff;
    box-shadow: -2px 0 5px rgba(0,0,0,0.1);
    z-index: 1000;
    padding: 16px;
    overflow-y: auto;
    transition: transform 0.3s ease-in-out;
    transform: translateX(100%);
}

.side-panel.visible {
    transform: translateX(0);
}

.side-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.side-panel-header h2 {
    margin: 0;
    font-size: 18px;
}

.side-panel-header button {
    background: none;
    border: none;
    cursor: pointer;
}

.connection-status {
    padding: 12px;
    background-color: #f5f5f5;
    border-radius: 4px;
    margin-bottom: 16px;
}

.connection-status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.connection-status-header-left {
    display: flex;
    align-items: center;
}

.connection-status-header-left .material-icons {
    margin-right: 8px;
}

.connection-status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.connection-status-badge.connected {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.connection-status-badge.disconnected {
    background-color: #f5f5f5;
    color: #616161;
}

.connection-details {
    font-size: 14px;
    color: #616161;
    margin-bottom: 8px;
}

.connection-details p {
    margin: 4px 0;
}

.acumatica-form {
    margin-top: 12px;
}

.form-group {
    margin-bottom: 8px;
}

.form-group label {
    display: block;
    font-size: 12px;
    margin-bottom: 4px;
}

.form-group input {
    width: 100%;
    padding: 6px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.btn {
    width: 100%;
    padding: 8px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 8px;
}

.btn-primary {
    background-color: #1976d2;
    color: white;
}

.btn-danger {
    background-color: #f44336;
    color: white;
}

.btn-success {
    background-color: #4caf50;
    color: white;
}

.btn-info {
    background-color: #2196f3;
    color: white;
}

.actions-section {
    margin-top: 20px;
}

.actions-section h3 {
    font-size: 16px;
    margin-bottom: 12px;
}

.notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 12px 16px;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 10000;
    max-width: 300px;
    animation: fadeIn 0.3s, fadeOut 0.3s 2.7s;
}

.notification.success {
    background-color: #4caf50;
    color: white;
}

.notification.error {
    background-color: #f44336;
    color: white;
}

.notification.warning {
    background-color: #ff9800;
    color: white;
}

.notification.info {
    background-color: #2196f3;
    color: white;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(20px); }
}
