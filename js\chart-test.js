/**
 * Chart System Test Utilities
 * Console-only test functions for chart system debugging
 */

// Test function to verify chart system (console use only)
function testChartSystem() {
    console.log('=== Chart System Test ===');

    // Check if chart libraries are loaded
    const hasApex = typeof window.ApexCharts !== 'undefined';
    const hasECharts = typeof window.echarts !== 'undefined';
    const hasD3 = typeof window.d3 !== 'undefined';

    console.log('Chart libraries status:', {
        ApexCharts: hasApex,
        ECharts: hasECharts,
        D3: hasD3
    });

    // Check if chart creation dialog is available
    if (window.showChartCreationDialog) {
        console.log('✓ Chart creation dialog function available');
    } else {
        console.error('✗ Chart creation dialog function not available');
    }

    // Check if chart manager is initialized
    if (window.ChartManager) {
        console.log('✓ Chart manager available');
    } else {
        console.warn('⚠ Chart manager not available');
    }

    return {
        libraries: { hasApex, hasECharts, hasD3 },
        dialogAvailable: !!window.showChartCreationDialog,
        managerAvailable: !!window.ChartManager
    };
}

// Test chart creation with sample data (console use only)
function testChartCreationWithSampleData() {
    console.log('=== Testing Chart Creation with Sample Data ===');

    // Test data
    const testRange = {
        start: { r: 1, c: 1 },
        end: { r: 5, c: 3 }
    };

    // Create sample data for testing
    const originalSheet = window.currentSheet;
    window.currentSheet = {
        cell: (r, c) => ({
            value: () => {
                if (r === 1 && c === 1) return 'Category';
                if (r === 1 && c === 2) return 'Values';
                if (r === 2 && c === 1) return 'A';
                if (r === 2 && c === 2) return 10;
                if (r === 3 && c === 1) return 'B';
                if (r === 3 && c === 2) return 20;
                if (r === 4 && c === 1) return 'C';
                if (r === 4 && c === 2) return 15;
                if (r === 5 && c === 1) return 'D';
                if (r === 5 && c === 2) return 25;
                return '';
            }
        })
    };

    // Test the chart creation dialog
    try {
        if (window.showChartCreationDialog) {
            window.showChartCreationDialog(testRange);
            console.log('✓ Chart creation dialog opened successfully');
        } else {
            console.error('✗ Chart creation dialog function not available');
        }
    } catch (error) {
        console.error('✗ Error testing chart creation:', error);
    } finally {
        // Restore original sheet
        window.currentSheet = originalSheet;
    }
}

// Export test functions for console use only
window.testChartSystem = testChartSystem;
window.testChartCreationWithSampleData = testChartCreationWithSampleData;

console.log('Chart test utilities loaded. Use testChartSystem() or testChartCreationWithSampleData() in console.');
