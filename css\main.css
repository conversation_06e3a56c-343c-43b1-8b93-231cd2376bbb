/* Enven Bridge Sheet Styles */
body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    background-color: #f8f9fa;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    overflow: hidden;
}

.full-screen {
    height: 100vh;
    width: 100vw;
    display: flex;
    flex-direction: column;
}

.app-title {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dadce0;
}

.app-title h1 {
    font-size: 20px;
    font-weight: 500;
    color: #202124;
    margin: 0;
}

.app-title .material-icons {
    font-size: 24px;
    color: #1a73e8;
}

.app-title .material-icons {
    color: #1a73e8;
    font-size: 24px;
}

.controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.controls input[type="file"] {
    padding: 8px;
    border: 1px solid #dadce0;
    border-radius: 4px;
    font-size: 14px;
    background-color: #ffffff;
}

.primary-button {
    padding: 8px 16px;
    background-color: #1a73e8;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
}

.primary-button:hover {
    background-color: #1557b0;
}

.primary-button:disabled {
    background-color: #dadce0;
    cursor: not-allowed;
}

.primary-button .material-icons {
    font-size: 18px;
}

#toolbar {
    display: flex;
    gap: 3px;
    align-items: center;
    padding: 3px 6px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dadce0;
    width: 100%;
    overflow-x: auto;
    flex-wrap: nowrap;
    box-sizing: border-box;
    min-height: 64px;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
}

.toolbar-group {
    display: flex;
    gap: 2px;
    align-items: center;
    padding: 1px;
    margin-right: 3px;
    height: 30px;
}

#toolbar button {
    padding: 3px;
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: 3px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.15s;
    min-width: 28px;
    height: 28px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #5f6368;
    position: relative;
}

#toolbar button:hover {
    background-color: #e8eaed;
    border-color: #dadce0;
}

#toolbar button.active {
    background-color: #e8f0fe;
    border-color: #1a73e8;
    color: #1a73e8;
}

#toolbar button:active {
    background-color: #e8f0fe;
    border-color: #1a73e8;
}

#toolbar button .material-icons {
    font-size: 20px;
    color: #5f6368;
}

#toolbar select {
    padding: 2px 18px 2px 4px;
    border: 1px solid #dadce0;
    border-radius: 3px;
    font-size: 12px;
    background-color: white;
    cursor: pointer;
    height: 24px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2214%22%20height%3D%2214%22%20viewBox%3D%220%200%2014%2014%22%3E%3Cpath%20fill%3D%22%23666%22%20d%3D%22M7%2010L3.5%206h7L7%2010z%22%2F%3E%3C%2Fsvg%3E");
    background-repeat: no-repeat;
    background-position: right 4px center;
    background-size: 10px;
}

#toolbar select:hover {
    border-color: #a0c3ff;
}

#toolbar input[type="color"] {
    padding: 1px 2px;
    width: 36px;
    border: 1px solid #dadce0;
    vertical-align: middle;
}

#toolbar input[type="number"] {
    width: 45px;
    height: 28px;
    padding: 4px;
    border: 1px solid #dadce0;
    border-radius: 3px;
    font-size: 13px;
    text-align: center;
    -moz-appearance: textfield; /* Remove spinner in Firefox */
}

#toolbar input[type="number"]::-webkit-inner-spin-button,
#toolbar input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

#toolbar input[type="number"]:hover,
#toolbar input[type="number"]:focus {
    border-color: #a0c3ff;
    outline: none;
}

#toolbar label {
    font-size: 13px;
    margin-left: 6px;
    margin-right: 2px;
    color: #5f6368;
    align-self: center;
}

.toolbar-separator {
    width: 1px;
    height: 24px;
    background-color: #dadce0;
    margin: 0 8px;
    align-self: center;
}

#spreadsheetContainer {
    flex: 1;
    overflow: auto;
    background-color: #ffffff;
    padding: 0;
    border-bottom: 1px solid #dadce0;
    position: relative;
}

#formulaBar {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 8px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dadce0;
    height: 32px;
}

#formulaBar .material-icons {
    color: #5f6368;
    font-size: 18px;
}

#formulaInput {
    flex: 1;
    padding: 4px 8px;
    border: 1px solid #dadce0;
    border-radius: 3px;
    font-size: 13px;
    font-family: monospace;
    background-color: #ffffff;
    transition: all 0.15s;
}

#formulaInput:focus {
    border-color: #1a73e8;
    outline: none;
    box-shadow: 0 0 0 1px #1a73e8;
}

.excel-table {
    border-collapse: collapse;
    width: auto;
    font-size: 13px;
    table-layout: fixed;
    background: #ffffff;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.excel-table th, .excel-table td {
    border: 1px solid #e0e0e0;
    padding: 0;
    min-width: 90px;
    height: 28px;
    box-sizing: border-box;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    position: relative;
}

.excel-table th.excel-col-header {
    background-color: #f8f9fa;
    padding: 5px 0;
    text-align: center;
    font-weight: 500;
    position: sticky;
    top: 0;
    z-index: 2;
    border-bottom: 2px solid #dadce0;
    user-select: none;
}

.excel-table td .excel-row-header,
.excel-table th.excel-header-corner {
    background-color: #f8f9fa;
    padding: 5px 0;
    text-align: center;
    font-weight: 500;
    min-width: 45px;
    max-width: 45px;
    position: sticky;
    left: 0;
    z-index: 1;
}

.excel-table th.excel-header-corner {
    z-index: 3;
}

.cell-input {
    width: 100%;
    height: 100%;
    border: none;
    padding: 4px 6px;
    box-sizing: border-box;
    font-family: inherit;
    font-size: inherit;
    outline: none;
    background-color: transparent;
    transition: all 0.15s;
    cursor: default; /* Default cursor when not editable */
}

.cell-input:not([readonly]) {
    cursor: text; /* Text cursor when editable */
    background-color: #ffffff;
    box-shadow: 0 0 0 2px #1a73e8 inset;
}

.cell-input:focus {
    box-shadow: 0 0 0 2px #1a73e8 inset;
    position: relative;
    z-index: 5;
    background-color: #ffffff;
}

.cell-input:hover:not(:focus) {
    background-color: #f8f9fa;
}

/* Style classes */
.cell-input.bold { font-weight: bold; }
.cell-input.italic { font-style: italic; }
.cell-input.underline { text-decoration: underline; }

/* Remove default input styling */
.cell-input {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.cell-input.selected-cell-highlight {
    background-color: rgba(26, 115, 232, 0.1);
}

/* Selected cells */
.excel-table td.selected {
    background-color: rgba(26, 115, 232, 0.1);
    border: 2px solid #1a73e8;
}

/* Format painter cursor */
.format-painter-cursor,
.format-painter-cursor * {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="%231a73e8" d="M7 14c-1.66 0-3 1.34-3 3 0 1.31-1.16 2-2 2 .92 1.22 2.49 2 4 2 2.21 0 4-1.79 4-4 0-1.66-1.34-3-3-3zm13.71-9.37l-1.34-1.34a.996.996 0 0 0-1.41 0L9 12.25 11.75 15l8.96-8.96a.996.996 0 0 0 0-1.41z"/></svg>') 0 24, auto !important;
}

/* History highlight effect for undo/redo visual feedback */
@keyframes historyHighlight {
    0% { background-color: rgba(26, 115, 232, 0.1); }
    50% { background-color: rgba(26, 115, 232, 0.4); }
    100% { background-color: rgba(26, 115, 232, 0.1); }
}

.history-highlight {
    animation: historyHighlight 0.5s ease;
}

/* Modal Dialog */
.modal {
    display: block;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: none;
    overflow: hidden;
    pointer-events: none;
}

.modal-content {
    pointer-events: auto;
    background-color: #fefefe;
    padding: 0;
    border: 1px solid #888;
    width: 80%;
    max-width: 1200px;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.modal-header {
    padding: 10px 16px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 16px;
    max-height: 70vh;
    overflow-y: auto;
}

.close-button {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close-button:hover,
.close-button:focus {
    color: #000;
    text-decoration: none;
}

/* Demo Sections */
.demo-section {
    margin-bottom: 30px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
}

.demo-section h2 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 18px;
}

.demo-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.demo-result {
    min-height: 100px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    background-color: #f8f9fa;
}

/* Form Elements */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.primary-button {
    background-color: #1a73e8;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
}

.primary-button:hover {
    background-color: #1669d9;
}

/* Chart Container */
.chart-wrapper {
    position: relative;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    background-color: white;
    border-radius: 4px;
    overflow: hidden;
    margin: 20px;
    width: 500px;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #ddd;
}

.chart-title {
    font-weight: 500;
    font-size: 14px;
}

.chart-controls {
    display: flex;
    gap: 5px;
}

.chart-control {
    background: none;
    border: none;
    cursor: pointer;
    padding: 2px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-control:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.chart-control .material-icons {
    font-size: 18px;
}

.chart-container {
    padding: 10px;
    background-color: white;
}

/* Chart Dialog */
.chart-dialog {
    max-width: 800px;
}

.chart-options {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.chart-type-selector {
    flex: 1;
}

.chart-type-selector h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
}

.chart-type-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
}

.chart-type-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.chart-type-item:hover {
    background-color: #f8f9fa;
}

.chart-type-item.selected {
    border-color: #1a73e8;
    background-color: rgba(26, 115, 232, 0.05);
}

.chart-icon {
    width: 40px;
    height: 40px;
    margin-bottom: 5px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.bar-icon {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231a73e8"><rect x="3" y="10" width="4" height="10" /><rect x="10" y="5" width="4" height="15" /><rect x="17" y="8" width="4" height="12" /></svg>');
}

.line-icon {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231a73e8"><path d="M3,16 L7,10 L11,14 L16,6 L21,12 L21,16 L3,16 Z" /></svg>');
}

.pie-icon {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231a73e8"><path d="M12,2 C17.52,2 22,6.48 22,12 C22,17.52 17.52,22 12,22 C6.48,22 2,17.52 2,12 C2,6.48 6.48,2 12,2 Z M12,4 C7.58,4 4,7.58 4,12 C4,16.42 7.58,20 12,20 C16.42,20 20,16.42 20,12 C20,7.58 16.42,4 12,4 Z" /><path d="M12,12 L12,4 C16.42,4 20,7.58 20,12 L12,12 Z" /></svg>');
}

.area-icon {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231a73e8"><path d="M3,16 L7,10 L11,14 L16,6 L21,12 L21,16 L3,16 Z" /><path d="M3,16 L3,18 L21,18 L21,16 L3,16 Z" fill-opacity="0.5" /></svg>');
}

.column-icon {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231a73e8"><rect x="3" y="10" width="4" height="10" /><rect x="10" y="5" width="4" height="15" /><rect x="17" y="8" width="4" height="12" /></svg>');
}

.donut-icon {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231a73e8"><path d="M12,2 C17.52,2 22,6.48 22,12 C22,17.52 17.52,22 12,22 C6.48,22 2,17.52 2,12 C2,6.48 6.48,2 12,2 Z M12,4 C7.58,4 4,7.58 4,12 C4,16.42 7.58,20 12,20 C16.42,20 20,16.42 20,12 C20,7.58 16.42,4 12,4 Z" /><circle cx="12" cy="12" r="4" fill="white" /></svg>');
}

.chart-settings {
    flex: 1;
}

.range-input-container {
    display: flex;
    gap: 5px;
}

.icon-button {
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-button:hover {
    background-color: #f1f3f4;
}

.color-palette {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 5px;
}

.color-option {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.2s;
}

.color-option:hover {
    transform: scale(1.1);
}

.color-option.selected {
    border-color: #333;
    transform: scale(1.1);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.secondary-button {
    background-color: #f8f9fa;
    color: #1a73e8;
    border: 1px solid #dadce0;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
}

.secondary-button:hover {
    background-color: #f1f3f4;
}

.preview-message {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #5f6368;
    font-size: 14px;
}

.preview-message.error {
    color: #d93025;
}

/* Range Selection Overlay */
.range-selection-overlay,
.selection-overlay {
    position: fixed;
    background-color: rgba(26, 115, 232, 0.2);
    border: 2px solid #1a73e8;
    pointer-events: none;
    z-index: 1000;
    display: none;
    box-sizing: border-box;
}

#status {
    padding: 4px 8px;
    text-align: left;
    font-weight: normal;
    font-size: 12px;
    background-color: #f8f9fa;
    border-top: 1px solid #dadce0;
    color: #5f6368;
    height: 24px;
    display: flex;
    align-items: center;
}

#status.success { color: #1e8e3e; background-color: #e6f4ea; }
#status.error { color: #d93025; background-color: #fce8e6; }

@media print {
    body { background-color: #fff; padding: 0; margin: 20px; font-size: 9pt; }
    .controls, #toolbar, #status, #formulaBar { display: none !important; }
    #spreadsheetContainer { box-shadow: none; border: none; overflow: visible; padding: 0; }
    .excel-table { width: 100%; table-layout: auto; }
    .excel-table th, .excel-table td { border: 1px solid #333 !important; white-space: normal; overflow: visible; height: auto; }
    .excel-table th.excel-col-header, .excel-table td .excel-row-header, .excel-table th.excel-header-corner { position: static; background-color: #eee !important; }
    .cell-input { border: none !important; padding: 3px 4px; box-shadow: none !important; background-color: transparent !important; color: #000 !important; -webkit-print-color-adjust: exact; color-adjust: exact; }
    .cell-input:focus { box-shadow: none !important; }
}