// js/formula_manager.js

let formulaBarElement = null;
let formulaInput = null;
let activeFormulaCell = null;

export function initializeFormulaBar() {
    formulaBarElement = document.getElementById('formulaBar');
    formulaInput = document.getElementById('formulaInput');
    
    if (formulaInput) {
        formulaInput.addEventListener('input', handleFormulaInput);
        formulaInput.addEventListener('keydown', handleFormulaKeydown);
    }
}

export function updateFormulaBar(cell, element) {
    if (!formulaBarElement || !formulaInput) return;
    
    activeFormulaCell = cell;
    formulaBarElement.style.display = cell ? 'flex' : 'none';
    
    if (cell) {
        const formula = cell.formula();
        formulaInput.value = formula ? '=' + formula : cell.value() || '';
    } else {
        formulaInput.value = '';
    }
}

function handleFormulaInput(event) {
    if (!activeFormulaCell) return;
    
    const value = event.target.value;
    if (value.startsWith('=')) {
        try {
            activeFormulaCell.formula(value.substring(1));
        } catch (e) {
            console.warn('Invalid formula:', e);
            // Keep the invalid formula in the cell for editing
            activeFormulaCell.value(value);
        }
    } else {
        activeFormulaCell.formula(null);
        activeFormulaCell.value(value);
    }
}

function handleFormulaKeydown(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        if (window.updateToolbarWithActiveCellStyles) {
            window.updateToolbarWithActiveCellStyles();
        }
        if (window.fullRenderAndRefresh) {
            window.fullRenderAndRefresh();
        }
    }
}