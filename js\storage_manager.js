// js/storage_manager.js

// Storage Manager for handling IndexedDB operations

class StorageManager {
    constructor() {
        this.dbName = 'SpreadsheetDB';
        this.dbVersion = 2; // Increased version for schema updates
        this.db = null;
        this.dbReady = false;
        this.dbReadyPromise = this.initializeDB();
    }

    // Initialize the IndexedDB database
    async initializeDB() {
        try {
            return new Promise((resolve, reject) => {
                const request = indexedDB.open(this.dbName, this.dbVersion);

                request.onerror = (event) => {
                    console.error('Database error:', event.target.error);
                    reject(event.target.error);
                };

                request.onsuccess = (event) => {
                    this.db = event.target.result;
                    this.dbReady = true;
                    console.log('Database opened successfully');
                    resolve(this.db);
                };

                request.onupgradeneeded = (event) => {
                    const db = event.target.result;
                    const oldVersion = event.oldVersion;

                    console.log(`Upgrading database from version ${oldVersion} to ${this.dbVersion}`);

                    // Create or update object stores
                    if (!db.objectStoreNames.contains('workbooks')) {
                        const workbookStore = db.createObjectStore('workbooks', { keyPath: 'id' });
                        workbookStore.createIndex('name', 'name', { unique: false });
                        workbookStore.createIndex('lastModified', 'lastModified', { unique: false });
                    }

                    if (!db.objectStoreNames.contains('settings')) {
                        db.createObjectStore('settings', { keyPath: 'key' });
                    }

                    if (!db.objectStoreNames.contains('history')) {
                        const historyStore = db.createObjectStore('history', { keyPath: 'id', autoIncrement: true });
                        historyStore.createIndex('workbookId', 'workbookId', { unique: false });
                        historyStore.createIndex('timestamp', 'timestamp', { unique: false });
                    }

                    // Add recentFiles store in version 2
                    if (oldVersion < 2 && !db.objectStoreNames.contains('recentFiles')) {
                        const recentFilesStore = db.createObjectStore('recentFiles', { keyPath: 'id', autoIncrement: true });
                        recentFilesStore.createIndex('path', 'path', { unique: true });
                        recentFilesStore.createIndex('lastOpened', 'lastOpened', { unique: false });
                    }
                };
            });
        } catch (error) {
            console.error('Error initializing database:', error);
            throw error;
        }
    }

    // Ensure database is ready before operations
    async ensureDbReady() {
        if (!this.dbReady) {
            await this.dbReadyPromise;
        }
        return this.db;
    }

    // Save workbook data
    async saveWorkbook(workbook) {
        await this.ensureDbReady();

        return new Promise((resolve, reject) => {
            // Create a transaction that spans multiple object stores
            const transaction = this.db.transaction(['workbooks', 'history'], 'readwrite');

            // Handle transaction-level errors
            transaction.onerror = (event) => {
                console.error('Transaction error:', event.target.error);
                reject(new Error(`Transaction error: ${event.target.error}`));
            };

            transaction.onabort = (event) => {
                console.error('Transaction aborted:', event.target.error);
                reject(new Error(`Transaction aborted: ${event.target.error}`));
            };

            const workbooksStore = transaction.objectStore('workbooks');
            const historyStore = transaction.objectStore('history');

            const workbookData = {
                id: workbook.id || Date.now().toString(),
                name: workbook.name,
                data: workbook.data,
                sheets: workbook.sheets || [],
                activeSheetIndex: workbook.activeSheetIndex || 0,
                lastModified: new Date().toISOString()
            };

            // Save workbook
            const workbookRequest = workbooksStore.put(workbookData);

            workbookRequest.onsuccess = () => {
                // Save to history within the same transaction
                const historyEntry = {
                    workbookId: workbookData.id,
                    data: workbookData.data,
                    sheets: workbookData.sheets || [],
                    activeSheetIndex: workbookData.activeSheetIndex || 0,
                    timestamp: new Date().toISOString()
                };

                historyStore.add(historyEntry);
            };

            // Handle transaction completion
            transaction.oncomplete = () => {
                // Add to recent files in a separate transaction
                if (workbook.path) {
                    this.addRecentFile(workbook.path, workbook.name)
                        .then(() => resolve(workbookData.id))
                        .catch(error => {
                            console.warn('Failed to add to recent files:', error);
                            // Still resolve with workbook ID even if adding to recent files fails
                            resolve(workbookData.id);
                        });
                } else {
                    resolve(workbookData.id);
                }
            };
        });
    }

    // Load workbook data
    async loadWorkbook(id) {
        await this.ensureDbReady();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['workbooks'], 'readonly');

            // Handle transaction-level errors
            transaction.onerror = (event) => {
                console.error('Transaction error:', event.target.error);
                reject(new Error(`Transaction error: ${event.target.error}`));
            };

            const store = transaction.objectStore('workbooks');
            const request = store.get(id);
            let result = null;

            request.onsuccess = () => {
                result = request.result;
            };

            // Use transaction completion to resolve the promise
            transaction.oncomplete = () => {
                resolve(result);
            };
        });
    }

    // List all workbooks
    async listWorkbooks() {
        await this.ensureDbReady();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['workbooks'], 'readonly');
            const store = transaction.objectStore('workbooks');
            const request = store.index('lastModified').openCursor(null, 'prev');

            const workbooks = [];

            request.onsuccess = (event) => {
                const cursor = event.target.result;
                if (cursor) {
                    workbooks.push({
                        id: cursor.value.id,
                        name: cursor.value.name,
                        sheets: cursor.value.sheets || [],
                        activeSheetIndex: cursor.value.activeSheetIndex || 0,
                        lastModified: cursor.value.lastModified
                    });
                    cursor.continue();
                } else {
                    resolve(workbooks);
                }
            };

            request.onerror = () => reject(request.error);
        });
    }

    // Delete workbook
    async deleteWorkbook(id) {
        await this.ensureDbReady();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['workbooks'], 'readwrite');
            const store = transaction.objectStore('workbooks');
            const request = store.delete(id);

            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
        });
    }

    // Save version to history
    async saveToHistory(workbookData) {
        await this.ensureDbReady();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['history'], 'readwrite');
            const store = transaction.objectStore('history');

            const historyEntry = {
                workbookId: workbookData.id,
                data: workbookData.data,
                sheets: workbookData.sheets || [],
                activeSheetIndex: workbookData.activeSheetIndex || 0,
                timestamp: new Date().toISOString()
            };

            const request = store.add(historyEntry);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // Get version history
    async getHistory(workbookId) {
        await this.ensureDbReady();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['history'], 'readonly');
            const store = transaction.objectStore('history');
            const index = store.index('workbookId');
            const request = index.getAll(workbookId);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // Save user settings
    async saveSettings(settings) {
        await this.ensureDbReady();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['settings'], 'readwrite');
            const store = transaction.objectStore('settings');

            const request = store.put({
                key: 'userSettings',
                value: settings
            });

            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
        });
    }

    // Load user settings
    async loadSettings() {
        await this.ensureDbReady();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['settings'], 'readonly');
            const store = transaction.objectStore('settings');
            const request = store.get('userSettings');

            request.onsuccess = () => resolve(request.result?.value || {});
            request.onerror = () => reject(request.error);
        });
    }
    // Add a file to recent files
    async addRecentFile(path, name) {
        await this.ensureDbReady();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['recentFiles'], 'readwrite');

            // Handle transaction-level errors
            transaction.onerror = (event) => {
                console.error('Transaction error:', event.target.error);
                reject(new Error(`Transaction error: ${event.target.error}`));
            };

            transaction.onabort = (event) => {
                console.error('Transaction aborted:', event.target.error);
                reject(new Error(`Transaction aborted: ${event.target.error}`));
            };

            const store = transaction.objectStore('recentFiles');
            let resultId = null;

            // First check if the file already exists
            const index = store.index('path');
            const request = index.get(path);

            request.onsuccess = () => {
                const existingFile = request.result;
                const now = new Date().toISOString();

                if (existingFile) {
                    // Update existing record
                    existingFile.lastOpened = now;
                    existingFile.name = name || existingFile.name;
                    const updateRequest = store.put(existingFile);
                    updateRequest.onsuccess = () => {
                        resultId = existingFile.id;
                    };
                } else {
                    // Add new record
                    const newFile = {
                        path: path,
                        name: name || path.split('/').pop(),
                        lastOpened: now
                    };
                    const addRequest = store.add(newFile);
                    addRequest.onsuccess = (event) => {
                        resultId = event.target.result;
                    };
                }
            };

            // Use transaction completion to resolve the promise
            transaction.oncomplete = () => {
                resolve(resultId);
            };
        });
    }

    // Get recent files - simplified to just return an array of the 3 most recent files
    async getRecentFiles(limit = 3) {
        await this.ensureDbReady();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['recentFiles'], 'readonly');

            // Handle transaction-level errors
            transaction.onerror = (event) => {
                console.error('Transaction error:', event.target.error);
                reject(new Error(`Transaction error: ${event.target.error}`));
            };

            const store = transaction.objectStore('recentFiles');
            const index = store.index('lastOpened');
            let count = 0;
            const recentFiles = [];

            const request = index.openCursor(null, 'prev'); // Sort by most recent first

            request.onsuccess = (event) => {
                const cursor = event.target.result;

                if (cursor && count < limit) {
                    recentFiles.push({
                        id: cursor.value.id,
                        path: cursor.value.path,
                        name: cursor.value.name,
                        lastOpened: cursor.value.lastOpened
                    });
                    count++;
                    cursor.continue();
                } else {
                    // Either no more records or we've reached the limit
                    resolve(recentFiles);
                }
            };

            // Use transaction completion as a fallback
            transaction.oncomplete = () => {
                if (!request.result) {
                    resolve(recentFiles);
                }
            };
        });
    }

    // Remove a file from recent files
    async removeRecentFile(id) {
        await this.ensureDbReady();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['recentFiles'], 'readwrite');
            const store = transaction.objectStore('recentFiles');
            const request = store.delete(id);

            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
        });
    }

    // Clear all recent files
    async clearRecentFiles() {
        await this.ensureDbReady();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['recentFiles'], 'readwrite');
            const store = transaction.objectStore('recentFiles');
            const request = store.clear();

            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
        });
    }
}

// Export a singleton instance
const storageManager = new StorageManager();
export default storageManager;