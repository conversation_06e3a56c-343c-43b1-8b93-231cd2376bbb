// js/menu_manager.js

class MenuManager {
    constructor() {
        this.menuBar = null;
        this.toolbar = null;
        this.contextMenu = null;
        this.initializeMenuBar();
        this.initializeToolbar();
        this.initializeContextMenu();
    }

    initializeMenuBar() {
        this.menuBar = document.querySelector('.menu-bar');
        if (!this.menuBar) {
            console.error('Menu bar not found in the DOM');
            return;
        }

        // Add event listeners and functionality for menu items
        this.setupMenuEventListeners();
    }

    setupMenuEventListeners() {
        // Define all handler methods if they don't exist
        if (!this.handleFileOpen) this.handleFileOpen = function() { console.log('File open handler'); };
        if (!this.handleFileImport) this.handleFileImport = function() { console.log('File import handler'); };
        if (!this.handleFileSave) this.handleFileSave = function() { console.log('File save handler'); };
        if (!this.handlePrint) this.handlePrint = function() { console.log('Print handler'); };
        if (!this.handleUndo) this.handleUndo = function() { console.log('Undo handler'); };
        if (!this.handleRedo) this.handleRedo = function() { console.log('Redo handler'); };
        if (!this.handleCut) this.handleCut = function() { console.log('Cut handler'); };
        if (!this.handleCopy) this.handleCopy = function() { console.log('Copy handler'); };
        if (!this.handlePaste) this.handlePaste = function() { console.log('Paste handler'); };
        if (!this.handleSelectAll) this.handleSelectAll = function() { console.log('Select all handler'); };

        // Bind all handler methods to this instance to prevent 'this' context issues
        this.handleFileOpen = this.handleFileOpen.bind(this);
        this.handleFileImport = this.handleFileImport.bind(this);
        this.handleFileSave = this.handleFileSave.bind(this);
        this.handlePrint = this.handlePrint.bind(this);
        this.handleUndo = this.handleUndo.bind(this);
        this.handleRedo = this.handleRedo.bind(this);
        this.handleCut = this.handleCut.bind(this);
        this.handleCopy = this.handleCopy.bind(this);
        this.handlePaste = this.handlePaste.bind(this);
        this.handleSelectAll = this.handleSelectAll.bind(this);

        // File menu actions
        const openFileBtn = document.getElementById('openFileBtn');
        const importFileBtn = document.getElementById('importFileBtn');
        const saveFileBtn = document.getElementById('saveFileBtn');
        const printBtn = document.getElementById('printBtn');

        if (openFileBtn) openFileBtn.addEventListener('click', this.handleFileOpen);
        if (importFileBtn) importFileBtn.addEventListener('click', this.handleFileImport);
        if (saveFileBtn) saveFileBtn.addEventListener('click', this.handleFileSave);
        if (printBtn) printBtn.addEventListener('click', this.handlePrint);

        // Edit menu actions - Fixed selector syntax
        const undoBtn = document.getElementById('undoBtn');
        const redoBtn = document.getElementById('redoBtn');
        const cutBtn = document.getElementById('cutBtn');
        const copyBtn = document.getElementById('copyBtn');
        const pasteBtn = document.getElementById('pasteBtn');
        const selectAllBtn = document.getElementById('selectAllBtn');
        if (undoBtn) undoBtn.addEventListener('click', this.handleUndo);
        if (redoBtn) redoBtn.addEventListener('click', this.handleRedo);
        if (cutBtn) cutBtn.addEventListener('click', this.handleCut);
        if (copyBtn) copyBtn.addEventListener('click', this.handleCopy);
        if (pasteBtn) pasteBtn.addEventListener('click', this.handlePaste);
        if (selectAllBtn) selectAllBtn.addEventListener('click', this.handleSelectAll);

        // View menu actions - Fixed selector syntax
        const zoomInBtn = document.getElementById('zoomInBtn');
        const zoomOutBtn = document.getElementById('zoomOutBtn');
        const fitScreenBtn = document.getElementById('fitScreenBtn');
        const gridlinesBtn = document.getElementById('gridlinesBtn');
        const headersBtn = document.getElementById('headersBtn');
        // Define view menu handlers if they don't exist
        if (!this.handleZoomIn) this.handleZoomIn = function() { console.log('Zoom in handler'); };
        if (!this.handleZoomOut) this.handleZoomOut = function() { console.log('Zoom out handler'); };
        if (!this.handleFitScreen) this.handleFitScreen = function() { console.log('Fit screen handler'); };
        if (!this.handleToggleGridlines) this.handleToggleGridlines = function() { console.log('Toggle gridlines handler'); };
        if (!this.handleToggleHeaders) this.handleToggleHeaders = function() { console.log('Toggle headers handler'); };

        // Bind view menu handlers
        this.handleZoomIn = this.handleZoomIn.bind(this);
        this.handleZoomOut = this.handleZoomOut.bind(this);
        this.handleFitScreen = this.handleFitScreen.bind(this);
        this.handleToggleGridlines = this.handleToggleGridlines.bind(this);
        this.handleToggleHeaders = this.handleToggleHeaders.bind(this);

        if (zoomInBtn) zoomInBtn.addEventListener('click', this.handleZoomIn);
        if (zoomOutBtn) zoomOutBtn.addEventListener('click', this.handleZoomOut);
        if (fitScreenBtn) fitScreenBtn.addEventListener('click', this.handleFitScreen);
        if (gridlinesBtn) gridlinesBtn.addEventListener('click', this.handleToggleGridlines);
        if (headersBtn) headersBtn.addEventListener('click', this.handleToggleHeaders);

        // Insert menu actions - Fixed selector syntax
        const insertRowBtn = document.getElementById('insertRowBtn');
        const insertColBtn = document.getElementById('insertColBtn');
        const addChartBtn = document.getElementById('addChartBtn');
        const imageBtn = document.getElementById('imageBtn');
        const linkBtn = document.getElementById('linkBtn');
        const commentBtn = document.getElementById('commentBtn');
        const functionBtn = document.getElementById('functionBtn');
        // Define insert menu handlers if they don't exist
        if (!this.handleInsertRow) this.handleInsertRow = function() { console.log('Insert row handler'); };
        if (!this.handleInsertColumn) this.handleInsertColumn = function() { console.log('Insert column handler'); };
        if (!this.handleAddChart) this.handleAddChart = function() { console.log('Add chart handler'); };
        if (!this.handleInsertImage) this.handleInsertImage = function() { console.log('Insert image handler'); };
        if (!this.handleInsertLink) this.handleInsertLink = function() { console.log('Insert link handler'); };
        if (!this.handleAddComment) this.handleAddComment = function() { console.log('Add comment handler'); };
        if (!this.handleInsertFunction) this.handleInsertFunction = function() { console.log('Insert function handler'); };

        // Bind insert menu handlers
        this.handleInsertRow = this.handleInsertRow.bind(this);
        this.handleInsertColumn = this.handleInsertColumn.bind(this);
        this.handleAddChart = this.handleAddChart.bind(this);
        this.handleInsertImage = this.handleInsertImage.bind(this);
        this.handleInsertLink = this.handleInsertLink.bind(this);
        this.handleAddComment = this.handleAddComment.bind(this);
        this.handleInsertFunction = this.handleInsertFunction.bind(this);

        if (insertRowBtn) insertRowBtn.addEventListener('click', this.handleInsertRow);
        if (insertColBtn) insertColBtn.addEventListener('click', this.handleInsertColumn);
        if (addChartBtn) addChartBtn.addEventListener('click', this.handleAddChart);
        if (imageBtn) imageBtn.addEventListener('click', this.handleInsertImage);
        if (linkBtn) linkBtn.addEventListener('click', this.handleInsertLink);
        if (commentBtn) commentBtn.addEventListener('click', this.handleAddComment);
        if (functionBtn) functionBtn.addEventListener('click', this.handleInsertFunction);

        // Format menu actions - Fixed selector syntax
        const fontBtn = document.getElementById('fontBtn');
        const alignBtn = document.getElementById('alignBtn');
        const numberFormatBtn = document.getElementById('numberFormatBtn');
        const bordersBtn = document.getElementById('bordersBtn');
        const fillColorBtn = document.getElementById('fillColorBtn');
        const textColorBtn = document.getElementById('textColorBtn');
        // Define format menu handlers if they don't exist
        if (!this.handleFont) this.handleFont = function() { console.log('Font handler'); };
        if (!this.handleAlignment) this.handleAlignment = function() { console.log('Alignment handler'); };
        if (!this.handleNumberFormat) this.handleNumberFormat = function() { console.log('Number format handler'); };
        if (!this.handleBorders) this.handleBorders = function() { console.log('Borders handler'); };
        if (!this.handleFillColor) this.handleFillColor = function() { console.log('Fill color handler'); };
        if (!this.handleTextColor) this.handleTextColor = function() { console.log('Text color handler'); };

        // Bind format menu handlers
        this.handleFont = this.handleFont.bind(this);
        this.handleAlignment = this.handleAlignment.bind(this);
        this.handleNumberFormat = this.handleNumberFormat.bind(this);
        this.handleBorders = this.handleBorders.bind(this);
        this.handleFillColor = this.handleFillColor.bind(this);
        this.handleTextColor = this.handleTextColor.bind(this);

        if (fontBtn) fontBtn.addEventListener('click', this.handleFont);
        if (alignBtn) alignBtn.addEventListener('click', this.handleAlignment);
        if (numberFormatBtn) numberFormatBtn.addEventListener('click', this.handleNumberFormat);
        if (bordersBtn) bordersBtn.addEventListener('click', this.handleBorders);
        if (fillColorBtn) fillColorBtn.addEventListener('click', this.handleFillColor);
        if (textColorBtn) textColorBtn.addEventListener('click', this.handleTextColor);

        // Data menu actions - Fixed selector syntax
        const sortBtn = document.getElementById('sortBtn');
        const filterBtn = document.getElementById('filterBtn');
        const validateBtn = document.getElementById('validateBtn');
        const groupBtn = document.getElementById('groupBtn');
        const ungroupBtn = document.getElementById('ungroupBtn');
        const subtotalBtn = document.getElementById('subtotalBtn');
        // Define data menu handlers if they don't exist
        if (!this.handleSort) this.handleSort = function() { console.log('Sort handler'); };
        if (!this.handleFilter) this.handleFilter = function() { console.log('Filter handler'); };
        if (!this.handleValidate) this.handleValidate = function() { console.log('Validate handler'); };
        if (!this.handleGroup) this.handleGroup = function() { console.log('Group handler'); };
        if (!this.handleUngroup) this.handleUngroup = function() { console.log('Ungroup handler'); };
        if (!this.handleSubtotal) this.handleSubtotal = function() { console.log('Subtotal handler'); };

        // Bind data menu handlers
        this.handleSort = this.handleSort.bind(this);
        this.handleFilter = this.handleFilter.bind(this);
        this.handleValidate = this.handleValidate.bind(this);
        this.handleGroup = this.handleGroup.bind(this);
        this.handleUngroup = this.handleUngroup.bind(this);
        this.handleSubtotal = this.handleSubtotal.bind(this);

        if (sortBtn) sortBtn.addEventListener('click', this.handleSort);
        if (filterBtn) filterBtn.addEventListener('click', this.handleFilter);
        if (validateBtn) validateBtn.addEventListener('click', this.handleValidate);
        if (groupBtn) groupBtn.addEventListener('click', this.handleGroup);
        if (ungroupBtn) ungroupBtn.addEventListener('click', this.handleUngroup);
        if (subtotalBtn) subtotalBtn.addEventListener('click', this.handleSubtotal);

        // Tools menu actions - Fixed selector syntax
        const scriptEditorBtn = document.getElementById('scriptEditorBtn');
        const chartBuilderBtn = document.getElementById('chartBuilderBtn');
        const pivotTableBtn = document.getElementById('pivotTableBtn');
        const dataValidationBtn = document.getElementById('dataValidationBtn');
        const auditLogBtn = document.getElementById('auditLogBtn');
        // Define tools menu handlers if they don't exist
        if (!this.handleScriptEditor) this.handleScriptEditor = function() { console.log('Script editor handler'); };
        if (!this.handleChartBuilder) this.handleChartBuilder = function() { console.log('Chart builder handler'); };
        if (!this.handlePivotTable) this.handlePivotTable = function() { console.log('Pivot table handler'); };
        if (!this.handleDataValidation) this.handleDataValidation = function() { console.log('Data validation handler'); };
        if (!this.handleAuditLog) this.handleAuditLog = function() { console.log('Audit log handler'); };

        // Bind tools menu handlers
        this.handleScriptEditor = this.handleScriptEditor.bind(this);
        this.handleChartBuilder = this.handleChartBuilder.bind(this);
        this.handlePivotTable = this.handlePivotTable.bind(this);
        this.handleDataValidation = this.handleDataValidation.bind(this);
        this.handleAuditLog = this.handleAuditLog.bind(this);

        if (scriptEditorBtn) scriptEditorBtn.addEventListener('click', this.handleScriptEditor);
        if (chartBuilderBtn) chartBuilderBtn.addEventListener('click', this.handleChartBuilder);
        if (pivotTableBtn) pivotTableBtn.addEventListener('click', this.handlePivotTable);
        if (dataValidationBtn) dataValidationBtn.addEventListener('click', this.handleDataValidation);
        if (auditLogBtn) auditLogBtn.addEventListener('click', this.handleAuditLog);

        // Add-ons menu actions - Fixed selector syntax
        const acumaticaConnectorBtn = document.getElementById('acumaticaConnectorBtn');
        const mondayBtn = document.getElementById('mondayBtn');
        const odataBtn = document.getElementById('odataBtn');
        const pluginManagerBtn = document.getElementById('pluginManagerBtn');

        // Define add-ons menu handlers if they don't exist
        if (!this.handleAcumaticaConnector) {
            this.handleAcumaticaConnector = function() {
                console.log('Acumatica connector handler');

                // Try to dynamically import the module if it's not available
                if (!window.acumaticaConnector) {
                    console.log('Acumatica connector not found, attempting to load it dynamically');

                    import('./acumatica-connector.js')
                        .then(module => {
                            console.log('Successfully loaded acumatica-connector.js');
                            window.acumaticaConnector = module.acumaticaConnector;

                            if (window.acumaticaConnector && typeof window.acumaticaConnector.togglePanel === 'function') {
                                window.acumaticaConnector.togglePanel();
                            } else {
                                console.error('Acumatica connector module loaded but togglePanel function not found');
                            }
                        })
                        .catch(error => {
                            console.error('Failed to load acumatica-connector.js:', error);
                            alert('Could not load the Acumatica connector. Please try again later.');
                        });
                } else if (typeof window.acumaticaConnector.togglePanel === 'function') {
                    // Module is already loaded, just call the function
                    window.acumaticaConnector.togglePanel();
                } else {
                    console.error('Acumatica connector found but togglePanel function not available');
                    alert('Acumatica connector is not properly initialized');
                }
            };
        }
        if (!this.handleMondayConnector) this.handleMondayConnector = function() { console.log('Monday connector handler'); };
        if (!this.handleODataConnection) this.handleODataConnection = function() { console.log('OData connection handler'); };
        if (!this.handlePluginManager) this.handlePluginManager = function() { console.log('Plugin manager handler'); };

        // Bind add-ons menu handlers
        this.handleAcumaticaConnector = this.handleAcumaticaConnector.bind(this);
        this.handleMondayConnector = this.handleMondayConnector.bind(this);
        this.handleODataConnection = this.handleODataConnection.bind(this);
        this.handlePluginManager = this.handlePluginManager.bind(this);

        if (acumaticaConnectorBtn) acumaticaConnectorBtn.addEventListener('click', this.handleAcumaticaConnector);
        if (mondayBtn) mondayBtn.addEventListener('click', this.handleMondayConnector);
        if (odataBtn) odataBtn.addEventListener('click', this.handleODataConnection);
        if (pluginManagerBtn) pluginManagerBtn.addEventListener('click', this.handlePluginManager);

        // Help menu actions - Fixed selector syntax
        const documentationBtn = document.getElementById('documentationBtn');
        const keyboardShortcutsBtn = document.getElementById('keyboardShortcutsBtn');
        const aboutBtn = document.getElementById('aboutBtn');
        const supportBtn = document.getElementById('supportBtn');
        // Define help menu handlers if they don't exist
        if (!this.handleDocumentation) this.handleDocumentation = function() { console.log('Documentation handler'); };
        if (!this.handleKeyboardShortcuts) this.handleKeyboardShortcuts = function() { console.log('Keyboard shortcuts handler'); };
        if (!this.handleAbout) this.handleAbout = function() { console.log('About handler'); };
        if (!this.handleSupport) this.handleSupport = function() { console.log('Support handler'); };

        // Bind help menu handlers
        this.handleDocumentation = this.handleDocumentation.bind(this);
        this.handleKeyboardShortcuts = this.handleKeyboardShortcuts.bind(this);
        this.handleAbout = this.handleAbout.bind(this);
        this.handleSupport = this.handleSupport.bind(this);

        if (documentationBtn) documentationBtn.addEventListener('click', this.handleDocumentation);
        if (keyboardShortcutsBtn) keyboardShortcutsBtn.addEventListener('click', this.handleKeyboardShortcuts);
        if (aboutBtn) aboutBtn.addEventListener('click', this.handleAbout);
        if (supportBtn) supportBtn.addEventListener('click', this.handleSupport);

        // Toolbar actions (ensure all toolbar buttons are wired)
        const toolbarButtons = [
            { id: 'boldBtn', handler: this.handleBold.bind(this) },
            { id: 'italicBtn', handler: this.handleItalic.bind(this) },
            { id: 'underlineBtn', handler: this.handleUnderline.bind(this) },
            { id: 'decreaseFontSizeBtn', handler: this.handleDecreaseFontSize.bind(this) },
            { id: 'increaseFontSizeBtn', handler: this.handleIncreaseFontSize.bind(this) },
            { id: 'fontColorPicker', handler: this.handleFontColor.bind(this) },
            { id: 'bgColorPicker', handler: this.handleBgColor.bind(this) },
            { id: 'alignLeftBtn', handler: this.handleAlignLeft.bind(this) },
            { id: 'alignCenterBtn', handler: this.handleAlignCenter.bind(this) },
            { id: 'alignRightBtn', handler: this.handleAlignRight.bind(this) },
            { id: 'mergeBtn', handler: this.handleMerge.bind(this) },
            { id: 'unmergeBtn', handler: this.handleUnmerge.bind(this) },
            { id: 'insertRowAboveBtn', handler: this.handleInsertRowAbove.bind(this) },
            { id: 'insertRowBelowBtn', handler: this.handleInsertRowBelow.bind(this) },
            { id: 'deleteSelectedRowsBtn', handler: this.handleDeleteSelectedRows.bind(this) },
            { id: 'insertColLeftBtn', handler: this.handleInsertColumnLeft.bind(this) },
            { id: 'insertColRightBtn', handler: this.handleInsertColumnRight.bind(this) },
            { id: 'deleteSelectedColsBtn', handler: this.handleDeleteSelectedColumns.bind(this) },
            { id: 'sortAscBtn', handler: this.handleSortAsc.bind(this) },
            { id: 'filterBtn', handler: this.handleFilter.bind(this) },
            { id: 'formulaBtn', handler: this.handleInsertFormula.bind(this) },
            { id: 'printSheetBtn', handler: this.handlePrint.bind(this) }
        ];
        toolbarButtons.forEach(btn => {
            const el = document.getElementById(btn.id);
            if (el) el.addEventListener('click', btn.handler);
        });
    }

    handleFileOpen() {
        console.log('Opening file...');
        // Implement file opening logic
    }

    handleFileImport() {
        console.log('Importing file...');
        // Implement file import logic
    }

    handleFileSave() {
        console.log('Saving file...');
        // Implement file save logic
    }

    handlePrint() {
        console.log('Printing...');
        // Implement print logic
    }

    handleInsertColumnLeft() {
        console.log('Inserting column to the left...');
        // Implement insert column left logic
    }

    handleInsertColumnRight() {
        console.log('Inserting column to the right...');
        // Implement insert column right logic
    }

    handleDeleteSelectedColumns() {
        console.log('Deleting selected columns...');
        // Implement delete selected columns logic
    }

    initializeToolbar() {
        const toolbarItems = [
            // Column manipulation group
            { icon: 'table_rows', tooltip: 'Insert Column Left', id: 'insertColLeftBtn' },
            { icon: 'table_rows', tooltip: 'Insert Column Right', id: 'insertColRightBtn' },
            { icon: 'delete_sweep', tooltip: 'Delete Selected Column(s)', id: 'deleteSelectedColsBtn' },
            { type: 'separator' },
            { icon: 'undo', tooltip: 'Undo' },
            { icon: 'redo', tooltip: 'Redo' },
            { type: 'separator' },
            { icon: 'save', tooltip: 'Save' },
            { icon: 'print', tooltip: 'Print' },
            { type: 'separator' },
            { icon: 'zoom_in', tooltip: 'Zoom In' },
            { icon: 'zoom_out', tooltip: 'Zoom Out' },
            { type: 'separator' },
            { icon: 'content_cut', tooltip: 'Cut' },
            { icon: 'content_copy', tooltip: 'Copy' },
            { icon: 'content_paste', tooltip: 'Paste' },
            { type: 'separator' },
            { icon: 'format_bold', tooltip: 'Bold' },
            { icon: 'format_italic', tooltip: 'Italic' },
            { icon: 'format_underlined', tooltip: 'Underline' },
            { type: 'separator' },
            { type: 'select', id: 'fontSizeSelect', options: ['8', '9', '10', '11', '12', '14', '16', '18', '20', '22', '24', '26', '28', '36', '48', '72'] },
            { type: 'select', id: 'fontFamilySelect', options: ['Arial', 'Calibri', 'Times New Roman', 'Verdana', 'Helvetica'] },
            { type: 'separator' },
            { icon: 'format_color_text', tooltip: 'Text Color' },
            { icon: 'format_color_fill', tooltip: 'Fill Color' },
            { type: 'separator' },
            { icon: 'border_all', tooltip: 'Borders' },
            { type: 'separator' },
            { icon: 'format_align_left', tooltip: 'Align Left' },
            { icon: 'format_align_center', tooltip: 'Align Center' },
            { icon: 'format_align_right', tooltip: 'Align Right' },
            { type: 'separator' },
            { icon: 'merge_type', tooltip: 'Merge Cells' },
            { icon: 'wrap_text', tooltip: 'Wrap Text' },
            { type: 'separator' },
            { icon: 'link', tooltip: 'Insert Link' },
            { icon: 'image', tooltip: 'Insert Image' },
            { type: 'separator' },
            { icon: 'view_column', tooltip: 'Freeze Columns' },
            { icon: 'filter_list', tooltip: 'Toggle Filter' },
            { icon: 'format_paint', tooltip: 'Conditional Formatting' },
            { type: 'separator' },
            { type: 'select', id: 'numberFormatSelect', options: ['General', 'Number', 'Currency', 'Percentage', 'Date', 'Time'] }
        ];

        this.toolbar = document.getElementById('toolbar');
        this.toolbar.innerHTML = '';

        toolbarItems.forEach(item => {
            if (item.type === 'separator') {
                const separator = document.createElement('span');
                separator.className = 'toolbar-separator';
                this.toolbar.appendChild(separator);
            } else if (item.type === 'select') {
                const select = document.createElement('select');
                select.id = item.id;
                select.className = 'toolbar-select';

                item.options.forEach(option => {
                    const optionElement = document.createElement('option');
                    optionElement.value = option;
                    optionElement.textContent = option;
                    select.appendChild(optionElement);
                });

                this.toolbar.appendChild(select);
            } else {
                const button = document.createElement('button');
                button.className = 'toolbar-button';
                button.title = item.tooltip;

                const icon = document.createElement('span');
                icon.className = 'material-icons';
                icon.textContent = item.icon;

                button.appendChild(icon);
                this.toolbar.appendChild(button);
            }
        });

        this.toolbar.style.display = 'flex';
    }

    initializeContextMenu() {
        const contextMenuItems = [
            'Cut',
            'Copy',
            'Paste',
            'separator',
            'Insert Row Above',
            'Insert Row Below',
            'Insert Column Left',
            'Insert Column Right',
            'separator',
            'Delete Row',
            'Delete Column',
            'Clear Contents',
            'separator',
            'Add Comment',
            'Format Cell',
            'Protect Range',
            'Resize Row/Column'
        ];

        this.contextMenu = document.createElement('div');
        this.contextMenu.className = 'context-menu';
        this.contextMenu.style.display = 'none';

        contextMenuItems.forEach(item => {
            if (item === 'separator') {
                const separator = document.createElement('div');
                separator.className = 'context-menu-separator';
                this.contextMenu.appendChild(separator);
            } else {
                const menuItem = document.createElement('div');
                menuItem.className = 'context-menu-item';
                menuItem.textContent = item;
                this.contextMenu.appendChild(menuItem);
            }
        });

        document.body.appendChild(this.contextMenu);

        // Handle right-click event
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.contextMenu.style.display = 'block';
            this.contextMenu.style.left = `${e.pageX}px`;
            this.contextMenu.style.top = `${e.pageY}px`;
        });

        // Hide context menu on click outside
        document.addEventListener('click', () => {
            this.contextMenu.style.display = 'none';
        });
    }
}

// Export the class instead of a singleton instance
export default MenuManager;