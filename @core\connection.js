// @core/connection.js

/**
 * Connection Manager for external services
 * Handles connections to Acumatica, Monday.com, and other services
 */
export class ConnectionManager {
    constructor() {
        this.initialized = false;
        this.connectionStatus = {
            acumatica: {
                isConnected: false,
                instance: null,
                username: null,
                sessionId: null,
                lastConnected: null,
                expiresAt: null
            },
            monday: {
                isConnected: false,
                apiKey: null,
                lastConnected: null
            }
        };
    }

    /**
     * Initialize the connection manager
     * Loads connection status from Chrome storage
     */
    async init() {
        if (this.initialized) {
            console.log('Connection manager already initialized');
            return;
        }

        try {
            console.log('Initializing connection manager...');
            
            // Load connection status from Chrome storage
            await this.loadConnectionStatus();
            
            // Check if Acumatica session is expired
            if (this.connectionStatus.acumatica.isConnected) {
                const now = new Date();
                const expiresAt = new Date(this.connectionStatus.acumatica.expiresAt);
                
                if (now > expiresAt) {
                    console.log('Acumatica session expired, disconnecting');
                    await this.disconnectFromAcumatica();
                } else {
                    console.log('Acumatica session valid until', expiresAt);
                }
            }
            
            this.initialized = true;
            console.log('Connection manager initialized successfully');
            return true;
        } catch (error) {
            console.error('Failed to initialize connection manager:', error);
            return false;
        }
    }

    /**
     * Load connection status from Chrome storage
     */
    async loadConnectionStatus() {
        return new Promise((resolve, reject) => {
            try {
                // Check if chrome.storage is available
                if (!chrome || !chrome.storage || !chrome.storage.local) {
                    console.warn('Chrome storage not available, using memory storage');
                    resolve();
                    return;
                }
                
                chrome.storage.local.get(['connectionStatus'], (result) => {
                    if (chrome.runtime.lastError) {
                        console.error('Error loading connection status:', chrome.runtime.lastError);
                        reject(chrome.runtime.lastError);
                        return;
                    }
                    
                    if (result.connectionStatus) {
                        console.log('Loaded connection status from Chrome storage');
                        this.connectionStatus = result.connectionStatus;
                    } else {
                        console.log('No connection status found in Chrome storage');
                    }
                    
                    resolve();
                });
            } catch (error) {
                console.error('Error in loadConnectionStatus:', error);
                reject(error);
            }
        });
    }

    /**
     * Save connection status to Chrome storage
     */
    async saveConnectionStatus() {
        return new Promise((resolve, reject) => {
            try {
                // Check if chrome.storage is available
                if (!chrome || !chrome.storage || !chrome.storage.local) {
                    console.warn('Chrome storage not available, using memory storage');
                    resolve();
                    return;
                }
                
                chrome.storage.local.set({ connectionStatus: this.connectionStatus }, () => {
                    if (chrome.runtime.lastError) {
                        console.error('Error saving connection status:', chrome.runtime.lastError);
                        reject(chrome.runtime.lastError);
                        return;
                    }
                    
                    console.log('Saved connection status to Chrome storage');
                    resolve();
                });
            } catch (error) {
                console.error('Error in saveConnectionStatus:', error);
                reject(error);
            }
        });
    }

    /**
     * Get the current connection status
     */
    getConnectionStatus() {
        return this.connectionStatus;
    }

    /**
     * Extract cookies from a response
     * @param {Response} response - The response object
     * @returns {Promise<Object>} - The cookies as an object
     */
    async extractCookiesFromResponse(response) {
        try {
            // Check if chrome.cookies is available
            if (chrome && chrome.cookies) {
                // Get the domain from the URL
                const url = response.url;
                const domain = new URL(url).hostname;
                
                // Get all cookies for the domain
                return new Promise((resolve) => {
                    chrome.cookies.getAll({ domain }, (cookies) => {
                        const cookieObj = {};
                        cookies.forEach(cookie => {
                            cookieObj[cookie.name] = cookie.value;
                        });
                        resolve(cookieObj);
                    });
                });
            } else {
                // Fallback: try to parse Set-Cookie headers
                const setCookieHeaders = response.headers.getAll('Set-Cookie');
                if (setCookieHeaders && setCookieHeaders.length > 0) {
                    const cookieObj = {};
                    setCookieHeaders.forEach(cookieStr => {
                        const parts = cookieStr.split(';')[0].split('=');
                        if (parts.length === 2) {
                            cookieObj[parts[0].trim()] = parts[1].trim();
                        }
                    });
                    return cookieObj;
                }
                
                console.warn('Could not extract cookies from response');
                return {};
            }
        } catch (error) {
            console.error('Error extracting cookies:', error);
            return {};
        }
    }

    /**
     * Save cookies to Chrome storage
     * @param {string} domain - The domain for the cookies
     * @param {Object} cookies - The cookies to save
     */
    async saveCookiesToStorage(domain, cookies) {
        try {
            // Check if chrome.storage is available
            if (!chrome || !chrome.storage || !chrome.storage.local) {
                console.warn('Chrome storage not available, cannot save cookies');
                return;
            }
            
            // Save cookies to Chrome storage
            const key = `cookies_${domain.replace(/[^a-zA-Z0-9]/g, '_')}`;
            
            return new Promise((resolve, reject) => {
                chrome.storage.local.set({ [key]: cookies }, () => {
                    if (chrome.runtime.lastError) {
                        console.error('Error saving cookies:', chrome.runtime.lastError);
                        reject(chrome.runtime.lastError);
                        return;
                    }
                    
                    console.log('Saved cookies to Chrome storage');
                    resolve();
                });
            });
        } catch (error) {
            console.error('Error saving cookies:', error);
        }
    }

    /**
     * Connect to Acumatica
     * @param {string} instance - Acumatica instance URL
     * @param {string} username - Username
     * @param {string} password - Password
     * @param {string} company - Company name
     * @returns {Promise<{success: boolean, error: string}>} - Result of the connection attempt
     */
    async connectToAcumatica(instance, username, password, company) {
        try {
            console.log(`Connecting to Acumatica: ${instance} as ${username}`);
            
            // Create SOAP envelope for login
            const soapEnvelope = `
                <soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
                    <soap:Body>
                        <Login xmlns="http://www.acumatica.com/generic/">
                            <name>${username}</name>
                            <password>${password}</password>
                            <company>${company}</company>
                        </Login>
                    </soap:Body>
                </soap:Envelope>
            `;
            
            console.log('Sending SOAP request to Acumatica...');
            
            // Make SOAP request
            const response = await fetch(`${instance}/Soap/Default.asmx`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'text/xml',
                    'SOAPAction': 'http://www.acumatica.com/generic/login'
                },
                body: soapEnvelope,
                credentials: 'include' // Include cookies
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
            }
            
            console.log('Received response from Acumatica');
            
            // Get cookies from the response
            const cookies = await this.extractCookiesFromResponse(response);
            console.log('Extracted cookies:', cookies);
            
            const responseText = await response.text();
            console.log('Response text:', responseText);
            
            // Save cookies to Chrome storage
            await this.saveCookiesToStorage(instance, cookies);
            
            // Parse session ID from cookies or response
            let sessionId = '';
            if (cookies && cookies.ASP_NET_SessionId) {
                sessionId = cookies.ASP_NET_SessionId;
            } else {
                // Try to parse from response if not in cookies
                const sessionIdMatch = responseText.match(/<LoginResult>(.*?)<\/LoginResult>/);
                if (sessionIdMatch && sessionIdMatch[1]) {
                    sessionId = sessionIdMatch[1];
                } else {
                    console.warn('Could not find session ID in response, using a placeholder');
                    sessionId = 'session-' + new Date().getTime(); // Use timestamp as fallback
                }
            }
            
            // Set connection status
            const now = new Date();
            const expiresAt = new Date(now.getTime() + 4 * 60 * 60 * 1000); // 4 hours from now
            
            this.connectionStatus.acumatica = {
                isConnected: true,
                instance,
                username,
                sessionId,
                lastConnected: now.toISOString(),
                expiresAt: expiresAt.toISOString()
            };
            
            // Save connection status
            await this.saveConnectionStatus();
            
            console.log('Successfully connected to Acumatica');
            return { success: true };
        } catch (error) {
            console.error('Failed to connect to Acumatica:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Disconnect from Acumatica
     * @returns {Promise<{success: boolean, error: string}>} - Result of the disconnection attempt
     */
    async disconnectFromAcumatica() {
        try {
            console.log('Disconnecting from Acumatica');
            
            const { instance, sessionId } = this.connectionStatus.acumatica;
            
            if (!instance || !sessionId) {
                console.log('No active Acumatica connection to disconnect');
                
                // Reset connection status
                this.connectionStatus.acumatica = {
                    isConnected: false,
                    instance: null,
                    username: null,
                    sessionId: null,
                    lastConnected: null,
                    expiresAt: null
                };
                
                // Save connection status
                await this.saveConnectionStatus();
                
                return { success: true };
            }
            
            // Create SOAP envelope for logout
            const soapEnvelope = `
                <soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
                    <soap:Body>
                        <Logout xmlns="http://www.acumatica.com/generic/"/>
                    </soap:Body>
                </soap:Envelope>
            `;
            
            // Make SOAP request
            const response = await fetch(`${instance}/Soap/Default.asmx`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'text/xml',
                    'SOAPAction': 'http://www.acumatica.com/generic/logout'
                },
                body: soapEnvelope,
                credentials: 'include' // Include cookies
            });
            
            // Reset connection status
            this.connectionStatus.acumatica = {
                isConnected: false,
                instance: null,
                username: null,
                sessionId: null,
                lastConnected: null,
                expiresAt: null
            };
            
            // Save connection status
            await this.saveConnectionStatus();
            
            console.log('Successfully disconnected from Acumatica');
            return { success: true };
        } catch (error) {
            console.error('Failed to disconnect from Acumatica:', error);
            
            // Reset connection status anyway
            this.connectionStatus.acumatica = {
                isConnected: false,
                instance: null,
                username: null,
                sessionId: null,
                lastConnected: null,
                expiresAt: null
            };
            
            // Save connection status
            await this.saveConnectionStatus();
            
            return { success: false, error: error.message };
        }
    }

    /**
     * Connect to Monday.com
     * @param {string} apiKey - Monday.com API key
     * @returns {Promise<{success: boolean, error: string}>} - Result of the connection attempt
     */
    async connectToMonday(apiKey) {
        try {
            console.log('Connecting to Monday.com');
            
            // Validate API key with a simple query
            const response = await fetch('https://api.monday.com/v2', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': apiKey
                },
                body: JSON.stringify({
                    query: '{ me { name } }'
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            if (data.errors) {
                throw new Error(data.errors[0].message);
            }
            
            // Set connection status
            const now = new Date();
            
            this.connectionStatus.monday = {
                isConnected: true,
                apiKey,
                lastConnected: now.toISOString()
            };
            
            // Save connection status
            await this.saveConnectionStatus();
            
            console.log('Successfully connected to Monday.com');
            return { success: true };
        } catch (error) {
            console.error('Failed to connect to Monday.com:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Disconnect from Monday.com
     * @returns {Promise<{success: boolean, error: string}>} - Result of the disconnection attempt
     */
    async disconnectFromMonday() {
        try {
            console.log('Disconnecting from Monday.com');
            
            // Reset connection status
            this.connectionStatus.monday = {
                isConnected: false,
                apiKey: null,
                lastConnected: null
            };
            
            // Save connection status
            await this.saveConnectionStatus();
            
            console.log('Successfully disconnected from Monday.com');
            return { success: true };
        } catch (error) {
            console.error('Failed to disconnect from Monday.com:', error);
            return { success: false, error: error.message };
        }
    }
}

// Create and export singleton instance
export const connectionManager = new ConnectionManager();
