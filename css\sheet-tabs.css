/* Sheet Tabs Styles */
.sheet-tabs-container {
    display: flex;
    background-color: #f1f3f4;
    border-top: 1px solid #dadce0;
    height: 36px;
    overflow-x: auto;
    user-select: none;
    position: relative;
    align-items: center;
}

.sheet-tabs-container::-webkit-scrollbar {
    height: 4px;
}

.sheet-tabs-container::-webkit-scrollbar-thumb {
    background-color: #dadce0;
    border-radius: 4px;
}

.sheet-tabs-container::-webkit-scrollbar-track {
    background-color: transparent;
}

.sheet-tab {
    display: flex;
    align-items: center;
    height: 28px;
    padding: 0 12px;
    margin: 4px 2px 4px 0;
    background-color: #e8eaed;
    border-radius: 4px;
    font-size: 13px;
    color: #5f6368;
    cursor: pointer;
    white-space: nowrap;
    transition: background-color 0.15s;
    position: relative;
}

.sheet-tab:first-child {
    margin-left: 4px;
}

.sheet-tab:hover {
    background-color: #dadce0;
}

.sheet-tab.active {
    background-color: #fff;
    color: #1a73e8;
    font-weight: 500;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.sheet-tab .rename-input {
    background: transparent;
    border: none;
    outline: none;
    font-size: 13px;
    color: #1a73e8;
    font-weight: 500;
    width: 100%;
    padding: 0;
    margin: 0;
}

.sheet-tab .close-btn {
    margin-left: 6px;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 14px;
    opacity: 0;
    transition: opacity 0.15s;
}

.sheet-tab:hover .close-btn {
    opacity: 0.7;
}

.sheet-tab .close-btn:hover {
    background-color: rgba(0, 0, 0, 0.1);
    opacity: 1;
}

.add-sheet-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    margin: 4px;
    background-color: transparent;
    border-radius: 50%;
    color: #5f6368;
    cursor: pointer;
    transition: background-color 0.15s;
}

.add-sheet-btn:hover {
    background-color: #dadce0;
}

.sheet-tab-menu {
    position: absolute;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    padding: 8px 0;
    z-index: 1000;
    min-width: 180px;
}

.sheet-tab-menu-item {
    padding: 8px 16px;
    font-size: 13px;
    color: #3c4043;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.sheet-tab-menu-item:hover {
    background-color: #f1f3f4;
}

.sheet-tab-menu-item .material-icons {
    font-size: 18px;
    margin-right: 8px;
    color: #5f6368;
}
