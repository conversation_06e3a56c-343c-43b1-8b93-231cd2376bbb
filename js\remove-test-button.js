/**
 * <PERSON><PERSON>t to remove the test welcome modal button
 */

// Execute immediately
(function() {
    console.log('Checking for test button to remove...');
    
    // Find all buttons that match the test button's characteristics
    const testButtons = document.querySelectorAll('button[style*="position: fixed"][style*="bottom: 10px"][style*="right: 10px"][style*="z-index: 9999"]');
    
    // Remove each matching button
    testButtons.forEach(button => {
        console.log('Found test button, removing it:', button);
        if (button.parentNode) {
            button.parentNode.removeChild(button);
        }
    });
    
    // Also check for buttons with text content "Test Welcome Modal"
    document.querySelectorAll('button').forEach(button => {
        if (button.textContent.includes('Test Welcome Modal')) {
            console.log('Found test button by text content, removing it:', button);
            if (button.parentNode) {
                button.parentNode.removeChild(button);
            }
        }
    });
})();
