/**
 * Application Initialization Script
 * This script initializes the application and loads all required modules
 */

// Define global variables
window.workbook = null;
window.currentSheet = null;
window.activeCell = null;
window.selectedRange = null;
window.isEditing = false;

// Initialize the application when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing application...');

    // Load modules
    loadModules();

    // Initialize UI components
    initializeUI();

    // Set up event listeners
    setupEventListeners();

    console.log('Application initialized successfully');
});

// Load all required modules
function loadModules() {
    try {
        // Import main modules
        import('./main_script.js')
            .then(module => {
                window.mainModule = module;
                console.log('Main script loaded successfully');

                // Expose main functions globally for direct access
                if (typeof module.createNewWorkbook === 'function') {
                    window.createNewWorkbook = module.createNewWorkbook;
                }
                if (typeof module.openFile === 'function') {
                    window.handleFileSelect = module.openFile;
                }
                if (typeof module.saveFile === 'function') {
                    window.handleFileSave = module.saveFile;
                }

                // Initialize the main module
                if (typeof module.init === 'function') {
                    module.init();
                }
            })
            .catch(error => {
                console.error('Failed to load main script:', error);
            });

        // Import menu manager and initialize it
        import('./menu_manager.js')
            .then(module => {
                // Store the MenuManager class
                const MenuManager = module.default;

                // Create an instance of MenuManager
                try {
                    // Wait for DOM to be fully loaded before initializing MenuManager
                    if (document.readyState === 'complete') {
                        window.menuManager = new MenuManager();
                        console.log('Menu manager initialized successfully');
                    } else {
                        // If DOM is not ready, wait for it
                        window.addEventListener('DOMContentLoaded', () => {
                            window.menuManager = new MenuManager();
                            console.log('Menu manager initialized successfully (delayed)');
                        });
                    }
                } catch (error) {
                    console.error('Error initializing menu manager:', error);
                }
            })
            .catch(error => {
                console.error('Failed to load menu manager:', error);
            });

        // Import improved chart manager
        import('./improved-chart-manager.js')
            .then(module => {
                window.ChartManager = module.default;
                console.log('Improved chart manager loaded successfully');

                // Initialize the chart manager if workbook and sheet are available
                if (window.workbook && window.currentSheet && typeof window.ChartManager.init === 'function') {
                    window.ChartManager.init(window.workbook, window.currentSheet);
                    window.chartManagerInitialized = true;
                    console.log('Improved chart manager initialized with current workbook and sheet');
                }
            })
            .catch(error => {
                console.error('Failed to load improved chart manager:', error);

                // Fallback to original chart manager if improved one fails
                console.log('Falling back to original chart manager');
                import('./chart-manager.js')
                    .then(fallbackModule => {
                        window.ChartManager = fallbackModule.default;
                        console.log('Original chart manager loaded as fallback');

                        if (window.workbook && window.currentSheet && typeof window.ChartManager.init === 'function') {
                            window.ChartManager.init(window.workbook, window.currentSheet);
                            window.chartManagerInitialized = true;
                            console.log('Original chart manager initialized with current workbook and sheet');
                        }
                    })
                    .catch(fallbackError => {
                        console.error('Failed to load original chart manager as fallback:', fallbackError);
                    });
            });

        // Import panel manager
        import('./panel-manager.js')
            .then(module => {
                window.PanelManager = module.default;
                console.log('Panel manager loaded successfully');
            })
            .catch(error => {
                console.error('Failed to load panel manager:', error);
            });

        // We're now using the direct welcome-fix.js approach instead of the module
        console.log('Using direct welcome-fix.js instead of welcome-modal.js module');

        // Import PDF export module
        import('./pdf-export.js')
            .then(module => {
                window.PdfExport = module.default;
                console.log('PDF export module loaded successfully');
            })
            .catch(error => {
                console.error('Failed to load PDF export module:', error);
            });

        // Import HTML export module
        import('./html-export.js')
            .then(module => {
                window.HtmlExport = module.default;
                console.log('HTML export module loaded successfully');
            })
            .catch(error => {
                console.error('Failed to load HTML export module:', error);
            });

        // Import file protection module
        import('./file-protection.js')
            .then(module => {
                // Create an instance of FileProtection
                window.FileProtection = new module.default();
                console.log('File protection module loaded successfully');

                // Make the showPasswordModal method globally accessible
                window.showPasswordProtectionModal = () => {
                    console.log('Global showPasswordProtectionModal called');
                    if (window.FileProtection && typeof window.FileProtection.showPasswordModal === 'function') {
                        window.FileProtection.showPasswordModal();
                    } else {
                        console.error('FileProtection instance or showPasswordModal method not found');
                        alert('Password protection feature is not available');
                    }
                };

                // Ensure the lock button has an event listener
                setTimeout(() => {
                    const lockBtn = document.getElementById('lockFileBtn');
                    if (lockBtn) {
                        console.log('Adding click event listener to lock button from app-init.js');

                        // Remove any existing event listeners
                        const newLockBtn = lockBtn.cloneNode(true);
                        lockBtn.parentNode.replaceChild(newLockBtn, lockBtn);

                        // Add direct onclick attribute
                        newLockBtn.setAttribute('onclick', 'if(window.showPasswordProtectionModal) window.showPasswordProtectionModal()');

                        // Also add event listener
                        newLockBtn.addEventListener('click', (e) => {
                            console.log('Lock button clicked from app-init.js');
                            e.preventDefault();
                            e.stopPropagation();
                            if (window.showPasswordProtectionModal) {
                                window.showPasswordProtectionModal();
                            }
                        });
                    } else {
                        console.warn('Lock button not found in the DOM');
                    }
                }, 1000); // Wait for DOM to be fully loaded
            })
            .catch(error => {
                console.error('Failed to load file protection module:', error);
            });

        // Import protected file opener module
        import('./protected-file-opener.js')
            .then(module => {
                window.ProtectedFileOpener = new module.default();
                console.log('Protected file opener module loaded successfully');
            })
            .catch(error => {
                console.error('Failed to load protected file opener module:', error);
            });

        // Import Acumatica connector module
        import('./acumatica-connector.js')
            .then(module => {
                window.acumaticaConnector = module.acumaticaConnector;
                console.log('Acumatica connector module loaded successfully');

                // Initialize the connector if it hasn't been initialized yet
                if (window.acumaticaConnector && typeof window.acumaticaConnector.init === 'function' && !window.acumaticaConnector.isInitialized) {
                    window.acumaticaConnector.init()
                        .then(() => console.log('Acumatica connector initialized successfully from app-init'))
                        .catch(err => console.error('Failed to initialize Acumatica connector from app-init:', err));
                }
            })
            .catch(error => {
                console.error('Failed to load Acumatica connector module:', error);
            });

        console.log('All modules loaded');
    } catch (error) {
        console.error('Error loading modules:', error);
    }
}

// Initialize UI components
function initializeUI() {
    try {
        // Initialize toolbar
        const toolbar = document.getElementById('toolbar');
        if (toolbar) {
            console.log('Initializing toolbar');
            // Toolbar initialization code
        }

        // Initialize spreadsheet
        const spreadsheet = document.getElementById('spreadsheetContainer');
        if (spreadsheet) {
            console.log('Initializing spreadsheet');
            // Spreadsheet initialization code
        }

        // Initialize status bar
        const statusBar = document.getElementById('statusBar');
        if (statusBar) {
            console.log('Initializing status bar');
            statusBar.textContent = 'Ready';
        }

        console.log('UI components initialized');
    } catch (error) {
        console.error('Error initializing UI:', error);
    }
}

// Set up event listeners
function setupEventListeners() {
    try {
        // File operations
        const openFileBtn = document.getElementById('openFileBtn');
        const saveFileBtn = document.getElementById('saveFileBtn');
        const newWorkbookBtn = document.getElementById('newWorkbookBtn');

        if (openFileBtn) {
            openFileBtn.addEventListener('click', function() {
                console.log('Open file button clicked');

                // Check if we're already opening a file (to prevent double dialogs)
                if (window._fileDialogOpen) {
                    console.log('File dialog already open, ignoring click');
                    return;
                }

                window._fileDialogOpen = true;

                // Check if there's an existing file input with id 'fileInput'
                let existingFileInput = document.getElementById('fileInput');
                if (existingFileInput) {
                    console.log('Using existing fileInput element');
                    existingFileInput.click();

                    // Reset the flag after a short delay
                    setTimeout(() => {
                        window._fileDialogOpen = false;
                    }, 1000);

                    return;
                }

                // Create a file input element
                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.accept = '.xlsx,.xls,.xlsm,.envent';
                fileInput.style.display = 'none';
                fileInput.id = 'fileInput'; // Give it an ID so we can find it later
                document.body.appendChild(fileInput);
                console.log('Created file input with accept:', fileInput.accept);

                // Set up the change event handler
                fileInput.addEventListener('change', function(event) {
                    if (window.mainModule && typeof window.mainModule.openFile === 'function') {
                        window.mainModule.openFile(event);
                    }
                    // Remove the file input after use
                    document.body.removeChild(fileInput);

                    // Reset the flag
                    window._fileDialogOpen = false;
                });

                // Trigger the file dialog
                fileInput.click();

                // Reset the flag after a timeout in case the user cancels the dialog
                setTimeout(() => {
                    window._fileDialogOpen = false;
                }, 10000); // 10 seconds should be enough for most file dialogs
            });
        }

        if (saveFileBtn) {
            saveFileBtn.addEventListener('click', function() {
                console.log('Save file button clicked');
                if (window.mainModule && typeof window.mainModule.saveFile === 'function') {
                    window.mainModule.saveFile();
                }
            });
        }

        if (newWorkbookBtn) {
            newWorkbookBtn.addEventListener('click', function() {
                console.log('New workbook button clicked');

                // Check if we're already creating a workbook (to prevent double triggers)
                if (window._creatingWorkbook) {
                    console.log('Already creating workbook, ignoring click');
                    return;
                }

                window._creatingWorkbook = true;

                if (window.mainModule && typeof window.mainModule.createNewWorkbook === 'function') {
                    window.mainModule.createNewWorkbook();
                }

                // Reset the flag after a short delay
                setTimeout(() => {
                    window._creatingWorkbook = false;
                }, 1000);
            });
        }

        // Chart button is already handled in main_script.js
        // We don't need to add another event listener here to avoid duplicates

        console.log('Event listeners set up');
    } catch (error) {
        console.error('Error setting up event listeners:', error);
    }
}

// Update status message
window.updateStatus = function(message, type = 'info') {
    const statusBar = document.getElementById('statusBar');
    if (statusBar) {
        statusBar.textContent = message;
        statusBar.className = 'status-bar ' + type;

        // Reset status after 5 seconds
        setTimeout(() => {
            statusBar.textContent = 'Ready';
            statusBar.className = 'status-bar';
        }, 5000);
    }
};
