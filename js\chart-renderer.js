/**
 * Chart Renderer
 * Handles the actual rendering of charts using different libraries
 */

/**
 * Create an ApexCharts chart
 * @param {HTMLElement} container - The container element
 * @param {string} type - The chart type
 * @param {Object} data - The chart data
 * @param {Object} options - The chart options
 * @returns {Object} The created chart
 */
export function createApexChart(container, type, data, options = {}) {
    console.log('Creating ApexChart with type:', type);

    if (!container) {
        console.error('Container element is not available');
        return null;
    }

    if (!data || !data.categories || !data.series) {
        console.error('Invalid data format for ApexChart');
        return null;
    }

    try {
        // Ensure container has dimensions
        if (!container.clientWidth || !container.clientHeight) {
            console.log('Container has no dimensions, setting explicit size');
            container.style.width = '100%';
            container.style.height = '300px';
        }

        // Ensure container is visible in the DOM
        if (!document.body.contains(container)) {
            console.warn('Container is not in the DOM yet, this may cause rendering issues');
        }

        // Map chart type to ApexCharts type
        let chartType = type;
        if (type === 'column') chartType = 'bar';
        if (type === 'doughnut') chartType = 'donut';

        // Ensure data is in the correct format
        let processedData = { ...data };

        // Make sure series data is valid
        if (processedData.series) {
            // Add detailed logging to diagnose the issue
            console.log('Processing series data:', JSON.stringify(processedData.series));

            // Ensure series is an array
            if (!Array.isArray(processedData.series)) {
                console.warn('Series is not an array, converting to array format');
                processedData.series = [{
                    name: 'Series 1',
                    data: Array.isArray(processedData.series) ? processedData.series : [0]
                }];
            } else {
                // Process each series
                processedData.series = processedData.series.map(series => {
                    // Check if series is properly structured
                    if (!series || typeof series !== 'object') {
                        console.warn('Invalid series object:', series);
                        return { name: 'Invalid Series', data: [0] };
                    }

                    // Check if data property exists and is an array
                    if (!series.data) {
                        console.warn('Series missing data property:', series);
                        return { name: series.name || 'Missing Data', data: [0] };
                    }

                    // Ensure data is an array
                    if (!Array.isArray(series.data)) {
                        console.warn('Series data is not an array:', series.data);
                        return {
                            name: series.name || 'Invalid Data',
                            data: [parseFloat(series.data) || 0]
                        };
                    }

                    // Process the data array
                    return {
                        name: series.name || 'Series',
                        data: series.data.map(val => {
                            if (val === null || val === undefined || isNaN(val)) {
                                return 0;
                            }
                            return typeof val === 'number' ? val : parseFloat(val) || 0;
                        })
                    };
                });
            }

            console.log('Processed series data:', JSON.stringify(processedData.series));
        }

        // Special handling for pie/donut charts
        if (chartType === 'pie' || chartType === 'donut') {
            // For pie charts, we need a simple array of values
            let pieData = [];

            try {
                // Extract data from the first series
                if (processedData.series && processedData.series.length > 0) {
                    const firstSeries = processedData.series[0];

                    // Check if data exists and is an array
                    if (firstSeries && firstSeries.data) {
                        if (Array.isArray(firstSeries.data)) {
                            pieData = firstSeries.data.map(val => {
                                if (val === null || val === undefined || isNaN(val)) {
                                    return 0;
                                }
                                return typeof val === 'number' ? val : parseFloat(val) || 0;
                            });
                        } else {
                            // If data is not an array, convert it
                            console.warn('Pie chart data is not an array:', firstSeries.data);
                            const singleValue = parseFloat(firstSeries.data) || 0;
                            pieData = [singleValue];
                        }
                    } else {
                        console.warn('Invalid first series for pie chart:', firstSeries);
                        pieData = [0];
                    }
                } else {
                    console.warn('No series data for pie chart');
                    pieData = [0];
                }
            } catch (error) {
                console.error('Error processing pie chart data:', error);
                pieData = [1]; // Fallback to dummy data
            }

            // Ensure we have at least one non-zero value
            if (!pieData.length || pieData.every(val => val === 0)) {
                console.warn('No valid data for pie chart, using dummy data');
                pieData = [1]; // Add a dummy value
                processedData.categories = ['No Data'];
            }

            // Ensure categories match data length
            if (!processedData.categories || !Array.isArray(processedData.categories) ||
                processedData.categories.length !== pieData.length) {
                console.warn('Categories don\'t match pie data length, generating default categories');
                processedData.categories = pieData.map((_, i) => `Category ${i+1}`);
            }

            const chartOptions = {
                chart: {
                    type: chartType,
                    height: options.height || 300,
                    width: options.width || '100%',
                    background: options.background || '#fff',
                    animations: {
                        enabled: options.animations !== false
                    },
                    toolbar: {
                        show: true,
                        tools: {
                            download: true,
                            selection: false,
                            zoom: false,
                            zoomin: false,
                            zoomout: false,
                            pan: false,
                            reset: false
                        }
                    }
                },
                title: {
                    text: options.title || 'Chart',
                    align: 'center',
                    style: {
                        fontSize: '16px',
                        fontWeight: 'bold'
                    }
                },
                series: pieData,
                labels: processedData.categories || ['No Data'],
                colors: options.colors || ['#008FFB', '#00E396', '#FEB019', '#FF4560', '#775DD0'],
                legend: {
                    show: options.legend !== false,
                    position: 'bottom'
                },
                dataLabels: {
                    enabled: options.dataLabels === true,
                    formatter: function(val, opts) {
                        if (val) {
                            return opts.w.config.labels[opts.seriesIndex] + ': ' + val.toFixed(1) + '%';
                        }
                        return '';
                    }
                },
                tooltip: {
                    enabled: true
                },
                plotOptions: {
                    pie: {
                        donut: {
                            size: chartType === 'donut' ? '55%' : '0%'
                        }
                    }
                },
                responsive: [
                    {
                        breakpoint: 480,
                        options: {
                            chart: {
                                width: '100%'
                            },
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                ]
            };

            // Create and render the chart with a delay to ensure container is ready
            console.log('Creating pie/donut ApexChart');

            // Ensure the container is ready before rendering
            return new Promise((resolve) => {
                setTimeout(() => {
                    try {
                        const chart = new ApexCharts(container, chartOptions);
                        chart.render();
                        console.log('ApexChart rendered successfully');
                        resolve(chart);
                    } catch (error) {
                        console.error('Error rendering ApexChart:', error);
                        // Fallback to basic chart
                        const fallbackChart = createBasicChart(container, type, processedData, options);
                        resolve(fallbackChart);
                    }
                }, 50);
            });
        } else {
            // For other chart types (bar, line, area)
            // Ensure we have valid series data
            if (!processedData.series || processedData.series.length === 0) {
                processedData.series = [{
                    name: 'Series 1',
                    data: [0]
                }];
                processedData.categories = ['No Data'];
            }

            const chartOptions = {
                chart: {
                    type: chartType,
                    height: options.height || 300,
                    width: options.width || '100%',
                    background: options.background || '#fff',
                    animations: {
                        enabled: options.animations !== false
                    },
                    toolbar: {
                        show: true,
                        tools: {
                            download: true,
                            selection: false,
                            zoom: false,
                            zoomin: false,
                            zoomout: false,
                            pan: false,
                            reset: false
                        }
                    }
                },
                title: {
                    text: options.title || 'Chart',
                    align: 'center',
                    style: {
                        fontSize: '16px',
                        fontWeight: 'bold'
                    }
                },
                series: processedData.series,
                xaxis: {
                    categories: processedData.categories || ['No Data'],
                    title: {
                        text: options.xAxisTitle || ''
                    }
                },
                yaxis: {
                    title: {
                        text: options.yAxisTitle || ''
                    },
                    min: 0 // Ensure y-axis starts at 0
                },
                colors: options.colors || ['#008FFB', '#00E396', '#FEB019', '#FF4560', '#775DD0'],
                legend: {
                    show: options.legend !== false,
                    position: 'bottom'
                },
                dataLabels: {
                    enabled: options.dataLabels === true
                },
                tooltip: {
                    enabled: true
                },
                grid: {
                    show: options.grid !== false
                },
                stroke: {
                    curve: options.lineStyle === 'smooth' ? 'smooth' : 'straight',
                    width: 2
                },
                fill: {
                    opacity: type === 'area' ? (options.fillOpacity || 0.7) : 1
                },
                plotOptions: {
                    bar: {
                        horizontal: type === 'bar',
                        columnWidth: '70%',
                        borderRadius: options.borderRadius || 0
                    }
                },
                responsive: [
                    {
                        breakpoint: 480,
                        options: {
                            chart: {
                                width: '100%'
                            },
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                ]
            };

            // Create and render the chart with a delay to ensure container is ready
            console.log('Creating standard ApexChart');

            // Ensure the container is ready before rendering
            return new Promise((resolve) => {
                setTimeout(() => {
                    try {
                        const chart = new ApexCharts(container, chartOptions);
                        chart.render();
                        console.log('ApexChart rendered successfully');
                        resolve(chart);
                    } catch (error) {
                        console.error('Error rendering ApexChart:', error);
                        // Fallback to basic chart
                        const fallbackChart = createBasicChart(container, type, processedData, options);
                        resolve(fallbackChart);
                    }
                }, 50);
            });
        }
    } catch (error) {
        console.error('Error creating ApexChart:', error);
        // Fallback to basic chart
        return createBasicChart(container, type, data, options);
    }
}

/**
 * Create a basic HTML chart (fallback when libraries aren't available)
 * @param {HTMLElement} container - The container element
 * @param {string} type - The chart type
 * @param {Object} data - The chart data
 * @param {Object} options - The chart options
 * @returns {Object} The created chart
 */
export function createBasicChart(container, type, data, options = {}) {
    if (!container) {
        console.error('Container element is not available');
        return null;
    }

    try {
        // Clear container
        container.innerHTML = '';

        // Create chart container
        const chartDiv = document.createElement('div');
        chartDiv.className = 'basic-chart';
        chartDiv.style.width = '100%';
        chartDiv.style.height = '100%';
        chartDiv.style.padding = '10px';
        chartDiv.style.boxSizing = 'border-box';

        // Add title
        const titleEl = document.createElement('h3');
        titleEl.textContent = options.title || 'Chart';
        titleEl.style.textAlign = 'center';
        titleEl.style.margin = '0 0 15px 0';
        titleEl.style.fontSize = '16px';
        chartDiv.appendChild(titleEl);

        // Create chart based on type
        if (type === 'pie' || type === 'doughnut') {
            createBasicPieChart(chartDiv, data, options, type === 'doughnut');
        } else {
            createBasicBarChart(chartDiv, data, options, type);
        }

        // Add to container
        container.appendChild(chartDiv);

        return {
            type: 'basic',
            element: chartDiv,
            update: (newData) => {
                // Simple update function
                container.innerHTML = '';
                return createBasicChart(container, type, newData, options);
            }
        };
    } catch (error) {
        console.error('Error creating basic chart:', error);
        return null;
    }
}

/**
 * Create a basic pie chart
 * @param {HTMLElement} container - The container element
 * @param {Object} data - The chart data
 * @param {Object} options - The chart options
 * @param {boolean} isDoughnut - Whether to create a doughnut chart
 */
function createBasicPieChart(container, data, options, isDoughnut) {
    // Create canvas element
    const canvas = document.createElement('canvas');
    canvas.width = 400;
    canvas.height = 300;
    canvas.style.maxWidth = '100%';
    canvas.style.height = 'auto';
    canvas.style.margin = '0 auto';
    canvas.style.display = 'block';

    container.appendChild(canvas);

    const ctx = canvas.getContext('2d');
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = Math.min(centerX, centerY) - 40;

    // Get data
    const values = data.series[0].data;
    const labels = data.categories;
    const colors = options.colors || ['#4472C4', '#ED7D31', '#A5A5A5', '#FFC000', '#5B9BD5', '#70AD47', '#264478', '#9E480E'];

    // Calculate total
    const total = values.reduce((sum, value) => sum + value, 0);

    // Draw pie/doughnut
    let startAngle = 0;

    for (let i = 0; i < values.length; i++) {
        const sliceAngle = 2 * Math.PI * values[i] / total;

        // Draw slice
        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.arc(centerX, centerY, radius, startAngle, startAngle + sliceAngle);
        ctx.closePath();

        // Fill slice
        ctx.fillStyle = colors[i % colors.length];
        ctx.fill();

        // Draw slice border
        ctx.lineWidth = 1;
        ctx.strokeStyle = '#fff';
        ctx.stroke();

        // If doughnut, create hole
        if (isDoughnut) {
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.arc(centerX, centerY, radius * 0.5, 0, 2 * Math.PI);
            ctx.closePath();
            ctx.fillStyle = '#fff';
            ctx.fill();
        }

        startAngle += sliceAngle;
    }

    // Add legend if enabled
    if (options.legend !== false) {
        const legendDiv = document.createElement('div');
        legendDiv.style.display = 'flex';
        legendDiv.style.flexWrap = 'wrap';
        legendDiv.style.justifyContent = 'center';
        legendDiv.style.marginTop = '15px';

        for (let i = 0; i < labels.length; i++) {
            const legendItem = document.createElement('div');
            legendItem.style.display = 'flex';
            legendItem.style.alignItems = 'center';
            legendItem.style.margin = '5px 10px';

            const colorBox = document.createElement('div');
            colorBox.style.width = '12px';
            colorBox.style.height = '12px';
            colorBox.style.backgroundColor = colors[i % colors.length];
            colorBox.style.marginRight = '5px';

            const labelText = document.createElement('span');
            labelText.textContent = `${labels[i]} (${values[i]})`;
            labelText.style.fontSize = '12px';

            legendItem.appendChild(colorBox);
            legendItem.appendChild(labelText);
            legendDiv.appendChild(legendItem);
        }

        container.appendChild(legendDiv);
    }
}

/**
 * Create a basic bar/column/line chart
 * @param {HTMLElement} container - The container element
 * @param {Object} data - The chart data
 * @param {Object} options - The chart options
 * @param {string} type - The chart type
 */
function createBasicBarChart(container, data, options, type) {
    // Create canvas element
    const canvas = document.createElement('canvas');
    canvas.width = 500;
    canvas.height = 300;
    canvas.style.maxWidth = '100%';
    canvas.style.height = 'auto';
    canvas.style.margin = '0 auto';
    canvas.style.display = 'block';

    container.appendChild(canvas);

    const ctx = canvas.getContext('2d');

    // Get data
    const categories = data.categories;
    const series = data.series;
    const colors = options.colors || ['#4472C4', '#ED7D31', '#A5A5A5', '#FFC000', '#5B9BD5', '#70AD47', '#264478', '#9E480E'];

    // Set dimensions
    const chartWidth = canvas.width - 60;
    const chartHeight = canvas.height - 60;
    const barWidth = chartWidth / categories.length / (series.length + 0.5);

    // Find max value for scaling
    let maxValue = 0;
    for (const serie of series) {
        const serieMax = Math.max(...serie.data);
        if (serieMax > maxValue) maxValue = serieMax;
    }

    // Round max value up to a nice number
    maxValue = Math.ceil(maxValue / 10) * 10;

    // Draw axes
    ctx.beginPath();
    ctx.moveTo(40, 20);
    ctx.lineTo(40, chartHeight + 20);
    ctx.lineTo(chartWidth + 40, chartHeight + 20);
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 1;
    ctx.stroke();

    // Draw grid if enabled
    if (options.grid !== false) {
        // Horizontal grid lines
        for (let i = 0; i <= 5; i++) {
            const y = chartHeight - (i * chartHeight / 5) + 20;

            ctx.beginPath();
            ctx.moveTo(40, y);
            ctx.lineTo(chartWidth + 40, y);
            ctx.strokeStyle = '#e0e0e0';
            ctx.lineWidth = 1;
            ctx.stroke();

            // Y-axis labels
            ctx.fillStyle = '#666';
            ctx.font = '10px Arial';
            ctx.textAlign = 'right';
            ctx.fillText((maxValue * i / 5).toFixed(0), 35, y + 3);
        }
    }

    // Draw data
    for (let s = 0; s < series.length; s++) {
        const serie = series[s];
        const color = colors[s % colors.length];

        if (type === 'line' || type === 'area') {
            // Draw line
            ctx.beginPath();

            for (let i = 0; i < categories.length; i++) {
                const x = 40 + (i + 0.5) * (chartWidth / categories.length);
                const y = chartHeight - (serie.data[i] / maxValue * chartHeight) + 20;

                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }

            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.stroke();

            // Fill area if area chart
            if (type === 'area') {
                ctx.lineTo(40 + (categories.length - 0.5) * (chartWidth / categories.length), chartHeight + 20);
                ctx.lineTo(40 + 0.5 * (chartWidth / categories.length), chartHeight + 20);
                ctx.closePath();
                ctx.fillStyle = `${color}40`; // Add transparency
                ctx.fill();
            }

            // Draw points
            for (let i = 0; i < categories.length; i++) {
                const x = 40 + (i + 0.5) * (chartWidth / categories.length);
                const y = chartHeight - (serie.data[i] / maxValue * chartHeight) + 20;

                ctx.beginPath();
                ctx.arc(x, y, 4, 0, 2 * Math.PI);
                ctx.fillStyle = color;
                ctx.fill();
                ctx.strokeStyle = '#fff';
                ctx.lineWidth = 1;
                ctx.stroke();
            }
        } else {
            // Draw bars
            for (let i = 0; i < categories.length; i++) {
                const value = serie.data[i];
                const barHeight = value / maxValue * chartHeight;

                let x, y, width, height;

                if (type === 'bar') {
                    // Horizontal bar
                    x = 40;
                    y = 20 + i * (chartHeight / categories.length) + s * (chartHeight / categories.length / series.length);
                    width = value / maxValue * chartWidth;
                    height = chartHeight / categories.length / series.length * 0.8;
                } else {
                    // Vertical column
                    x = 40 + i * (chartWidth / categories.length) + s * barWidth;
                    y = chartHeight - barHeight + 20;
                    width = barWidth * 0.8;
                    height = barHeight;
                }

                ctx.fillStyle = color;
                ctx.fillRect(x, y, width, height);

                // Add border
                ctx.strokeStyle = '#fff';
                ctx.lineWidth = 1;
                ctx.strokeRect(x, y, width, height);
            }
        }
    }

    // Draw x-axis labels
    for (let i = 0; i < categories.length; i++) {
        const x = 40 + (i + 0.5) * (chartWidth / categories.length);

        ctx.fillStyle = '#666';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(categories[i], x, chartHeight + 35);
    }

    // Add legend if enabled
    if (options.legend !== false && series.length > 1) {
        const legendDiv = document.createElement('div');
        legendDiv.style.display = 'flex';
        legendDiv.style.flexWrap = 'wrap';
        legendDiv.style.justifyContent = 'center';
        legendDiv.style.marginTop = '15px';

        for (let i = 0; i < series.length; i++) {
            const legendItem = document.createElement('div');
            legendItem.style.display = 'flex';
            legendItem.style.alignItems = 'center';
            legendItem.style.margin = '5px 10px';

            const colorBox = document.createElement('div');
            colorBox.style.width = '12px';
            colorBox.style.height = '12px';
            colorBox.style.backgroundColor = colors[i % colors.length];
            colorBox.style.marginRight = '5px';

            const labelText = document.createElement('span');
            labelText.textContent = series[i].name;
            labelText.style.fontSize = '12px';

            legendItem.appendChild(colorBox);
            legendItem.appendChild(labelText);
            legendDiv.appendChild(legendItem);
        }

        container.appendChild(legendDiv);
    }
}
