// js/selection_manager.js

let activeCell = null;
let selectionStart = null;
let selectionEnd = null;
let isSelecting = false;
let selectionType = 'cell'; // 'cell', 'column', 'row'

export function setupCellSelection(tableElement) {
    if (!tableElement) {
        console.error('No table element provided to setupCellSelection');
        return;
    }

    console.log('Setting up cell selection for table:', tableElement);

    // Clear any existing selection
    clearSelection();

    // Remove any existing event listeners to prevent duplicates
    tableElement.removeEventListener('mousedown', handleSelectionStart);
    document.removeEventListener('mousemove', handleSelectionMove);
    document.removeEventListener('mouseup', handleSelectionEnd);
    document.removeEventListener('keydown', handleKeyboardNavigation);

    // Add mousedown event to start selection
    tableElement.addEventListener('mousedown', handleSelectionStart);

    // Add mousemove event to update selection
    document.addEventListener('mousemove', handleSelectionMove);

    // Add mouseup event to end selection
    document.addEventListener('mouseup', handleSelectionEnd);

    // Add keyboard navigation
    document.addEventListener('keydown', handleKeyboardNavigation);

    // Add column header selection
    setupColumnHeaderSelection(tableElement);

    // Add row header selection
    setupRowHeaderSelection(tableElement);

    console.log('Cell selection setup complete');
}

/**
 * Setup column header selection
 * @param {HTMLElement} tableElement - The Excel table element
 */
function setupColumnHeaderSelection(tableElement) {
    // Use a more specific selector to ensure we get only the column headers
    const columnHeaders = tableElement.querySelectorAll('th.excel-col-header, th.column-header');

    console.log(`Found ${columnHeaders.length} column headers using improved selector`);

    // Add a direct click handler to the table for column headers
    tableElement.addEventListener('click', (event) => {
        // Check if the clicked element is a column header or contains a column header
        const columnHeader = event.target.closest('.excel-col-header, .column-header');
        if (!columnHeader) return;

        console.log('Column header clicked via delegation:', columnHeader);

        // Get the column number from the dataset or calculate from position
        let columnNumber;
        if (columnHeader.dataset.col) {
            columnNumber = parseInt(columnHeader.dataset.col);
        } else {
            // If no dataset, try to calculate from the header text (A=1, B=2, etc.)
            const headerText = columnHeader.textContent.trim();
            if (headerText.length === 1 && headerText >= 'A' && headerText <= 'Z') {
                columnNumber = headerText.charCodeAt(0) - 'A'.charCodeAt(0) + 1;
            } else {
                console.warn('Could not determine column number from header:', columnHeader);
                return;
            }
        }

        console.log(`Column header clicked: ${columnHeader.textContent}, column number: ${columnNumber}`);

        // Select the entire column
        selectEntireColumn(columnNumber, tableElement);

        // Prevent default behavior
        event.preventDefault();
        event.stopPropagation();
    });

    // Also add direct event listeners to each header as a fallback
    columnHeaders.forEach(header => {
        // Log the header details for debugging
        console.log(`Column header found: ${header.textContent}, dataset.col: ${header.dataset.col}`);

        header.addEventListener('click', (event) => {
            // Get the column number from the dataset or calculate from text
            let columnNumber;
            if (header.dataset.col) {
                columnNumber = parseInt(header.dataset.col);
            } else {
                // If no dataset, try to calculate from the header text (A=1, B=2, etc.)
                const headerText = header.textContent.trim();
                if (headerText.length === 1 && headerText >= 'A' && headerText <= 'Z') {
                    columnNumber = headerText.charCodeAt(0) - 'A'.charCodeAt(0) + 1;
                } else {
                    console.warn('Could not determine column number from header:', header);
                    return;
                }
            }

            console.log(`Column header direct click: ${header.textContent}, column number: ${columnNumber}`);

            // Select the entire column
            selectEntireColumn(columnNumber, tableElement);

            // Prevent default behavior
            event.preventDefault();
            event.stopPropagation();
        });
    });
}

/**
 * Setup row header selection
 * @param {HTMLElement} tableElement - The Excel table element
 */
function setupRowHeaderSelection(tableElement) {
    // Use a more specific selector to ensure we get only the row headers
    const rowHeaders = tableElement.querySelectorAll('td.excel-row-header, th.excel-row-header, td.row-header, th.row-header');

    console.log(`Found ${rowHeaders.length} row headers using improved selector`);

    // Add a direct click handler to the table for row headers
    tableElement.addEventListener('click', (event) => {
        // Check if the clicked element is a row header or contains a row header
        const rowHeader = event.target.closest('.excel-row-header, .row-header');
        if (!rowHeader) return;

        console.log('Row header clicked via delegation:', rowHeader);

        // Get the row number from the dataset or text content
        const rowNumber = parseInt(rowHeader.dataset.row || rowHeader.textContent);

        console.log(`Row header clicked: ${rowHeader.textContent}, row number: ${rowNumber}`);

        // Select the entire row
        selectEntireRow(rowNumber, tableElement);

        // Prevent default behavior
        event.preventDefault();
        event.stopPropagation();
    });

    // Also add direct event listeners to each header as a fallback
    rowHeaders.forEach(header => {
        // Log the header details for debugging
        console.log(`Row header found: ${header.textContent}, dataset.row: ${header.dataset.row}`);

        header.addEventListener('click', (event) => {
            // Get the row number from the dataset or header text (1-based)
            const rowNumber = parseInt(header.dataset.row || header.textContent);

            console.log(`Row header direct click: ${header.textContent}, row number: ${rowNumber}`);

            // Select the entire row
            selectEntireRow(rowNumber, tableElement);

            // Prevent default behavior
            event.preventDefault();
            event.stopPropagation();
        });
    });
}

/**
 * Select an entire column
 * @param {number} columnNumber - The column number (1-based)
 * @param {HTMLElement} tableElement - The Excel table element
 */
function selectEntireColumn(columnNumber, tableElement) {
    console.log(`Selecting entire column ${columnNumber}`);

    // Clear any existing selection
    clearSelection();

    // Set selection type
    selectionType = 'column';

    // Find the table if not provided
    if (!tableElement) {
        tableElement = document.querySelector('.excel-table');
        if (!tableElement) {
            console.error('No table element found for column selection');
            return;
        }
    }

    // Try multiple selectors to find cells in this column
    let cells = tableElement.querySelectorAll(`td input[data-col="${columnNumber}"]`);

    // If no cells found with the first selector, try alternative selectors
    if (cells.length === 0) {
        // Try to find cells by their position in the row (nth-child)
        // Add 1 to account for the row header cell
        cells = tableElement.querySelectorAll(`tr td:nth-child(${columnNumber + 1}) input`);
    }

    // If still no cells found, try to get the td elements directly
    if (cells.length === 0) {
        cells = tableElement.querySelectorAll(`tr td:nth-child(${columnNumber + 1})`);
    }

    console.log(`Found ${cells.length} cells in column ${columnNumber}`);

    if (cells.length === 0) {
        console.warn(`No cells found in column ${columnNumber}`);
        // Even if no cells found, still highlight the column header
        highlightColumnHeader(columnNumber);
        return;
    }

    // Set the first cell as active
    let firstCell;
    if (cells[0].tagName === 'INPUT') {
        firstCell = cells[0].parentElement;
    } else {
        firstCell = cells[0];
    }

    setActiveCell(firstCell);

    // Set selection start and end
    selectionStart = {
        row: 1,
        col: columnNumber,
        element: firstCell
    };

    selectionEnd = {
        row: cells.length,
        col: columnNumber,
        element: cells[cells.length - 1].tagName === 'INPUT' ?
                cells[cells.length - 1].parentElement :
                cells[cells.length - 1]
    };

    console.log(`Selection set from row 1 to row ${cells.length} in column ${columnNumber}`);

    // Highlight the selection
    highlightSelection();

    // Dispatch a custom event to notify that a column has been selected
    document.dispatchEvent(new CustomEvent('column-selected', {
        detail: {
            column: columnNumber
        }
    }));
}

// Helper function to highlight just the column header
function highlightColumnHeader(columnNumber) {
    // Find the column header
    const columnHeaders = document.querySelectorAll('th.excel-col-header, th.column-header');
    let columnHeader = null;

    // Find the header with matching dataset.col
    for (let i = 0; i < columnHeaders.length; i++) {
        if (columnHeaders[i].dataset.col && parseInt(columnHeaders[i].dataset.col) === columnNumber) {
            columnHeader = columnHeaders[i];
            break;
        }

        // If no dataset.col, try to calculate from the header text (A=1, B=2, etc.)
        const headerText = columnHeaders[i].textContent.trim();
        if (headerText.length === 1 && headerText >= 'A' && headerText <= 'Z') {
            const colNum = headerText.charCodeAt(0) - 'A'.charCodeAt(0) + 1;
            if (colNum === columnNumber) {
                columnHeader = columnHeaders[i];
                break;
            }
        }
    }

    if (columnHeader) {
        console.log(`Found column header for column ${columnNumber}: ${columnHeader.textContent}`);
        columnHeader.classList.add('selected');
    } else {
        console.warn(`Could not find column header for column ${columnNumber}`);
    }
}

/**
 * Select an entire row
 * @param {number} rowNumber - The row number (1-based)
 * @param {HTMLElement} tableElement - The Excel table element
 */
function selectEntireRow(rowNumber, tableElement) {
    console.log(`Selecting entire row ${rowNumber}`);

    // Clear any existing selection
    clearSelection();

    // Set selection type
    selectionType = 'row';

    // Find the table if not provided
    if (!tableElement) {
        tableElement = document.querySelector('.excel-table');
        if (!tableElement) {
            console.error('No table element found for row selection');
            return;
        }
    }

    // Try multiple selectors to find cells in this row
    let cells = tableElement.querySelectorAll(`td input[data-row="${rowNumber}"]`);

    // If no cells found with the first selector, try alternative selectors
    if (cells.length === 0) {
        // Try to find cells by their position in the table (nth-child)
        // Add 1 to account for the header row
        cells = tableElement.querySelectorAll(`tr:nth-child(${rowNumber + 1}) td input`);
    }

    // If still no cells found, try to get the td elements directly
    if (cells.length === 0) {
        cells = tableElement.querySelectorAll(`tr:nth-child(${rowNumber + 1}) td`);
        // Filter out the row header cell
        cells = Array.from(cells).filter(cell => !cell.classList.contains('excel-row-header') && !cell.classList.contains('row-header'));
    }

    console.log(`Found ${cells.length} cells in row ${rowNumber}`);

    if (cells.length === 0) {
        console.warn(`No cells found in row ${rowNumber}`);
        // Even if no cells found, still highlight the row header
        highlightRowHeader(rowNumber);
        return;
    }

    // Set the first cell as active
    let firstCell;
    if (cells[0].tagName === 'INPUT') {
        firstCell = cells[0].parentElement;
    } else {
        firstCell = cells[0];
    }

    setActiveCell(firstCell);

    // Set selection start and end
    selectionStart = {
        row: rowNumber,
        col: 1,
        element: firstCell
    };

    selectionEnd = {
        row: rowNumber,
        col: cells.length,
        element: cells[cells.length - 1].tagName === 'INPUT' ?
                cells[cells.length - 1].parentElement :
                cells[cells.length - 1]
    };

    console.log(`Selection set from column 1 to column ${cells.length} in row ${rowNumber}`);

    // Highlight the selection
    highlightSelection();

    // Dispatch a custom event to notify that a row has been selected
    document.dispatchEvent(new CustomEvent('row-selected', {
        detail: {
            row: rowNumber
        }
    }));
}

// Helper function to highlight just the row header
function highlightRowHeader(rowNumber) {
    // Find the row header
    const rowHeaders = document.querySelectorAll('.excel-row-header, .row-header');
    let rowHeader = null;

    // Find the header with matching dataset.row or text content
    for (let i = 0; i < rowHeaders.length; i++) {
        const headerRow = parseInt(rowHeaders[i].dataset.row || rowHeaders[i].textContent);
        if (headerRow === rowNumber) {
            rowHeader = rowHeaders[i];
            break;
        }
    }

    if (rowHeader) {
        console.log(`Found row header for row ${rowNumber}: ${rowHeader.textContent}`);
        rowHeader.classList.add('selected');
    } else {
        console.warn(`Could not find row header for row ${rowNumber}`);
    }
}

export function setActiveCell(cell) {
    // Remove active class from previous active cell
    if (activeCell) {
        activeCell.classList.remove('active-cell');
    }

    activeCell = cell;

    if (activeCell) {
        activeCell.classList.add('active-cell');

        // Update formula bar
        if (window.formulaManager) {
            const row = parseInt(activeCell.dataset.row);
            const col = parseInt(activeCell.dataset.col);
            const xlsxCell = window.currentSheet.cell(row, col);
            window.formulaManager.updateFormulaBar(xlsxCell, activeCell);
        }

        // Update toolbar states
        updateToolbarStates();
    }

    return activeCell;
}

export function getActiveCell() {
    return activeCell;
}

export function getSelectedRange() {
    if (!selectionStart || !selectionEnd) return null;

    return {
        start: {
            r: Math.min(selectionStart.row, selectionEnd.row),
            c: Math.min(selectionStart.col, selectionEnd.col)
        },
        end: {
            r: Math.max(selectionStart.row, selectionEnd.row),
            c: Math.max(selectionStart.col, selectionEnd.col)
        },
        type: selectionType // 'cell', 'column', or 'row'
    };
}

function handleSelectionStart(event) {
    // Check if we're clicking on a header (but allow row/column header clicks to be handled by their own handlers)
    if (event.target.tagName === 'TH' &&
        !event.target.classList.contains('excel-col-header') &&
        !event.target.classList.contains('column-header') &&
        !event.target.classList.contains('excel-row-header') &&
        !event.target.classList.contains('row-header')) {
        console.log('Clicked on a non-selectable header, ignoring in handleSelectionStart');
        return;
    }

    // Check if we're clicking on a row or column header
    const isRowHeader = event.target.classList.contains('excel-row-header') ||
                        event.target.classList.contains('row-header') ||
                        event.target.closest('.excel-row-header, .row-header');

    const isColHeader = event.target.classList.contains('excel-col-header') ||
                        event.target.classList.contains('column-header') ||
                        event.target.closest('.excel-col-header, .column-header');

    // If it's a row or column header, let the specific handlers deal with it
    if (isRowHeader || isColHeader) {
        console.log('Clicked on a row/column header, will be handled by specific handlers');
        return;
    }

    // Find the cell - could be the td or an input inside it
    let cell;
    if (event.target.tagName === 'INPUT') {
        cell = event.target.parentElement;
    } else {
        cell = event.target.closest('td');
    }

    if (!cell) {
        console.log('No cell found in handleSelectionStart');
        return;
    }

    console.log('Cell selection started on:', cell);

    // Reset selection type to cell
    selectionType = 'cell';

    isSelecting = true;

    // Set active cell
    setActiveCell(cell);

    // Get row and column from dataset
    let row, col;

    // Try to get row/col from the cell itself
    if (cell.dataset.row && cell.dataset.col) {
        row = parseInt(cell.dataset.row);
        col = parseInt(cell.dataset.col);
    }
    // Otherwise try to get from the input inside the cell
    else {
        const input = cell.querySelector('input');
        if (input && input.dataset.row && input.dataset.col) {
            row = parseInt(input.dataset.row);
            col = parseInt(input.dataset.col);
        } else {
            console.warn('Could not determine row/col for cell:', cell);
            return;
        }
    }

    console.log(`Cell selection: row=${row}, col=${col}`);

    // Start selection
    selectionStart = {
        row: row,
        col: col,
        element: cell
    };

    selectionEnd = { ...selectionStart };

    // Highlight the cell
    highlightSelection();

    // Prevent text selection
    event.preventDefault();
}

function handleSelectionMove(event) {
    if (!isSelecting) return;

    const cell = document.elementFromPoint(event.clientX, event.clientY)?.closest('td');
    if (!cell || !cell.dataset.row || !cell.dataset.col) return;

    selectionEnd = {
        row: parseInt(cell.dataset.row),
        col: parseInt(cell.dataset.col),
        element: cell
    };

    highlightSelection();
}

function handleSelectionEnd() {
    isSelecting = false;
}

function highlightSelection() {
    // Clear previous selection
    clearSelectionHighlight();

    if (!selectionStart || !selectionEnd) return;

    const startRow = Math.min(selectionStart.row, selectionEnd.row);
    const endRow = Math.max(selectionStart.row, selectionEnd.row);
    const startCol = Math.min(selectionStart.col, selectionEnd.col);
    const endCol = Math.max(selectionStart.col, selectionEnd.col);

    // Get the table element
    const table = document.querySelector('.excel-table');
    if (!table) return;

    console.log(`Highlighting selection: type=${selectionType}, startRow=${startRow}, startCol=${startCol}`);

    // Handle different selection types
    switch (selectionType) {
        case 'column':
            console.log(`Highlighting column ${startCol}`);

            // Highlight the column header
            highlightColumnHeader(startCol);

            // Try multiple approaches to highlight all cells in the column

            // Approach 1: Use data-col attribute on inputs
            const columnCells1 = document.querySelectorAll(`td input[data-col="${startCol}"]`);
            if (columnCells1.length > 0) {
                console.log(`Found ${columnCells1.length} cells in column ${startCol} using data-col`);
                columnCells1.forEach(input => {
                    if (input.parentElement) {
                        input.parentElement.classList.add('selected-column');
                    }
                });
            }

            // Approach 2: Use nth-child on td elements (accounting for row header)
            const columnCells2 = document.querySelectorAll(`tr td:nth-child(${startCol + 1})`);
            if (columnCells2.length > 0) {
                console.log(`Found ${columnCells2.length} cells in column ${startCol} using nth-child`);
                columnCells2.forEach(cell => {
                    // Skip row header cells
                    if (!cell.classList.contains('excel-row-header') && !cell.classList.contains('row-header')) {
                        cell.classList.add('selected-column');
                    }
                });
            }

            // Approach 3: Use the table rows directly
            const rows = table.querySelectorAll('tr');
            rows.forEach((row, rowIndex) => {
                // Skip the header row
                if (rowIndex === 0) return;

                // Get all cells in this row
                const cells = row.querySelectorAll('td');
                // The column we want is at index startCol (accounting for row header)
                if (cells.length > startCol) {
                    const cell = cells[startCol];
                    if (cell && !cell.classList.contains('excel-row-header') && !cell.classList.contains('row-header')) {
                        cell.classList.add('selected-column');
                    }
                }
            });

            break;

        case 'row':
            console.log(`Highlighting row ${startRow}`);

            // Highlight the row header
            highlightRowHeader(startRow);

            // Try multiple approaches to highlight all cells in the row

            // Approach 1: Use data-row attribute on inputs
            const rowCells1 = document.querySelectorAll(`td input[data-row="${startRow}"]`);
            if (rowCells1.length > 0) {
                console.log(`Found ${rowCells1.length} cells in row ${startRow} using data-row`);
                rowCells1.forEach(input => {
                    if (input.parentElement) {
                        input.parentElement.classList.add('selected-row');
                    }
                });
            }

            // Approach 2: Use nth-child on tr elements (accounting for header row)
            const rowElement = table.querySelector(`tr:nth-child(${startRow + 1})`);
            if (rowElement) {
                const rowCells2 = rowElement.querySelectorAll('td');
                console.log(`Found ${rowCells2.length} cells in row ${startRow} using tr:nth-child`);
                rowCells2.forEach(cell => {
                    // Skip row header cells
                    if (!cell.classList.contains('excel-row-header') && !cell.classList.contains('row-header')) {
                        cell.classList.add('selected-row');
                    }
                });
            }

            // Approach 3: Use direct DOM traversal
            // Get all rows in the table
            const allRows = table.querySelectorAll('tr');
            // The row we want is at index startRow (accounting for header row)
            if (allRows.length > startRow) {
                const targetRow = allRows[startRow]; // 0-based index, so startRow is correct
                if (targetRow) {
                    const cells = targetRow.querySelectorAll('td');
                    cells.forEach(cell => {
                        // Skip row header cells
                        if (!cell.classList.contains('excel-row-header') && !cell.classList.contains('row-header')) {
                            cell.classList.add('selected-row');
                        }
                    });
                }
            }

            break;

        case 'cell':
        default:
            console.log(`Highlighting cell range from [${startRow},${startCol}] to [${endRow},${endCol}]`);

            // Try multiple approaches to highlight cells in the range

            // Approach 1: Use data-row and data-col attributes on inputs
            for (let row = startRow; row <= endRow; row++) {
                for (let col = startCol; col <= endCol; col++) {
                    const cell = document.querySelector(`td input[data-row="${row}"][data-col="${col}"]`);
                    if (cell && cell.parentElement) {
                        cell.parentElement.classList.add('selected-cell');
                    }
                }
            }

            // Approach 2: Use table rows and cells directly
            const tableRows = table.querySelectorAll('tr');
            for (let r = startRow; r <= endRow; r++) {
                // Skip the header row and account for 0-based indexing
                if (r < tableRows.length) {
                    const row = tableRows[r];
                    const cells = row.querySelectorAll('td');
                    for (let c = startCol; c <= endCol; c++) {
                        // Account for row header by adding 1 to column index
                        if (c < cells.length) {
                            const cell = cells[c];
                            if (cell && !cell.classList.contains('excel-row-header') && !cell.classList.contains('row-header')) {
                                cell.classList.add('selected-cell');
                            }
                        }
                    }
                }
            }

            break;
    }

    // Make the selected range available globally for clipboard operations
    window.selectedRange = getSelectedRange();

    // Dispatch a custom event to notify that cells have been selected
    document.dispatchEvent(new CustomEvent('selection-changed', {
        detail: {
            range: window.selectedRange,
            type: selectionType
        }
    }));
}

function clearSelectionHighlight() {
    // Clear cell selections
    const selectedCells = document.querySelectorAll('.selected-cell');
    selectedCells.forEach(cell => {
        cell.classList.remove('selected-cell');
    });

    // Clear column selections
    const selectedColumns = document.querySelectorAll('.selected-column');
    selectedColumns.forEach(cell => {
        cell.classList.remove('selected-column');
    });

    // Clear row selections
    const selectedRows = document.querySelectorAll('.selected-row');
    selectedRows.forEach(cell => {
        cell.classList.remove('selected-row');
    });

    // Clear header selections
    const selectedHeaders = document.querySelectorAll('th.selected');
    selectedHeaders.forEach(header => {
        header.classList.remove('selected');
    });

    // Also clear the legacy .selected class for backward compatibility
    const legacySelected = document.querySelectorAll('.selected');
    legacySelected.forEach(cell => {
        cell.classList.remove('selected');
    });
}

function clearSelection() {
    selectionStart = null;
    selectionEnd = null;
    selectionType = 'cell'; // Reset to default
    clearSelectionHighlight();
}

function handleKeyboardNavigation(event) {
    if (!activeCell) return;

    const row = parseInt(activeCell.dataset.row);
    const col = parseInt(activeCell.dataset.col);

    let newRow = row;
    let newCol = col;

    switch (event.key) {
        case 'ArrowUp':
            newRow = Math.max(1, row - 1);
            break;
        case 'ArrowDown':
            newRow = Math.min(100, row + 1);
            break;
        case 'ArrowLeft':
            newCol = Math.max(1, col - 1);
            break;
        case 'ArrowRight':
            newCol = Math.min(26, col + 1);
            break;
        case 'Tab':
            newCol = Math.min(26, col + 1);
            event.preventDefault();
            break;
        case 'Enter':
            newRow = Math.min(100, row + 1);
            event.preventDefault();
            break;
        default:
            return;
    }

    if (newRow !== row || newCol !== col) {
        const newCell = document.querySelector(`td[data-row="${newRow}"][data-col="${newCol}"]`);
        if (newCell) {
            setActiveCell(newCell);
            newCell.focus();
        }
    }
}

// Make functions available globally for other modules
// This needs to be at the end of the file after the functions are defined
window.getActiveCell = getActiveCell;
window.getSelectedRange = getSelectedRange;
window.selectEntireColumn = selectEntireColumn;
window.selectEntireRow = selectEntireRow;
