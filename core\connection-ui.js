// UI Controller for the Connection Panel
import { connectionManager } from "./connection.js";
import { NotificationSystem } from "./notifications.js";

export class ConnectionUI {
  constructor() {
    this.notificationSystem = new NotificationSystem();
    this.isPopupOpen = false;
  }

  async init() {
    await connectionManager.init();
    this.updateConnectionIcon();
    this.setupEventListeners();
  }

  setupEventListeners() {
    // Set up the connection button click event
    const connectionButton = document.getElementById('connectionButton');
    if (connectionButton) {
      connectionButton.addEventListener('click', () => {
        if (this.isPopupOpen) {
          this.removeExistingPopup();
        } else {
          this.showConnectionPopup();
        }
      });
    }

    // Listen for connection changes from other components
    document.addEventListener('acumaticaConnectionChanged', () => {
      this.updateConnectionIcon();
    });
  }

  updateConnectionIcon() {
    const connectionButton = document.getElementById('connectionButton');
    if (!connectionButton) return;

    const status = connectionManager.getConnectionStatus();
    
    // Count active connections
    const connections = Object.values(status).filter(conn => conn.isConnected);
    const activeCount = connections.length;
    
    if (activeCount > 0) {
      connectionButton.classList.add('connected');
      connectionButton.innerHTML = `<i class="fas fa-plug"></i>`;
      // Add a green dot indicator
      connectionButton.classList.add('bg-green-500');
    } else {
      connectionButton.classList.remove('connected', 'bg-green-500');
      connectionButton.innerHTML = `<i class="fas fa-plug"></i>`;
    }
  }

  showConnectionPopup() {
    // Remove any existing popup
    this.removeExistingPopup();
    this.isPopupOpen = true;
    
    // Get latest status
    const status = connectionManager.getConnectionStatus();
    
    // Create connection popup - position it with absolute instead of fixed
    const popup = document.createElement('div');
    popup.id = 'connectionPopup';
    popup.className = 'fixed inset-0 bg-black bg-opacity-50 flex justify-center z-50 overflow-hidden';
    popup.style.cssText = 'padding-top: 75px; padding-bottom: 75px;';
    
    const content = document.createElement('div');
    content.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-xl w-96 flex flex-col';
    content.style.cssText = 'max-height: 450px; height: 450px;';
    
    content.innerHTML = `
      <div class="flex justify-between items-center p-3 pb-2 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 z-20 shadow-sm rounded-t-lg">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white">External Connections</h3>
        <button id="closeConnectionPopupBtn" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      <div class="p-3 overflow-y-auto flex-grow">
        <div class="space-y-3">
          <!-- Acumatica Connection -->
          <div class="p-2 bg-gray-50 dark:bg-gray-700 rounded-md">
            <div class="flex justify-between items-center mb-1">
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2 ${status.acumatica.isConnected ? 'text-green-500' : 'text-gray-400'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M12 5l7 7-7 7"></path>
                </svg>
                <h4 class="font-medium">Acumatica ERP</h4>
              </div>
              <span class="px-2 py-1 text-xs rounded-full ${status.acumatica.isConnected ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                ${status.acumatica.isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
            
            ${status.acumatica.isConnected ? 
              `<div class="text-sm text-gray-600 dark:text-gray-300 mb-1">
                <p>Connected to: ${status.acumatica.instance}</p>
                <p>User: ${status.acumatica.username}</p>
              </div>
              <button id="disconnectAcumaticaBtn" class="mt-1 px-3 py-1 bg-red-600 text-white rounded-md text-sm hover:bg-red-700">
                Disconnect
              </button>` 
              : 
              `<form id="acumaticaForm" class="mt-1 space-y-1">
                <div>
                  <label class="block text-xs mb-1">Instance URL</label>
                  <input type="text" id="acumaticaInstance" value="https://envent-eng.acumatica.com" class="w-full px-2 py-1 text-sm border rounded-md dark:bg-gray-800 dark:border-gray-600">
                </div>
                <div>
                  <label class="block text-xs mb-1">Username</label>
                  <input type="text" id="acumaticaUsername" class="w-full px-2 py-1 text-sm border rounded-md dark:bg-gray-800 dark:border-gray-600">
                </div>
                <div>
                  <label class="block text-xs mb-1">Password</label>
                  <input type="password" id="acumaticaPassword" class="w-full px-2 py-1 text-sm border rounded-md dark:bg-gray-800 dark:border-gray-600">
                </div>
                <div>
                  <label class="block text-xs mb-1">Company</label>
                  <input type="text" id="acumaticaCompany" value="Envent CA - Live" class="w-full px-2 py-1 text-sm border rounded-md dark:bg-gray-800 dark:border-gray-600">
                </div>
                <button type="submit" id="connectAcumaticaBtn" class="w-full mt-1 px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                  Connect
                </button>
              </form>`
            }
          </div>
          
          <!-- Monday.com Connection -->
          <div class="p-2 bg-gray-50 dark:bg-gray-700 rounded-md">
            <div class="flex justify-between items-center mb-1">
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2 ${status.monday.isConnected ? 'text-green-500' : 'text-gray-400'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                  <circle cx="8.5" cy="8.5" r="1.5"></circle>
                  <circle cx="15.5" cy="8.5" r="1.5"></circle>
                  <circle cx="15.5" cy="15.5" r="1.5"></circle>
                  <circle cx="8.5" cy="15.5" r="1.5"></circle>
                </svg>
                <h4 class="font-medium">Monday.com</h4>
              </div>
              <span class="px-2 py-1 text-xs rounded-full ${status.monday.isConnected ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                ${status.monday.isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
            
            ${status.monday.isConnected ? 
              `<div class="text-sm text-gray-600 dark:text-gray-300 mb-1">
                <p>Integration is active</p>
              </div>
              <div class="flex space-x-2">
                <button id="updateMondayBtn" class="mt-1 px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                  Update API Key
                </button>
                <button id="disconnectMondayBtn" class="mt-1 px-3 py-1 bg-red-600 text-white rounded-md text-sm hover:bg-red-700">
                  Disconnect
                </button>
              </div>` 
              : 
              `<form id="mondayForm" class="mt-1 space-y-1">
                <div>
                  <label class="block text-xs mb-1">API Key</label>
                  <input type="text" id="mondayApiKey" class="w-full px-2 py-1 text-sm border rounded-md dark:bg-gray-800 dark:border-gray-600">
                </div>
                <button type="submit" id="connectMondayBtn" class="w-full mt-1 px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                  Connect
                </button>
              </form>`
            }
          </div>
          
          <!-- ShipWave Connection -->
          <div class="p-2 bg-gray-50 dark:bg-gray-700 rounded-md">
            <div class="flex justify-between items-center mb-1">
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2 ${status.shipWave.isConnected ? 'text-green-500' : 'text-gray-400'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
                </svg>
                <h4 class="font-medium">ShipWave</h4>
              </div>
              <span class="px-2 py-1 text-xs rounded-full ${status.shipWave.isConnected ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                ${status.shipWave.isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
            
            ${status.shipWave.isConnected ? 
              `<div class="text-sm text-gray-600 dark:text-gray-300 mb-1">
                <p>Integration is active</p>
              </div>
              <div class="flex space-x-2">
                <button id="updateShipWaveBtn" class="mt-1 px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                  Update API Key
                </button>
                <button id="disconnectShipWaveBtn" class="mt-1 px-3 py-1 bg-red-600 text-white rounded-md text-sm hover:bg-red-700">
                  Disconnect
                </button>
              </div>` 
              : 
              `<form id="shipWaveForm" class="mt-1 space-y-1">
                <div>
                  <label class="block text-xs mb-1">API Key</label>
                  <input type="text" id="shipWaveApiKey" class="w-full px-2 py-1 text-sm border rounded-md dark:bg-gray-800 dark:border-gray-600">
                </div>
                <button type="submit" id="connectShipWaveBtn" class="w-full mt-1 px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                  Connect
                </button>
              </form>`
            }
          </div>
          
          <!-- Freight Sample Connection -->
          <div class="p-2 bg-gray-50 dark:bg-gray-700 rounded-md">
            <div class="flex justify-between items-center mb-1">
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2 ${status.freightSample.isConnected ? 'text-green-500' : 'text-gray-400'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h8a1 1 0 001-1z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 16l-3.5-3.5M20 6.5L16.5 10"></path>
                </svg>
                <h4 class="font-medium">Freight Sample</h4>
              </div>
              <span class="px-2 py-1 text-xs rounded-full ${status.freightSample.isConnected ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                ${status.freightSample.isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
            
            ${status.freightSample.isConnected ? 
              `<div class="text-sm text-gray-600 dark:text-gray-300 mb-1">
                <p>Integration is active</p>
              </div>
              <div class="flex space-x-2">
                <button id="updateFreightSampleBtn" class="mt-1 px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                  Update Token
                </button>
                <button id="disconnectFreightSampleBtn" class="mt-1 px-3 py-1 bg-red-600 text-white rounded-md text-sm hover:bg-red-700">
                  Disconnect
                </button>
              </div>` 
              : 
              `<form id="freightSampleForm" class="mt-1 space-y-1">
                <div>
                  <label class="block text-xs mb-1">Bearer Token</label>
                  <input type="text" id="freightSampleToken" class="w-full px-2 py-1 text-sm border rounded-md dark:bg-gray-800 dark:border-gray-600">
                </div>
                <button type="submit" id="connectFreightSampleBtn" class="w-full mt-1 px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                  Connect
                </button>
              </form>`
            }
          </div>

          <!-- Dymo Labeler Connection -->
          <div class="p-2 bg-gray-50 dark:bg-gray-700 rounded-md">
            <div class="flex justify-between items-center mb-1">
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2 ${status.dymo.isConnected ? 'text-green-500' : 'text-gray-400'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 6h3a2 2 0 012 2v8a2 2 0 01-2 2h-3M11 6H8a2 2 0 00-2 2v8a2 2 0 002 2h3M17 16v-2m0 0v-6m0 6h-6m6 0h-6"></path>
                </svg>
                <h4 class="font-medium">Dymo Labeler</h4>
              </div>
              <span class="px-2 py-1 text-xs rounded-full ${status.dymo.isConnected ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                ${status.dymo.isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
            
            ${status.dymo.isConnected ? 
              `<div class="text-sm text-gray-600 dark:text-gray-300 mb-1">
                <p>Labeler is connected and ready</p>
              </div>
              <button id="disconnectDymoBtn" class="mt-1 px-3 py-1 bg-red-600 text-white rounded-md text-sm hover:bg-red-700">
                Disconnect
              </button>` 
              : 
              `<div class="mt-1 space-y-1">
                <p class="text-sm text-gray-600 dark:text-gray-300">Connect to use Dymo label printing functionality.</p>
                <button id="connectDymoBtn" class="w-full mt-1 px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                  Connect
                </button>
              </div>`
            }
          </div>

          <!-- Microsoft Services -->
          <div class="p-2 bg-gray-50 dark:bg-gray-700 rounded-md">
            <div class="flex justify-between items-center mb-1">
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                </svg>
                <h4 class="font-medium">Microsoft Services</h4>
              </div>
            </div>
            
            <div class="mt-1 grid grid-cols-2 gap-2">
              <button id="connectOutlookBtn" class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 flex items-center justify-center">
                <span class="mr-1">Outlook</span>
                <span class="px-1 text-xs bg-blue-500 rounded-full">${status.outlook.isConnected ? '✓' : '+'}</span>
              </button>
              
              <button id="connectTeamsBtn" class="px-3 py-1 bg-purple-600 text-white rounded-md text-sm hover:bg-purple-700 flex items-center justify-center">
                <span class="mr-1">Teams</span>
                <span class="px-1 text-xs bg-purple-500 rounded-full">${status.teams.isConnected ? '✓' : '+'}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    `;
    
    popup.appendChild(content);
    document.body.appendChild(popup);
    
    // Add event listeners
    this.setupPopupEventListeners();
    
    // Fetch and populate the Monday API key from user data if available
    chrome.storage.local.get(['user'], (data) => {
      if (data.user && data.user["Monday API Key"]) {
        const mondayApiKeyInput = document.getElementById('mondayApiKey');
        if (mondayApiKeyInput) {
          mondayApiKeyInput.value = data.user["Monday API Key"];
        }
      }
    });
    
    // Close popup when clicking outside
    popup.addEventListener('click', (e) => {
      if (e.target === popup) {
        this.removeExistingPopup();
      }
    });
  }

  setupPopupEventListeners() {
    // Close button
    const closeBtn = document.getElementById('closeConnectionPopupBtn');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        this.removeExistingPopup();
      });
    }
    
    // Acumatica connection form
    const acumaticaForm = document.getElementById('acumaticaForm');
    if (acumaticaForm) {
      acumaticaForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const instance = document.getElementById('acumaticaInstance').value;
        const username = document.getElementById('acumaticaUsername').value;
        const password = document.getElementById('acumaticaPassword').value;
        const company = document.getElementById('acumaticaCompany').value;
        
        if (!instance || !username || !password || !company) {
          this.notificationSystem.addNotification('Please fill in all Acumatica fields.', 'error');
          return;
        }
        
        // Disable form
        const submitBtn = document.getElementById('connectAcumaticaBtn');
        submitBtn.disabled = true;
        submitBtn.textContent = 'Connecting...';
        
        // Connect
        const result = await connectionManager.connectToAcumatica(instance, username, password, company);
        
        // Handle result
        if (result.success) {
          this.notificationSystem.addNotification('Successfully connected to Acumatica.', 'success');
          this.updateConnectionIcon();
          this.showConnectionPopup(); // Refresh popup
          
          // Dispatch event for components to know about the connection
          document.dispatchEvent(new CustomEvent('acumaticaConnectionChanged', { 
            detail: { connected: true } 
          }));
        } else {
          this.notificationSystem.addNotification(`Failed to connect: ${result.error}`, 'error');
          
          // Re-enable form
          submitBtn.disabled = false;
          submitBtn.textContent = 'Connect';
        }
      });
    }
    
    // Acumatica disconnect button
    const disconnectAcumaticaBtn = document.getElementById('disconnectAcumaticaBtn');
    if (disconnectAcumaticaBtn) {
      disconnectAcumaticaBtn.addEventListener('click', async () => {
        disconnectAcumaticaBtn.textContent = 'Disconnecting...';
        disconnectAcumaticaBtn.disabled = true;
        
        const result = await connectionManager.disconnectFromAcumatica();
        
        if (result.success) {
          this.notificationSystem.addNotification('Disconnected from Acumatica.', 'success');
          this.updateConnectionIcon();
          this.showConnectionPopup(); // Refresh popup
          
          // Dispatch event
          document.dispatchEvent(new CustomEvent('acumaticaConnectionChanged', { 
            detail: { connected: false } 
          }));
        } else {
          this.notificationSystem.addNotification(`Failed to disconnect: ${result.error}`, 'error');
          disconnectAcumaticaBtn.textContent = 'Disconnect';
          disconnectAcumaticaBtn.disabled = false;
        }
      });
    }
    
    // Monday.com form
    const mondayForm = document.getElementById('mondayForm');
    if (mondayForm) {
      mondayForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const apiKey = document.getElementById('mondayApiKey').value;
        
        if (!apiKey) {
          this.notificationSystem.addNotification('Please enter your Monday.com API Key.', 'error');
          return;
        }
        
        // Disable form
        const submitBtn = document.getElementById('connectMondayBtn');
        submitBtn.disabled = true;
        submitBtn.textContent = 'Connecting...';
        
        // Connect
        const result = await connectionManager.connectToMonday(apiKey);
        
        // Handle result
        if (result.success) {
          this.notificationSystem.addNotification('Successfully connected to Monday.com.', 'success');
          this.updateConnectionIcon();
          this.showConnectionPopup(); // Refresh popup
        } else {
          this.notificationSystem.addNotification(`Failed to connect: ${result.error}`, 'error');
          
          // Re-enable form
          submitBtn.disabled = false;
          submitBtn.textContent = 'Connect';
        }
      });
    }
    
    // Monday.com disconnect button
    const disconnectMondayBtn = document.getElementById('disconnectMondayBtn');
    if (disconnectMondayBtn) {
      disconnectMondayBtn.addEventListener('click', async () => {
        disconnectMondayBtn.textContent = 'Disconnecting...';
        disconnectMondayBtn.disabled = true;
        
        const result = await connectionManager.disconnectFromMonday();
        
        if (result.success) {
          this.notificationSystem.addNotification('Disconnected from Monday.com.', 'success');
          this.updateConnectionIcon();
          this.showConnectionPopup(); // Refresh popup
        } else {
          this.notificationSystem.addNotification(`Failed to disconnect: ${result.error}`, 'error');
          disconnectMondayBtn.textContent = 'Disconnect';
          disconnectMondayBtn.disabled = false;
        }
      });
    }

    // ShipWave form
    const shipWaveForm = document.getElementById('shipWaveForm');
    if (shipWaveForm) {
      shipWaveForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const apiKey = document.getElementById('shipWaveApiKey').value;
        
        if (!apiKey) {
          this.notificationSystem.addNotification('Please enter your ShipWave API Key.', 'error');
          return;
        }
        
        // Disable form
        const submitBtn = document.getElementById('connectShipWaveBtn');
        submitBtn.disabled = true;
        submitBtn.textContent = 'Connecting...';
        
        // Connect
        const result = await connectionManager.connectToShipWave(apiKey);
        
        // Handle result
        if (result.success) {
          this.notificationSystem.addNotification('Successfully connected to ShipWave.', 'success');
          this.updateConnectionIcon();
          this.showConnectionPopup(); // Refresh popup
        } else {
          this.notificationSystem.addNotification(`Failed to connect: ${result.error}`, 'error');
          
          // Re-enable form
          submitBtn.disabled = false;
          submitBtn.textContent = 'Connect';
        }
      });
    }
    
    // ShipWave disconnect button
    const disconnectShipWaveBtn = document.getElementById('disconnectShipWaveBtn');
    if (disconnectShipWaveBtn) {
      disconnectShipWaveBtn.addEventListener('click', async () => {
        disconnectShipWaveBtn.textContent = 'Disconnecting...';
        disconnectShipWaveBtn.disabled = true;
        
        const result = await connectionManager.disconnectFromShipWave();
        
        if (result.success) {
          this.notificationSystem.addNotification('Disconnected from ShipWave.', 'success');
          this.updateConnectionIcon();
          this.showConnectionPopup(); // Refresh popup
        } else {
          this.notificationSystem.addNotification(`Failed to disconnect: ${result.error}`, 'error');
          disconnectShipWaveBtn.textContent = 'Disconnect';
          disconnectShipWaveBtn.disabled = false;
        }
      });
    }
    
    // ShipWave update API key button
    const updateShipWaveBtn = document.getElementById('updateShipWaveBtn');
    if (updateShipWaveBtn) {
      updateShipWaveBtn.addEventListener('click', () => {
        // Create a modal for updating the API key
        const updateModal = document.createElement('div');
        updateModal.id = 'updateShipWaveKeyModal';
        updateModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex justify-center z-50 overflow-hidden';
        updateModal.style.cssText = 'padding-top: 75px; padding-bottom: 75px;';
        
        updateModal.innerHTML = `
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-80 flex flex-col" style="max-height: 450px; height: 450px;">
            <div class="p-4 pb-2 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 rounded-t-lg">
              <div class="text-lg font-semibold">Update ShipWave API Key</div>
            </div>
            <div class="p-4 overflow-y-auto flex-grow">
              <form id="updateShipWaveKeyForm">
                <div>
                  <label class="block text-xs mb-1">New API Key</label>
                  <input type="text" id="newShipWaveApiKey" class="w-full px-2 py-1 text-sm border rounded-md dark:bg-gray-800 dark:border-gray-600">
                </div>
                <div class="flex justify-end space-x-2 mt-4">
                  <button type="button" id="cancelShipWaveUpdateBtn" class="px-3 py-1 bg-gray-300 text-gray-800 rounded-md text-sm hover:bg-gray-400">
                    Cancel
                  </button>
                  <button type="submit" id="confirmShipWaveUpdateBtn" class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                    Update
                  </button>
                </div>
              </form>
            </div>
          </div>
        `;
        
        document.body.appendChild(updateModal);
        
        // Add cancel button event
        document.getElementById('cancelShipWaveUpdateBtn').addEventListener('click', () => {
          updateModal.remove();
        });
        
        // Add form submit event
        document.getElementById('updateShipWaveKeyForm').addEventListener('submit', async (e) => {
          e.preventDefault();
          
          const apiKey = document.getElementById('newShipWaveApiKey').value;
          
          if (!apiKey) {
            this.notificationSystem.addNotification('Please enter a valid API Key.', 'error');
            return;
          }
          
          // Disable form
          const submitBtn = document.getElementById('confirmShipWaveUpdateBtn');
          submitBtn.disabled = true;
          submitBtn.textContent = 'Updating...';
          
          // Update connection
          const result = await connectionManager.connectToShipWave(apiKey);
          
          if (result.success) {
            this.notificationSystem.addNotification('ShipWave API Key updated successfully.', 'success');
            updateModal.remove();
            this.showConnectionPopup(); // Refresh popup
          } else {
            this.notificationSystem.addNotification(`Failed to update: ${result.error}`, 'error');
            
            // Re-enable form
            submitBtn.disabled = false;
            submitBtn.textContent = 'Update';
          }
        });
        
        // Close when clicking outside
        updateModal.addEventListener('click', (e) => {
          if (e.target === updateModal) {
            updateModal.remove();
          }
        });
      });
    }
    
    // Freight Sample form
    const freightSampleForm = document.getElementById('freightSampleForm');
    if (freightSampleForm) {
      freightSampleForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const token = document.getElementById('freightSampleToken').value;
        
        if (!token) {
          this.notificationSystem.addNotification('Please enter your Freight Sample Bearer Token.', 'error');
          return;
        }
        
        // Disable form
        const submitBtn = document.getElementById('connectFreightSampleBtn');
        submitBtn.disabled = true;
        submitBtn.textContent = 'Connecting...';
        
        // Connect
        const result = await connectionManager.connectToFreightSample(token);
        
        // Handle result
        if (result.success) {
          this.notificationSystem.addNotification('Successfully connected to Freight Sample.', 'success');
          this.updateConnectionIcon();
          this.showConnectionPopup(); // Refresh popup
        } else {
          this.notificationSystem.addNotification(`Failed to connect: ${result.error}`, 'error');
          
          // Re-enable form
          submitBtn.disabled = false;
          submitBtn.textContent = 'Connect';
        }
      });
    }
    
    // Freight Sample disconnect button
    const disconnectFreightSampleBtn = document.getElementById('disconnectFreightSampleBtn');
    if (disconnectFreightSampleBtn) {
      disconnectFreightSampleBtn.addEventListener('click', async () => {
        disconnectFreightSampleBtn.textContent = 'Disconnecting...';
        disconnectFreightSampleBtn.disabled = true;
        
        const result = await connectionManager.disconnectFromFreightSample();
        
        if (result.success) {
          this.notificationSystem.addNotification('Disconnected from Freight Sample.', 'success');
          this.updateConnectionIcon();
          this.showConnectionPopup(); // Refresh popup
        } else {
          this.notificationSystem.addNotification(`Failed to disconnect: ${result.error}`, 'error');
          disconnectFreightSampleBtn.textContent = 'Disconnect';
          disconnectFreightSampleBtn.disabled = false;
        }
      });
    }
    
    // Freight Sample update token button
    const updateFreightSampleBtn = document.getElementById('updateFreightSampleBtn');
    if (updateFreightSampleBtn) {
      updateFreightSampleBtn.addEventListener('click', () => {
        // Create a modal for updating the token
        const updateModal = document.createElement('div');
        updateModal.id = 'updateFreightSampleTokenModal';
        updateModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex justify-center z-50 overflow-hidden';
        updateModal.style.cssText = 'padding-top: 75px; padding-bottom: 75px;';
        
        updateModal.innerHTML = `
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-80 flex flex-col" style="max-height: 450px; height: 450px;">
            <div class="p-4 pb-2 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 rounded-t-lg">
              <div class="text-lg font-semibold">Update Freight Sample Token</div>
            </div>
            <div class="p-4 overflow-y-auto flex-grow">
              <form id="updateFreightSampleTokenForm">
                <div>
                  <label class="block text-xs mb-1">New Bearer Token</label>
                  <input type="text" id="newFreightSampleToken" class="w-full px-2 py-1 text-sm border rounded-md dark:bg-gray-800 dark:border-gray-600">
                </div>
                <div class="flex justify-end space-x-2 mt-4">
                  <button type="button" id="cancelFreightSampleUpdateBtn" class="px-3 py-1 bg-gray-300 text-gray-800 rounded-md text-sm hover:bg-gray-400">
                    Cancel
                  </button>
                  <button type="submit" id="confirmFreightSampleUpdateBtn" class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                    Update
                  </button>
                </div>
              </form>
            </div>
          </div>
        `;
        
        document.body.appendChild(updateModal);
        
        // Add cancel button event
        document.getElementById('cancelFreightSampleUpdateBtn').addEventListener('click', () => {
          updateModal.remove();
        });
        
        // Add form submit event
        document.getElementById('updateFreightSampleTokenForm').addEventListener('submit', async (e) => {
          e.preventDefault();
          
          const token = document.getElementById('newFreightSampleToken').value;
          
          if (!token) {
            this.notificationSystem.addNotification('Please enter a valid Bearer Token.', 'error');
            return;
          }
          
          // Disable form
          const submitBtn = document.getElementById('confirmFreightSampleUpdateBtn');
          submitBtn.disabled = true;
          submitBtn.textContent = 'Updating...';
          
          // Update connection
          const result = await connectionManager.connectToFreightSample(token);
          
          if (result.success) {
            this.notificationSystem.addNotification('Freight Sample Token updated successfully.', 'success');
            updateModal.remove();
            this.showConnectionPopup(); // Refresh popup
          } else {
            this.notificationSystem.addNotification(`Failed to update: ${result.error}`, 'error');
            
            // Re-enable form
            submitBtn.disabled = false;
            submitBtn.textContent = 'Update';
          }
        });
        
        // Close when clicking outside
        updateModal.addEventListener('click', (e) => {
          if (e.target === updateModal) {
            updateModal.remove();
          }
        });
      });
    }
    
    // Monday.com update API key button
    const updateMondayBtn = document.getElementById('updateMondayBtn');
    if (updateMondayBtn) {
      updateMondayBtn.addEventListener('click', () => {
        // Create a modal for updating the API key
        const updateModal = document.createElement('div');
        updateModal.id = 'updateMondayKeyModal';
        updateModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex justify-center z-50 overflow-hidden';
        updateModal.style.cssText = 'padding-top: 75px; padding-bottom: 75px;';
        
        updateModal.innerHTML = `
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-80 flex flex-col" style="max-height: 450px; height: 450px;">
            <div class="p-4 pb-2 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 rounded-t-lg">
              <div class="text-lg font-semibold">Update Monday.com API Key</div>
            </div>
            <div class="p-4 overflow-y-auto flex-grow">
              <form id="updateMondayKeyForm">
                <div>
                  <label class="block text-xs mb-1">New API Key</label>
                  <input type="text" id="newMondayApiKey" class="w-full px-2 py-1 text-sm border rounded-md dark:bg-gray-800 dark:border-gray-600">
                </div>
                <div class="flex justify-end space-x-2 mt-4">
                  <button type="button" id="cancelUpdateBtn" class="px-3 py-1 bg-gray-300 text-gray-800 rounded-md text-sm hover:bg-gray-400">
                    Cancel
                  </button>
                  <button type="submit" id="confirmUpdateBtn" class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                    Update
                  </button>
                </div>
              </form>
            </div>
          </div>
        `;
        
        document.body.appendChild(updateModal);
        
        // Fetch and populate current API key if available
        chrome.storage.local.get(['user'], (data) => {
          if (data.user && data.user["Monday API Key"]) {
            const newMondayApiKeyInput = document.getElementById('newMondayApiKey');
            if (newMondayApiKeyInput) {
              newMondayApiKeyInput.value = data.user["Monday API Key"];
            }
          }
        });
        
        // Add cancel button event
        document.getElementById('cancelUpdateBtn').addEventListener('click', () => {
          updateModal.remove();
        });
        
        // Add form submit event
        document.getElementById('updateMondayKeyForm').addEventListener('submit', async (e) => {
          e.preventDefault();
          
          const apiKey = document.getElementById('newMondayApiKey').value;
          
          if (!apiKey) {
            this.notificationSystem.addNotification('Please enter a valid API Key.', 'error');
            return;
          }
          
          // Disable form
          const submitBtn = document.getElementById('confirmUpdateBtn');
          submitBtn.disabled = true;
          submitBtn.textContent = 'Updating...';
          
          // Update connection
          const result = await connectionManager.connectToMonday(apiKey);
          
          if (result.success) {
            this.notificationSystem.addNotification('Monday.com API Key updated successfully.', 'success');
            updateModal.remove();
            this.showConnectionPopup(); // Refresh popup
          } else {
            this.notificationSystem.addNotification(`Failed to update: ${result.error}`, 'error');
            
            // Re-enable form
            submitBtn.disabled = false;
            submitBtn.textContent = 'Update';
          }
        });
        
        // Close when clicking outside
        updateModal.addEventListener('click', (e) => {
          if (e.target === updateModal) {
            updateModal.remove();
          }
        });
      });
    }
    
    // Dymo connect button
    const connectDymoBtn = document.getElementById('connectDymoBtn');
    if (connectDymoBtn) {
      connectDymoBtn.addEventListener('click', async () => {
        connectDymoBtn.textContent = 'Connecting...';
        connectDymoBtn.disabled = true;
        
        // Connect to Dymo with placeholder implementation
        const result = await connectionManager.connectToDymo({
          initialized: true,
          printerName: 'DYMO LabelWriter'
        });
        
        if (result.success) {
          this.notificationSystem.addNotification('Successfully connected to Dymo Labeler.', 'success');
          this.updateConnectionIcon();
          this.showConnectionPopup(); // Refresh popup
        } else {
          this.notificationSystem.addNotification(`Failed to connect: ${result.error}`, 'error');
          connectDymoBtn.textContent = 'Connect';
          connectDymoBtn.disabled = false;
        }
      });
    }
    
    // Dymo disconnect button
    const disconnectDymoBtn = document.getElementById('disconnectDymoBtn');
    if (disconnectDymoBtn) {
      disconnectDymoBtn.addEventListener('click', async () => {
        disconnectDymoBtn.textContent = 'Disconnecting...';
        disconnectDymoBtn.disabled = true;
        
        const result = await connectionManager.disconnectFromDymo();
        
        if (result.success) {
          this.notificationSystem.addNotification('Disconnected from Dymo Labeler.', 'success');
          this.updateConnectionIcon();
          this.showConnectionPopup(); // Refresh popup
        } else {
          this.notificationSystem.addNotification(`Failed to disconnect: ${result.error}`, 'error');
          disconnectDymoBtn.textContent = 'Disconnect';
          disconnectDymoBtn.disabled = false;
        }
      });
    }
    
    // Microsoft services buttons
    const connectOutlookBtn = document.getElementById('connectOutlookBtn');
    if (connectOutlookBtn) {
      connectOutlookBtn.addEventListener('click', () => {
        this.notificationSystem.addNotification('Microsoft authentication coming soon!', 'info');
      });
    }
    
    const connectTeamsBtn = document.getElementById('connectTeamsBtn');
    if (connectTeamsBtn) {
      connectTeamsBtn.addEventListener('click', () => {
        this.notificationSystem.addNotification('Microsoft authentication coming soon!', 'info');
      });
    }
  }

  removeExistingPopup() {
    const existingPopup = document.getElementById('connectionPopup');
    if (existingPopup) {
      existingPopup.remove();
      this.isPopupOpen = false;
    }
  }
}

// Create singleton instance
export const connectionUI = new ConnectionUI();