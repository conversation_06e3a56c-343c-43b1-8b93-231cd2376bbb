/**
 * File Protection Module
 * Handles password protection and encryption/decryption of Excel files
 */

// START of new encoding/decoding logic
const baseMapping = {
  'A': '73', 'B': '28', 'C': '96', 'D': '41', 'E': '59', 'F': '82', 'G': '37', 'H': '64',
  'I': '15', 'J': '93', 'K': '52', 'L': '07', 'M': '48', 'N': '21', 'O': '69', 'P': '34',
  'Q': '85', 'R': '62', 'S': '19', 'T': '76', 'U': '43', 'V': '90', 'W': '25', 'X': '58',
  'Y': '81', 'Z': '36',
  'a': '74', 'b': '29', 'c': '97', 'd': '42', 'e': '60', 'f': '83', 'g': '38', 'h': '65',
  'i': '16', 'j': '94', 'k': '53', 'l': '08', 'm': '49', 'n': '22', 'o': '70', 'p': '35',
  'q': '86', 'r': '63', 's': '20', 't': '77', 'u': '44', 'v': '91', 'w': '26', 'x': '57',
  'y': '80', 'z': '39',
  '0': '17', '1': '95', '2': '54', '3': '09', '4': '66', '5': '31', '6': '88', '7': '47',
  '8': '02', '9': '75',
  ' ': '00', '.': '24', ',': '51', '!': '68', '?': '13', '@': '89', '#': '32', '$': '79',
  '%': '46', '^': '03', '&': '92', '*': '55', '(': '10', ')': '67', '-': '84', '_': '40',
  '+': '27', '=': '71', '<': '56', '>': '23', '/': '99', '\\': '45', '\'': '78', '"': '14',
  // Added mappings for JSON structural characters
  '{': '01',
  '}': '04',
  '[': '05',
  ']': '06',
  ':': '11'
};

function _fpApplyShift(code, position) {
  const shiftValue = (position % 5) + 1;
  const firstDigit = parseInt(code[0]);
  const secondDigit = parseInt(code[1]);
  const newFirstDigit = (firstDigit + shiftValue) % 10;
  const newSecondDigit = (secondDigit + (position % 3)) % 10;
  return `${newFirstDigit}${newSecondDigit}`;
}

function _fpInsertDecoys(encodedMessage) {
  let result = '';
  for (let i = 0; i < encodedMessage.length; i++) {
    result += encodedMessage[i];
    if ((i + 1) % 6 === 0 && i < encodedMessage.length - 1) {
      const decoy = (i * 7) % 10;
      result += decoy;
    }
  }
  return result;
}

function _fpReverseSegments(encodedMessage) {
  const segmentSize = 8;
  let result = '';
  for (let i = 0; i < encodedMessage.length; i += segmentSize) {
    const segment = encodedMessage.slice(i, Math.min(i + segmentSize, encodedMessage.length));
    if ((i / segmentSize) % 2 === 1) {
      result += segment.split('').reverse().join('');
    } else {
      result += segment;
    }
  }
  return result;
}

function _fpEncodeMessage(message) {
  let encoded = '';
  for (let i = 0; i < message.length; i++) {
    const char = message[i];
    if (baseMapping[char]) {
      const code = baseMapping[char];
      const shiftedCode = _fpApplyShift(code, i);
      encoded += shiftedCode;
    } else {
      encoded += '50'; // Default code for unmapped characters
    }
  }
  encoded = _fpInsertDecoys(encoded);
  encoded = _fpReverseSegments(encoded);
  return encoded;
}

const _fpReverseBaseMapping = {};
for (const key in baseMapping) {
  if (Object.prototype.hasOwnProperty.call(baseMapping, key)) {
    _fpReverseBaseMapping[baseMapping[key]] = key;
  }
}

const _fpUnreverseSegments = _fpReverseSegments; // Reversing segments twice restores original

function _fpRemoveDecoys(messageWithDecoys) {
  let originalMessage = '';
  let i = 0;
  while (i < messageWithDecoys.length) {
    const dataSegment = messageWithDecoys.substring(i, Math.min(i + 6, messageWithDecoys.length));
    originalMessage += dataSegment;
    i += dataSegment.length;
    if (dataSegment.length === 6 && i < messageWithDecoys.length) {
      i += 1; // Skip the decoy
    }
  }
  return originalMessage;
}

function _fpUnapplyShift(shiftedCode, position) {
  const shiftValue = (position % 5) + 1;
  const firstDigit = parseInt(shiftedCode[0]);
  const secondDigit = parseInt(shiftedCode[1]);
  const originalFirstDigit = (firstDigit - shiftValue + 10) % 10;
  const originalSecondDigit = (secondDigit - (position % 3) + 10) % 10;
  return `${originalFirstDigit}${originalSecondDigit}`;
}

function _fpDecodeMessage(encodedMessage) {
  let message = _fpUnreverseSegments(encodedMessage);
  message = _fpRemoveDecoys(message);

  let decodedText = '';
  let originalCharPosition = 0;
  for (let i = 0; i < message.length; i += 2) {
    const shiftedCodePair = message.substring(i, i + 2);
    if (shiftedCodePair.length < 2) continue;

    const originalCode = _fpUnapplyShift(shiftedCodePair, originalCharPosition);
    decodedText += (_fpReverseBaseMapping[originalCode] || '?'); // Use '?' for unmappable codes
    originalCharPosition++;
  }
  return decodedText;
}
// END of new encoding/decoding logic

class FileProtection {
    /**
     * Initialize the file protection module
     */
    constructor() {
        this.setupEventListeners();
        console.log('File Protection module initialized');

        // Make the showPasswordModal method globally accessible
        window.showPasswordProtectionModal = () => this.showPasswordModal();
    }

    /**
     * Set up event listeners for the lock button
     */
    setupEventListeners() {
        // Add event listener immediately if document is already loaded
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            this.attachLockButtonListener();
        } else {
            // Otherwise wait for DOMContentLoaded
            document.addEventListener('DOMContentLoaded', () => {
                this.attachLockButtonListener();
            });
        }

        // Also set up a direct event listener on the document
        document.addEventListener('click', (e) => {
            if (e.target && (e.target.id === 'lockFileBtn' ||
                (e.target.parentElement && e.target.parentElement.id === 'lockFileBtn'))) {
                console.log('Lock button clicked via document event delegation');
                e.preventDefault();
                e.stopPropagation();
                this.showPasswordModal();
            }
        });
    }

    /**
     * Attach event listener to the lock button
     */
    attachLockButtonListener() {
        const lockBtn = document.getElementById('lockFileBtn');
        if (lockBtn) {
            console.log('Lock button found, attaching direct event listener');

            // Remove any existing event listeners to prevent duplicates
            const newLockBtn = lockBtn.cloneNode(true);
            lockBtn.parentNode.replaceChild(newLockBtn, lockBtn);

            // Add the event listener
            newLockBtn.addEventListener('click', (e) => {
                console.log('Lock button clicked from file-protection.js');
                e.preventDefault();
                e.stopPropagation();
                this.showPasswordModal();
            });

            // Store a reference to the button
            this.lockBtn = newLockBtn;

            // Add a global click handler as a fallback
            window.lockFileButtonClick = () => {
                console.log('Lock button clicked via global handler');
                this.showPasswordModal();
            };

            // Add the onclick attribute directly
            newLockBtn.setAttribute('onclick', 'window.lockFileButtonClick()');
        } else {
            console.warn('Lock button not found in the DOM');
            // Try again after a short delay in case the button is added dynamically
            setTimeout(() => {
                const retryLockBtn = document.getElementById('lockFileBtn');
                if (retryLockBtn) {
                    console.log('Lock button found on retry, attaching event listener');

                    // Remove any existing event listeners to prevent duplicates
                    const newLockBtn = retryLockBtn.cloneNode(true);
                    retryLockBtn.parentNode.replaceChild(newLockBtn, retryLockBtn);

                    // Add the event listener
                    newLockBtn.addEventListener('click', (e) => {
                        console.log('Lock button clicked from file-protection.js (retry)');
                        e.preventDefault();
                        e.stopPropagation();
                        this.showPasswordModal();
                    });

                    // Store a reference to the button
                    this.lockBtn = newLockBtn;

                    // Add a global click handler as a fallback
                    window.lockFileButtonClick = () => {
                        console.log('Lock button clicked via global handler');
                        this.showPasswordModal();
                    };

                    // Add the onclick attribute directly
                    newLockBtn.setAttribute('onclick', 'window.lockFileButtonClick()');
                }
            }, 1000);
        }
    }

    /**
     * Show the password protection modal
     */
    showPasswordModal() {
        console.log('showPasswordModal called');

        // Check if workbook exists
        if (!window.workbook) {
            console.error('No workbook found in window.workbook');
            this.showMessage('Please open a workbook first', 'error');
            return;
        }

        // Log workbook details for debugging
        console.log('Current workbook:', window.workbook);
        console.log('Workbook type:', typeof window.workbook);
        console.log('Showing password protection modal for workbook:', window.workbook.originalFilename || 'Unnamed workbook');

        // Import the standardized modal functions
        import('./standardized-modal.js').then(module => {
            // Create a standardized password modal
            const modal = module.createPasswordModal({
                onProtect: async () => {
                    const password = document.getElementById('password').value;
                    const confirmPassword = document.getElementById('confirmPassword').value;
                    const errorBox = document.getElementById('passwordError');

                    // Function to show error in the modal
                    const showPasswordError = (message) => {
                        errorBox.textContent = message;
                        errorBox.style.display = 'block';
                    };

                    // Hide any previous error
                    errorBox.style.display = 'none';

                    if (!password) {
                        showPasswordError('Please enter a password');
                        document.getElementById('password').focus();
                        return;
                    }

                    if (password !== confirmPassword) {
                        showPasswordError('Passwords do not match');
                        document.getElementById('confirmPassword').focus();
                        return;
                    }

                    // Show a loading message
                    this.showMessage('Preparing protected file...', 'info');

                    // Disable the export button to prevent multiple clicks
                    const exportBtn = document.getElementById('exportProtectedBtn');
                    exportBtn.disabled = true;
                    exportBtn.innerHTML = '<span style="display: inline-block; margin-right: 8px;">Exporting...</span><span class="loading-spinner"></span>';

                    // Add loading spinner style
                    if (!document.getElementById('spinnerAnimation')) {
                        const style = document.createElement('style');
                        style.id = 'spinnerAnimation';
                        style.textContent = `
                            .loading-spinner {
                                display: inline-block;
                                width: 16px;
                                height: 16px;
                                border: 2px solid rgba(255,255,255,0.3);
                                border-radius: 50%;
                                border-top-color: white;
                                animation: spin 1s linear infinite;
                            }
                            @keyframes spin {
                                to { transform: rotate(360deg); }
                            }
                        `;
                        document.head.appendChild(style);
                    }

                    try {
                        const success = await this.protectAndExportFile(password);
                        if (success) {
                            // Close the modal only if export was successful
                            modal.close();
                        } else {
                            // Show error in the modal
                            showPasswordError('Failed to export protected file. Please try again.');

                            // Re-enable the export button if export failed
                            exportBtn.disabled = false;
                            exportBtn.textContent = 'Protect & Export';
                        }
                    } catch (error) {
                        console.error('Error protecting file:', error);

                        // Show error in the modal
                        showPasswordError('Error: ' + error.message);

                        // Re-enable the export button
                        exportBtn.disabled = false;
                        exportBtn.textContent = 'Protect & Export';
                    }
                }
            });
        }).catch(error => {
            console.error('Error loading standardized-modal.js:', error);
            this.showMessage('Error showing password modal', 'error');
        });
    }

    /**
     * Protect and export the file with password and new custom encoding
     * @param {string} password - The password to protect the file
     */
    async protectAndExportFile(password) {
        try {
            this.showMessage('Preparing protected file with custom encoding...', 'info');
            console.log('Workbook for protection:', window.workbook);

            if (!window.workbook) {
                console.error('No workbook found in window.workbook for protection.');
                throw new Error('No workbook found. Please open or create a workbook first.');
            }

            // 1. Convert workbook to a JSON-compatible object (excluding security for now)
            let workbookContentForEncoding = await this.convertWorkbookToJson(window.workbook);
            if (workbookContentForEncoding && workbookContentForEncoding.security) {
                delete workbookContentForEncoding.security; // Remove old security if present
            }

            const jsonStringToEncode = JSON.stringify(workbookContentForEncoding);
            console.log('JSON string to be custom encoded, length:', jsonStringToEncode.length);
            if (jsonStringToEncode.length === 0) {
                console.warn("The JSON string to encode is empty. Exporting an empty encoded content.");
            }

            // 2. Encode the JSON string using the new custom method
            const encodedContent = this.customEncode(jsonStringToEncode);
            console.log('Custom encoded content string, length:', encodedContent.length);

            // 3. Prepare the final JSON object for the file
            const finalOutputObject = {
                encodedSheetData: encodedContent,
                security: {
                    passwordHash: this.hashPassword(password),
                    protected: true,
                    timestamp: new Date().toISOString(),
                    version: 2 // Indicate new protection scheme
                }
            };

            // 4. Create a blob with the final JSON data
            const finalJsonString = JSON.stringify(finalOutputObject, null, 2);
            console.log('Final JSON string for blob, length:', finalJsonString.length);
            const jsonBlob = new Blob([finalJsonString], { type: 'application/json' });
            console.log('Final JSON blob created, size:', jsonBlob.size);

            const originalFilename = window.workbook.originalFilename || 'spreadsheet.xlsx';
            const baseFilename = originalFilename.replace(/\\.(xlsx|xls|xlsm|envent)$/, '');
            const protectedFilename = `${baseFilename}.xlsm`;

            console.log(`Exporting custom protected file as: ${protectedFilename}`);

            const url = URL.createObjectURL(jsonBlob);
            const a = document.createElement('a');
            a.href = url;
            a.download = protectedFilename;
            document.body.appendChild(a);
a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.showMessage(`File "${protectedFilename}" protected and exported successfully.`, 'success', 7000);

            try {
                const storageManagerModule = await import('./storage_manager.js');
                storageManagerModule.default.addRecentFile(protectedFilename, protectedFilename);
                console.log('Added custom protected file to recent files');
            } catch (storageError) {
                console.error('Error adding custom protected file to recent files:', storageError);
            }
            return true;

        } catch (error) {
            console.error('Error in custom protectAndExportFile:', error);
            this.showMessage(`Error protecting file: ${error.message}`, 'error', 7000);
            // Propagate error for modal UI if applicable
            if (typeof window.showPasswordError === 'function') {
                window.showPasswordError('Error protecting file: ' + error.message);
            }
            return false;
        }
    }

    // Add customEncode and customDecode to the prototype
    customEncode(message) {
        return _fpEncodeMessage(message);
    }

    customDecode(encodedMessage) {
        return _fpDecodeMessage(encodedMessage);
    }

    /**
     * Convert workbook to JSON format
     * @param {Object} workbook - The workbook to convert
     * @returns {Object} - JSON representation of the workbook
     */
    async convertWorkbookToJson(workbook) {
        console.log('Converting workbook to JSON format...');

        // Determine which Excel library is being used
        let activeLibrary = 'unknown';
        try {
            if (window.utils && window.utils.excel && window.utils.excel.activeLibrary) {
                activeLibrary = window.utils.excel.activeLibrary;
                console.log(`Active Excel library from utils: ${activeLibrary}`);
            } else {
                // Try to determine the library type from the workbook object
                if (workbook.sheets && typeof workbook.sheets === 'function') {
                    activeLibrary = 'xlsx-populate';
                    console.log('Detected xlsx-populate library from workbook structure');
                } else if (workbook.SheetNames && Array.isArray(workbook.SheetNames)) {
                    activeLibrary = 'xlsx';
                    console.log('Detected xlsx library from workbook structure');
                } else if (workbook.worksheets && typeof workbook.worksheets === 'object') {
                    activeLibrary = 'exceljs';
                    console.log('Detected exceljs library from workbook structure');
                } else {
                    console.log('Could not determine Excel library, using default conversion');
                    activeLibrary = 'xlsx-populate'; // Default to xlsx-populate
                }
            }
        } catch (error) {
            console.error('Error determining active Excel library:', error);
            activeLibrary = 'xlsx-populate'; // Default to xlsx-populate
        }

        console.log(`Using Excel library for conversion: ${activeLibrary}`);

        const result = {
            sheets: [],
            properties: {
                title: workbook.title || '',
                author: workbook.author || '',
                subject: workbook.subject || '',
                keywords: workbook.keywords || '',
                category: workbook.category || '',
                createdAt: new Date().toISOString(),
                originalFilename: workbook.originalFilename || 'spreadsheet.xlsx'
            }
        };

        try {
            // Handle different Excel libraries
            switch (activeLibrary) {
                case 'xlsx-populate':
                    return await this.convertXlsxPopulateWorkbookToJson(workbook, result);
                case 'xlsx':
                    return await this.convertXlsxWorkbookToJson(workbook, result);
                case 'exceljs':
                    return await this.convertExcelJsWorkbookToJson(workbook, result);
                default:
                    console.log(`Using default conversion method for library: ${activeLibrary}`);
                    return await this.convertXlsxPopulateWorkbookToJson(workbook, result);
            }
        } catch (error) {
            console.error('Error converting workbook to JSON:', error);
            throw new Error(`Failed to convert workbook to JSON: ${error.message}`);
        }
    }

    /**
     * Convert XlsxPopulate workbook to JSON
     * @param {Object} workbook - The XlsxPopulate workbook
     * @param {Object} result - The result object to populate
     * @returns {Object} - JSON representation of the workbook
     */
    async convertXlsxPopulateWorkbookToJson(workbook, result) {
        console.log('Using XlsxPopulate conversion method');

        try {
            // Check if workbook has sheets method
            if (!workbook.sheets || typeof workbook.sheets !== 'function') {
                console.warn('Workbook does not have sheets method, creating a simple representation');

                // Create a simple representation with a single sheet
                const sheetData = {
                    name: 'Sheet1',
                    index: 0,
                    data: [[]],
                    styles: [[]]
                };

                // Try to extract some data if possible
                try {
                    if (workbook.sheet && typeof workbook.sheet === 'function') {
                        const sheet = workbook.sheet(0);
                        if (sheet) {
                            sheetData.name = sheet.name ? sheet.name() : 'Sheet1';

                            // Try to get some data
                            if (sheet.cell && typeof sheet.cell === 'function') {
                                const data = [];
                                const styles = [];

                                // Extract a small sample of data (10x10 grid)
                                for (let r = 1; r <= 10; r++) {
                                    const rowData = [];
                                    const rowStyles = [];

                                    for (let c = 1; c <= 10; c++) {
                                        try {
                                            const cell = sheet.cell(r, c);
                                            rowData.push(cell.value ? cell.value() : null);

                                            // Extract basic cell styles
                                            rowStyles.push({
                                                bold: false,
                                                italic: false,
                                                underline: false,
                                                fontColor: null,
                                                fill: null,
                                                horizontalAlignment: 'left',
                                                verticalAlignment: 'middle',
                                                fontSize: 11,
                                                fontFamily: 'Calibri',
                                                numberFormat: 'General'
                                            });
                                        } catch (cellError) {
                                            rowData.push(null);
                                            rowStyles.push({});
                                        }
                                    }

                                    data.push(rowData);
                                    styles.push(rowStyles);
                                }

                                if (data.length > 0) {
                                    sheetData.data = data;
                                    sheetData.styles = styles;
                                }
                            }
                        }
                    }
                } catch (extractError) {
                    console.error('Error extracting data from workbook:', extractError);
                }

                result.sheets.push(sheetData);
                return result;
            }

            // Get all sheets
            const sheets = workbook.sheets();
            console.log(`Found ${sheets.length} sheets in workbook`);

            // Process each sheet
            for (let i = 0; i < sheets.length; i++) {
                const sheet = sheets[i];
                const sheetData = {
                    name: sheet.name(),
                    index: i,
                    data: [],
                    styles: []
                };

                console.log(`Processing sheet: ${sheetData.name}`);

                try {
                    // Get sheet data range
                    const range = sheet.usedRange();

                    if (!range) {
                        console.log(`Sheet ${sheetData.name} is empty, adding empty data`);
                        result.sheets.push(sheetData);
                        continue;
                    }

                    const startRow = range.startCell().rowNumber();
                    const startCol = range.startCell().columnNumber();
                    const endRow = range.endCell().rowNumber();
                    const endCol = range.endCell().columnNumber();

                    console.log(`Sheet range: ${startRow},${startCol} to ${endRow},${endCol}`);

                    // Extract data and styles
                    for (let r = startRow; r <= endRow; r++) {
                        const rowData = [];
                        const rowStyles = [];

                        for (let c = startCol; c <= endCol; c++) {
                            const cell = sheet.cell(r, c);
                            rowData.push(cell.value());

                            // Extract cell styles
                            rowStyles.push({
                                bold: cell.style('bold') || false,
                                italic: cell.style('italic') || false,
                                underline: cell.style('underline') || false,
                                fontColor: cell.style('fontColor') || null,
                                fill: cell.style('fill') || null,
                                horizontalAlignment: cell.style('horizontalAlignment') || 'left',
                                verticalAlignment: cell.style('verticalAlignment') || 'middle',
                                fontSize: cell.style('fontSize') || 11,
                                fontFamily: cell.style('fontFamily') || 'Calibri',
                                numberFormat: cell.style('numberFormat') || 'General'
                            });
                        }

                        sheetData.data.push(rowData);
                        sheetData.styles.push(rowStyles);
                    }
                } catch (rangeError) {
                    console.error(`Error processing sheet range for ${sheetData.name}:`, rangeError);
                    // Add empty data for this sheet
                    sheetData.data = [[]];
                    sheetData.styles = [[]];
                }

                result.sheets.push(sheetData);
            }

            return result;
        } catch (error) {
            console.error('Error in XlsxPopulate conversion:', error);

            // Create a fallback result with minimal data
            console.log('Creating fallback result with minimal data');
            result.sheets.push({
                name: 'Sheet1',
                index: 0,
                data: [[]],
                styles: [[]]
            });

            return result;
        }
    }

    /**
     * Convert XLSX workbook to JSON
     * @param {Object} workbook - The XLSX workbook
     * @param {Object} result - The result object to populate
     * @returns {Object} - JSON representation of the workbook
     */
    async convertXlsxWorkbookToJson(workbook, result) {
        console.log('Using XLSX conversion method');

        try {
            // Process each sheet
            for (let i = 0; i < workbook.SheetNames.length; i++) {
                const sheetName = workbook.SheetNames[i];
                const sheet = workbook.Sheets[sheetName];

                const sheetData = {
                    name: sheetName,
                    index: i,
                    data: [],
                    styles: []
                };

                console.log(`Processing sheet: ${sheetData.name}`);

                // Convert sheet to array of arrays
                const data = XLSX.utils.sheet_to_json(sheet, { header: 1 });

                // Process data and create empty styles
                for (let r = 0; r < data.length; r++) {
                    const rowData = data[r] || [];
                    const rowStyles = Array(rowData.length).fill({
                        bold: false,
                        italic: false,
                        underline: false,
                        fontColor: null,
                        fill: null,
                        horizontalAlignment: 'left',
                        verticalAlignment: 'middle',
                        fontSize: 11,
                        fontFamily: 'Calibri',
                        numberFormat: 'General'
                    });

                    sheetData.data.push(rowData);
                    sheetData.styles.push(rowStyles);
                }

                result.sheets.push(sheetData);
            }

            return result;
        } catch (error) {
            console.error('Error in XLSX conversion:', error);
            throw error;
        }
    }

    /**
     * Convert ExcelJS workbook to JSON
     * @param {Object} workbook - The ExcelJS workbook
     * @param {Object} result - The result object to populate
     * @returns {Object} - JSON representation of the workbook
     */
    async convertExcelJsWorkbookToJson(workbook, result) {
        console.log('Using ExcelJS conversion method');

        try {
            // Process each sheet
            workbook.eachSheet((worksheet, sheetId) => {
                const sheetData = {
                    name: worksheet.name,
                    index: sheetId - 1,
                    data: [],
                    styles: []
                };

                console.log(`Processing sheet: ${sheetData.name}`);

                // Get used range
                const usedRange = worksheet.usedRange;
                if (!usedRange) {
                    console.log(`Sheet ${sheetData.name} is empty, adding empty data`);
                    result.sheets.push(sheetData);
                    return;
                }

                // Process each row
                worksheet.eachRow((row, rowNumber) => {
                    const rowData = [];
                    const rowStyles = [];

                    // Process each cell in the row
                    row.eachCell((cell, colNumber) => {
                        rowData[colNumber - 1] = cell.value;

                        // Extract cell styles
                        const style = {
                            bold: cell.font && cell.font.bold || false,
                            italic: cell.font && cell.font.italic || false,
                            underline: cell.font && cell.font.underline || false,
                            fontColor: cell.font && cell.font.color ? cell.font.color.argb : null,
                            fill: cell.fill && cell.fill.fgColor ? cell.fill.fgColor.argb : null,
                            horizontalAlignment: cell.alignment && cell.alignment.horizontal || 'left',
                            verticalAlignment: cell.alignment && cell.alignment.vertical || 'middle',
                            fontSize: cell.font && cell.font.size || 11,
                            fontFamily: cell.font && cell.font.name || 'Calibri',
                            numberFormat: cell.numFmt || 'General'
                        };

                        rowStyles[colNumber - 1] = style;
                    });

                    sheetData.data[rowNumber - 1] = rowData;
                    sheetData.styles[rowNumber - 1] = rowStyles;
                });

                result.sheets.push(sheetData);
            });

            return result;
        } catch (error) {
            console.error('Error in ExcelJS conversion:', error);
            throw error;
        }
    }

    /**
     * Simple password hashing function
     * @param {string} password - The password to hash
     * @returns {string} - Hashed password
     */
    hashPassword(password) {
        // This is a simple hash for demonstration
        // In a real application, use a proper crypto library
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
        }
        return hash.toString(16);
    }

    /**
     * Check if a file is a protected XLSM file
     * @param {File} file - The file to check
     * @returns {Promise<boolean>} - True if the file is a protected XLSM file
     */
    async isProtectedFile(file) {
        // Check file extension
        if (!file.name.toLowerCase().endsWith('.xlsm')) {
            return false;
        }

        try {
            // Read the file as text
            const text = await this.readFileAsText(file);

            // Try to parse as JSON
            const data = JSON.parse(text);

            // Check if it has the security property and it's marked as protected
            // This check remains valid for both old and new protected files.
            // The new 'version' field in security can distinguish them later if needed.
            return data && data.security && data.security.protected === true;
        } catch (error) {
            // This can happen if the file is not JSON or an error occurs during reading
            console.warn('Error checking if file is protected (isProtectedFile):', error);
            return false;
        }
    }

    /**
     * Read a file as text
     * @param {File} file - The file to read
     * @returns {Promise<string>} - The file contents as text
     */
    async readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = () => reject(reader.error);
            reader.readAsText(file);
        });
    }

    /**
     * Show a message to the user
     * @param {string} message - The message to show
     * @param {string} type - The message type (info, success, error)
     */
    showMessage(message, type = 'info') {
        const statusElement = document.getElementById('status');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = `status-${type}`;

            // Clear the message after 5 seconds
            setTimeout(() => {
                statusElement.textContent = '';
                statusElement.className = '';
            }, 5000);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}

// Export the class
export default FileProtection;
