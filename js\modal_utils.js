/**
 * Creates a modal dialog
 * @param {string} title - The title of the modal
 * @param {string} bodyHtml - The HTML content of the modal
 * @param {Array} footerButtons - Array of button configurations
 * @param {string} maxWidth - Maximum width of the modal
 * @param {Function} onClose - Optional callback function to execute when modal is closed
 * @returns {Object} - Modal object with elements and close method
 */
import { createStandardModal } from './standardized-modal.js';

export function createModal(title, bodyHtml, footerButtons = [], maxWidth = '500px', onClose = null) {
    // Map the button configuration to match standardized-modal format
    const standardizedButtons = footerButtons.map(btn => ({
        text: btn.text,
        primary: btn.isPrimary,
        id: btn.id,
        onClick: btn.onClick,
        closeModal: btn.closesModal !== false
    }));

    // Use the standardized modal implementation
    const modal = createStandardModal({
        title,
        content: bodyHtml,
        buttons: standardizedButtons,
        onClose,
        size: 'custom',
        customWidth: maxWidth
    });

    // Show the modal immediately
    modal.show();

    // Return compatible interface
    return {
        overlayElement: modal.getOverlay(),
        contentBoxElement: modal.getElement(),
        close: modal.close
    };
}