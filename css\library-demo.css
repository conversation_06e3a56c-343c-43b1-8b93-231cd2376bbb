/* Library Demo Styles */
#demoContainer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #f8f9fa;
    z-index: 1000;
    overflow-y: auto;
    padding: 20px;
    box-sizing: border-box;
}

.library-demo-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
}

.library-demo-header h1 {
    font-size: 24px;
    color: #1a73e8;
    margin-bottom: 10px;
}

.library-demo-header p {
    color: #5f6368;
    margin-bottom: 20px;
}

.close-demo-btn {
    background-color: #1a73e8;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
}

.close-demo-btn:hover {
    background-color: #1557b0;
}

.demo-section {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.demo-section h2 {
    font-size: 18px;
    color: #202124;
    margin-top: 0;
    margin-bottom: 10px;
}

.demo-section p {
    color: #5f6368;
    margin-bottom: 15px;
}

.demo-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
}

.demo-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #f1f3f4;
    border: 1px solid #dadce0;
    border-radius: 8px;
    padding: 15px;
    min-width: 120px;
    cursor: pointer;
    transition: all 0.2s;
}

.demo-btn:hover {
    background-color: #e8eaed;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.demo-btn .material-icons {
    font-size: 24px;
    color: #1a73e8;
    margin-bottom: 8px;
}

.demo-btn span:last-child {
    font-size: 14px;
    font-weight: 500;
    color: #202124;
}

.demo-result {
    background-color: #f8f9fa;
    border: 1px solid #dadce0;
    border-radius: 4px;
    padding: 15px;
    min-height: 100px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .demo-controls {
        justify-content: center;
    }
    
    .demo-btn {
        min-width: 100px;
    }
}
