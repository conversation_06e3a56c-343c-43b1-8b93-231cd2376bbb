/**
 * Protected File Opener
 * Handles opening password-protected XLSM files
 */

class ProtectedFileOpener {
    /**
     * Constructor
     */
    constructor() {
        console.log('Protected File Opener initialized');
    }

    /**
     * Open a password-protected file
     * @param {File} file - The file to open
     * @returns {Promise<boolean>} - True if the file was opened successfully
     */
    async openProtectedFile(file) {
        try {
            console.log(`Attempting to open protected file: ${file.name}`);

            // Check file extension
            if (!file.name.toLowerCase().endsWith('.xlsm')) {
                console.log('File is not an XLSM file, skipping password check');
                return false;
            }

            // Read the file as text
            console.log('Reading file as text...');
            const text = await this.readFileAsText(file);
            console.log('File content length:', text.length);
            console.log('First 100 characters:', text.substring(0, 100));

            // Try to parse as JSON
            console.log('Parsing file content as JSON...');
            let data;
            try {
                data = JSON.parse(text);
                console.log('Successfully parsed file as JSON');
            } catch (parseError) {
                console.error('Failed to parse file as JSON:', parseError);
                console.log('This is not a valid protected file (not valid JSON)');
                return false;
            }

            // Check if it has the security property
            if (!data || !data.security || data.security.protected !== true) {
                console.log('File is not a valid protected file (missing security property)');
                return false;
            }

            console.log('File is password protected, showing password prompt');
            console.log('Security info:', data.security);

            // Show password prompt
            return await this.promptForPassword(file, data);
        } catch (error) {
            console.error('Error opening protected file:', error);
            this.showMessage('Error opening protected file: ' + error.message, 'error');
            return false;
        }
    }

    /**
     * Prompt the user for a password
     * @param {File} file - The file being opened
     * @param {Object} data - The parsed file data
     * @returns {Promise<boolean>} - True if the password was correct
     */
    promptForPassword(file, data) {
        return new Promise((resolve) => {
            // Store the current file for reference in loadProtectedWorkbook
            this.currentFileForPasswordPrompt = file;

            // Import the standardized modal functions
            import('./standardized-modal.js').then(module => {
                // Create a standardized password prompt modal
                const modal = module.createPasswordPromptModal({
                    fileName: file.name,
                    onUnlock: () => {
                        const password = document.getElementById('password').value;
                        const errorBox = document.getElementById('passwordError');

                        // Function to show error in the modal
                        const showPasswordError = (message) => {
                            errorBox.textContent = message;
                            errorBox.style.display = 'block';
                        };

                        // Hide any previous error
                        errorBox.style.display = 'none';

                        if (!password) {
                            showPasswordError('Please enter a password');
                            document.getElementById('password').focus();
                            return;
                        }

                        // Check if password is correct
                        const passwordHash = this.hashPassword(password);
                        console.log('Checking password...');
                        console.log('Entered password hash:', passwordHash);
                        console.log('Expected password hash:', data.security.passwordHash);

                        // Disable the unlock button and show loading state
                        const unlockBtn = document.getElementById('unlockFileBtn');
                        unlockBtn.disabled = true;
                        unlockBtn.innerHTML = '<span style="display: inline-block; margin-right: 8px;">Unlocking...</span><span class="loading-spinner"></span>';

                        // Add loading spinner style
                        if (!document.getElementById('spinnerAnimation')) {
                            const style = document.createElement('style');
                            style.id = 'spinnerAnimation';
                            style.textContent = `
                                .loading-spinner {
                                    display: inline-block;
                                    width: 16px;
                                    height: 16px;
                                    border: 2px solid rgba(255,255,255,0.3);
                                    border-radius: 50%;
                                    border-top-color: white;
                                    animation: spin 1s linear infinite;
                                }
                                @keyframes spin {
                                    to { transform: rotate(360deg); }
                                }
                            `;
                            document.head.appendChild(style);
                        }

                        // Use setTimeout to allow the UI to update
                        setTimeout(() => {
                            if (passwordHash === data.security.passwordHash) {
                                console.log('Password is correct, loading protected workbook');

                                try {
                                    this.loadProtectedWorkbook(data)
                                        .then(() => {
                                            modal.close();
                                            resolve(true);
                                        })
                                        .catch(error => {
                                            console.error('Error loading protected workbook:', error);
                                            showPasswordError('Error loading workbook: ' + error.message);
                                            unlockBtn.disabled = false;
                                            unlockBtn.textContent = 'Unlock File';
                                            resolve(false);
                                        });
                                } catch (error) {
                                    console.error('Error loading protected workbook:', error);
                                    showPasswordError('Error loading workbook: ' + error.message);
                                    unlockBtn.disabled = false;
                                    unlockBtn.textContent = 'Unlock File';
                                    resolve(false);
                                }
                            } else {
                                console.log('Incorrect password entered');
                                showPasswordError('Incorrect password. Please try again.');
                                document.getElementById('password').value = '';
                                document.getElementById('password').focus();
                                unlockBtn.disabled = false;
                                unlockBtn.textContent = 'Unlock File';
                            }
                        }, 100);
                    },
                    onClose: () => {
                        resolve(false);
                    }
                });

                // Handle Enter key
                document.getElementById('password').addEventListener('keydown', (e) => {
                    if (e.key === 'Enter') {
                        document.getElementById('unlockFileBtn').click();
                        e.preventDefault();
                    }
                });
            }).catch(error => {
                console.error('Error loading standardized-modal.js:', error);
                this.showMessage('Error showing password prompt', 'error');
                resolve(false);
            });
        });
    }

    /**
     * Load the protected workbook data
     * @param {Object} data - The parsed workbook data
     * @returns {Promise<boolean>} - Promise that resolves to true if the workbook was loaded successfully
     */
    async loadProtectedWorkbook(dataFromFile) {
        try {
            this.showMessage('Loading protected workbook...', 'info');
            console.log('Data from file for protected loading:', dataFromFile);

            // Check for the new encoding structure first
            if (dataFromFile.encodedSheetData && dataFromFile.security && dataFromFile.security.protected) {
                console.log('Attempting to load with new custom decryption scheme.');
                // 1. Get the FileProtection instance to access customDecode
                if (!window.FileProtection || typeof window.FileProtection.customDecode !== 'function') {
                    console.error('FileProtection module or customDecode method not available.');
                    throw new Error('Decryption module not available. Cannot open file.');
                }
                const fileProtectionInstance = window.FileProtection;

                // 2. Decode the content
                console.log('Decoding content string using customDecode...');
                const decodedJsonString = fileProtectionInstance.customDecode(dataFromFile.encodedSheetData);
                console.log('Decoded JSON string (first 100 chars):', decodedJsonString.substring(0, 100));

                let actualWorkbookData;
                try {
                    actualWorkbookData = JSON.parse(decodedJsonString);
                } catch (parseError) {
                    console.error('Error parsing decoded JSON string:', parseError);
                    throw new Error('Failed to parse decrypted content. File may be corrupted or encoding/decoding mismatch.');
                }

                console.log('Successfully parsed custom decoded workbook data.');

                // 3. Create a blank workbook instance
                let workbook = await this.createBlankWorkbookForLoading();
                console.log('Blank workbook created for population with custom decoded data.');

                // 4. Populate the workbook with the decoded data
                await this.populateWorkbookFromJson(workbook, actualWorkbookData);

                // 5. Set up the workbook globally and update UI
                window.workbook = workbook;
                let currentFileName = (this.currentFileForPasswordPrompt && this.currentFileForPasswordPrompt.name) || 'protected_file.xlsm';
                window.workbook.originalFilename = currentFileName;
                this.updateUIAfterLoad(workbook, currentFileName, 'Custom protected file loaded and decrypted successfully.');
                return true;

            } else if (dataFromFile.workbook && dataFromFile.security && dataFromFile.security.protected) {
                // Fallback to old protection scheme if `encodedSheetData` is not present but `dataFromFile.workbook` is
                console.log('Attempting to load with old protection scheme (no custom encoding).');
                let workbook = await this.createBlankWorkbookForLoading();
                await this.populateWorkbookFromJson(workbook, dataFromFile.workbook); // Old format had data in dataFromFile.workbook

                window.workbook = workbook;
                let currentFileName = (this.currentFileForPasswordPrompt && this.currentFileForPasswordPrompt.name) || 'protected_file.xlsm';
                window.workbook.originalFilename = currentFileName;
                this.updateUIAfterLoad(workbook, currentFileName, 'Protected file (old format) loaded successfully.');
                return true;
            } else {
                console.error('Invalid protected file format. Neither new nor old structure found.', dataFromFile);
                throw new Error('Invalid protected file format. Cannot determine how to load.');
            }

        } catch (error) {
            console.error('Error loading protected workbook:', error);
            this.showMessage(`Error loading protected file: ${error.message}`, 'error');
            // Potentially re-enable UI elements in the password modal if it's still open
            const unlockBtn = document.getElementById('unlockFileBtn');
            if (unlockBtn) {
                unlockBtn.disabled = false;
                unlockBtn.innerHTML = 'Unlock File';
            }
            return false;
        }
    }

    // Helper method to create a blank workbook, consolidating logic from original function
    async createBlankWorkbookForLoading() {
        let workbook;
        try {
            if (window.utils && window.utils.excel && typeof window.utils.excel.createBlankWorkbook === 'function') {
                workbook = await window.utils.excel.createBlankWorkbook();
            } else if (typeof XlsxPopulate !== 'undefined' && typeof XlsxPopulate.fromBlankAsync === 'function') {
                workbook = await XlsxPopulate.fromBlankAsync();
            } else {
                console.warn("Excel library's createBlankWorkbook not found, creating simple workbook object for loading.");
                workbook = this.createSimpleWorkbook();
            }
        } catch (createError) {
            console.error('Error creating blank workbook during load:', createError);
            workbook = this.createSimpleWorkbook(); // Fallback
        }
        return workbook;
    }

    // Helper method to update UI and messages, consolidating logic
    updateUIAfterLoad(loadedWorkbook, fileName, successMessage) {
        // loadedWorkbook is window.workbook set by loadProtectedWorkbook
        // fileName is also available from loadedWorkbook.originalFilename or the file object initially passed

        if (typeof window.handleWorkbookLoadedExternally === 'function') {
            // This function will now handle setting main_script.js's internal state
            // and triggering sheetManager.initialize and renderSheet.
            window.handleWorkbookLoadedExternally();
        } else {
            console.warn('handleWorkbookLoadedExternally function not found. UI might not update correctly.');
            // Fallback to the old method (which was likely insufficient)
            if (typeof window.updateSpreadsheetView === 'function') {
                console.warn('Falling back to old updateSpreadsheetView call.');
                window.updateSpreadsheetView(); // This likely won't work as expected without proper state sync.
            }
        }

        // This message confirms that ProtectedFileOpener completed its part.
        // The success message for rendering completion should ideally be within handleWorkbookLoadedExternally or renderSheet.
        this.showMessage(successMessage, 'success', 7000);
        console.log('ProtectedFileOpener.updateUIAfterLoad completed for:', fileName);
    }

    /**
     * Populate a workbook object from JSON data.
     * @param {Object} workbook - The workbook instance to populate.
     * @param {Object} jsonData - The JSON data representing the workbook content.
     */
    async populateWorkbookFromJson(workbook, jsonData) {
        console.log('Populating workbook from JSON data:', jsonData);
        if (!workbook || !jsonData) {
            console.error('Workbook or JSON data is missing for population.');
            throw new Error('Workbook or JSON data is missing.');
        }

        try {
            // Set workbook properties
            if (jsonData.properties) {
                console.log('Setting workbook properties...');
                workbook.title = jsonData.properties.title || '';
                workbook.author = jsonData.properties.author || '';
                workbook.subject = jsonData.properties.subject || '';
                workbook.keywords = jsonData.properties.keywords || '';
                workbook.category = jsonData.properties.category || '';
                // originalFilename is typically set when the file is first loaded or saved,
                // but we can preserve it if it's in the JSON.
                if (jsonData.properties.originalFilename) {
                    workbook.originalFilename = jsonData.properties.originalFilename;
                }
            }

            if (!jsonData.sheets || jsonData.sheets.length === 0) {
                console.warn('JSON data has no sheets. Workbook will be empty.');
                // Ensure at least one sheet exists if underlying lib expects it
                if (typeof workbook.sheet === 'function' && !workbook.sheet(0)) {
                     if(typeof workbook.addSheet === 'function') workbook.addSheet("Sheet1");
                     else if (workbook.SheetNames && workbook.Sheets) { // xlsx.js specific
                        XLSX.utils.book_append_sheet(workbook, XLSX.utils.aoa_to_sheet([[]]), "Sheet1");
                     }
                }
                return;
            }

            console.log(`Processing ${jsonData.sheets.length} sheets from JSON...`);

            // Clear existing sheets if necessary (some libraries might add a default sheet)
            // For XlsxPopulate, new workbooks start with one sheet. We can rename it or manage.
            // For exceljs, it's usually empty.
            // For xlsx (SheetJS), new workbooks are empty.

            let isFirstSheet = true;
            for (let i = 0; i < jsonData.sheets.length; i++) {
                const sheetJson = jsonData.sheets[i];
                console.log(`Processing sheet from JSON: ${sheetJson.name}`);

                let sheet;
                // Attempt to get or create sheet based on library
                if (typeof workbook.sheet === 'function') { // XlsxPopulate like
                    if (isFirstSheet && workbook.sheet(0)) {
                        sheet = workbook.sheet(0);
                        if (sheet.name() !== sheetJson.name) {
                           try { sheet.name(sheetJson.name); } catch(e){ console.warn("Could not rename first sheet", e); }
                        }
                    } else if (typeof workbook.addSheet === 'function') {
                        sheet = workbook.addSheet(sheetJson.name);
                    } else {
                         // Fallback for XlsxPopulate if addSheet is not there but sheet(name) works for creation
                        sheet = workbook.sheet(sheetJson.name);
                    }
                } else if (workbook.addWorksheet) { // ExcelJS like
                    sheet = workbook.addWorksheet(sheetJson.name);
                } else if (workbook.SheetNames && workbook.Sheets) { // SheetJS (xlsx) like
                    const newSheetAoA = XLSX.utils.aoa_to_sheet([[]]); // Start with an empty sheet
                    XLSX.utils.book_append_sheet(workbook, newSheetAoA, sheetJson.name);
                    sheet = workbook.Sheets[sheetJson.name]; // Get reference for data population
                }


                if (!sheet) {
                    console.warn(`Could not get or create sheet object for: ${sheetJson.name}. Skipping this sheet.`);
                    continue;
                }
                isFirstSheet = false;

                // Add data and styles
                if (sheetJson.data && sheetJson.data.length > 0) {
                    console.log(`Adding data for sheet ${sheetJson.name}: ${sheetJson.data.length} rows`);

                    // XlsxPopulate / ExcelJS like cell access
                    if (typeof sheet.cell === 'function' || sheet.getCell) { // sheet.getCell for ExcelJS
                        for (let r = 0; r < sheetJson.data.length; r++) {
                            const rowData = sheetJson.data[r] || [];
                            const rowStyles = (sheetJson.styles && sheetJson.styles[r]) ? sheetJson.styles[r] : [];
                            for (let c = 0; c < rowData.length; c++) {
                                const cellValue = rowData[c];
                                const cellStyleJson = (rowStyles && rowStyles[c]) ? rowStyles[c] : {};

                                const cell = typeof sheet.cell === 'function' ? sheet.cell(r + 1, c + 1) : sheet.getCell(r + 1, c + 1);

                                if (cell) {
                                    if (typeof cell.value === 'function') cell.value(cellValue); // XlsxPopulate
                                    else cell.value = cellValue; // ExcelJS

                                    if (cellStyleJson) {
                                        // Apply styles - XlsxPopulate
                                        if (typeof cell.style === 'function') {
                                            if (cellStyleJson.bold !== undefined) cell.style('bold', cellStyleJson.bold);
                                            if (cellStyleJson.italic !== undefined) cell.style('italic', cellStyleJson.italic);
                                            if (cellStyleJson.underline !== undefined) cell.style('underline', cellStyleJson.underline);
                                            if (cellStyleJson.fontColor) cell.style('fontColor', cellStyleJson.fontColor);
                                            if (cellStyleJson.fill) cell.style('fill', cellStyleJson.fill);
                                            if (cellStyleJson.horizontalAlignment) cell.style('horizontalAlignment', cellStyleJson.horizontalAlignment);
                                            if (cellStyleJson.verticalAlignment) cell.style('verticalAlignment', cellStyleJson.verticalAlignment);
                                            if (cellStyleJson.fontSize) cell.style('fontSize', cellStyleJson.fontSize);
                                            if (cellStyleJson.fontFamily) cell.style('fontFamily', cellStyleJson.fontFamily);
                                            if (cellStyleJson.numberFormat) cell.style('numberFormat', cellStyleJson.numberFormat);
                                        }
                                        // Apply styles - ExcelJS
                                        else {
                                            cell.font = {
                                                bold: cellStyleJson.bold || false,
                                                italic: cellStyleJson.italic || false,
                                                underline: cellStyleJson.underline || false,
                                                color: { argb: cellStyleJson.fontColor },
                                                size: cellStyleJson.fontSize,
                                                name: cellStyleJson.fontFamily
                                            };
                                            if (cellStyleJson.fill) {
                                                cell.fill = {
                                                    type: 'pattern',
                                                    pattern: 'solid',
                                                    fgColor: { argb: cellStyleJson.fill }
                                                };
                                            }
                                            cell.alignment = {
                                                horizontal: cellStyleJson.horizontalAlignment,
                                                vertical: cellStyleJson.verticalAlignment
                                            };
                                            cell.numFmt = cellStyleJson.numberFormat || 'General';
                                        }
                                    }
                                }
                            }
                        }
                    }
                    // SheetJS (xlsx) like data population (using AoA)
                    else if (sheet && Array.isArray(sheetJson.data)) {
                         // For SheetJS, we replace the initially empty sheet with the actual data
                         // Note: This basic AoA approach won't carry over complex styles from JSON.
                         // For styles with SheetJS, one would typically build a cell object for each cell.
                         // This is a simplified version focusing on values.
                        const aoaData = sheetJson.data.map(row => row.map(cell => (cell === null || cell === undefined ? "" : cell)));
                        const newSheetWithData = XLSX.utils.aoa_to_sheet(aoaData);
                        // Preserve other sheet properties if any by merging (e.g., !merges, !cols)
                        workbook.Sheets[sheetJson.name] = { ...sheet, ...newSheetWithData };

                        // A more robust way for SheetJS to handle styles would be:
                        // Iterate sheetJson.data and sheetJson.styles
                        // For each cell, create a cell object: {v: value, t: type, s: style_object}
                        // Then use XLSX.utils.sheet_add_aoa or directly manipulate worksheet['A1'] etc.
                        // For simplicity, the above aoa_to_sheet is used. If styles are critical,
                        // this part needs expansion to map JSON styles to SheetJS style objects.
                        console.warn("For SheetJS, complex styles from JSON are not fully applied in this simplified populateWorkbookFromJson. Values are prioritized.");
                    }
                }
            }
            console.log('Finished populating workbook from JSON.');
        } catch (error) {
            console.error('Error populating workbook from JSON:', error);
            throw new Error(`Failed to populate workbook: ${error.message}`);
        }
    }

    /**
     * Create a simple workbook object as a fallback
     * @returns {Object} - A simple workbook object
     */
    createSimpleWorkbook() {
        return {
            title: '',
            author: '',
            subject: '',
            keywords: '',
            category: '',
            originalFilename: 'spreadsheet.xlsx',
            sheet: function(index) {
                return {
                    name: function(newName) {
                        if (newName) {
                            this._name = newName;
                            return this;
                        }
                        return this._name || 'Sheet1';
                    },
                    cell: function(row, col) {
                        return {
                            value: function(newValue) {
                                if (newValue !== undefined) {
                                    this._value = newValue;
                                    return this;
                                }
                                return this._value;
                            },
                            style: function(name, value) {
                                if (!this._styles) this._styles = {};
                                if (value !== undefined) {
                                    this._styles[name] = value;
                                    return this;
                                }
                                return this._styles[name];
                            }
                        };
                    }
                };
            },
            sheets: function() {
                return [this.sheet(0)];
            },
            addSheet: function(name) {
                const sheet = this.sheet(0);
                sheet.name(name);
                return sheet;
            }
        };
    }

    /**
     * Render a simple sheet as a fallback
     * @param {Object} data - The parsed workbook data
     */
    renderSimpleSheet(data) {
        console.log('Rendering simple sheet as fallback');

        // Get the spreadsheet container
        const container = document.getElementById('spreadsheetContainer');
        if (!container) {
            console.error('Spreadsheet container not found');
            return;
        }

        // Clear the container
        container.innerHTML = '';

        // Create a table to display the data
        const table = document.createElement('table');
        table.id = 'excelTable';
        table.className = 'excel-table';
        table.style.borderCollapse = 'collapse';
        table.style.width = '100%';

        // Get the first sheet data
        const sheetData = data.sheets[0] || { name: 'Sheet1', data: [[]] };

        // Create a header row with the sheet name
        const headerRow = document.createElement('tr');
        const headerCell = document.createElement('th');
        headerCell.colSpan = 10;
        headerCell.textContent = `Sheet: ${sheetData.name}`;
        headerCell.style.backgroundColor = '#f0f0f0';
        headerCell.style.padding = '8px';
        headerCell.style.textAlign = 'center';
        headerRow.appendChild(headerCell);
        table.appendChild(headerRow);

        // Create rows and cells
        for (let r = 0; r < sheetData.data.length; r++) {
            const rowData = sheetData.data[r];
            const rowStyles = sheetData.styles[r];

            const tr = document.createElement('tr');

            for (let c = 0; c < rowData.length; c++) {
                const cellValue = rowData[c];
                const cellStyle = rowStyles[c];

                const td = document.createElement('td');
                td.textContent = cellValue !== null ? cellValue : '';
                td.style.border = '1px solid #ddd';
                td.style.padding = '8px';

                // Apply styles
                if (cellStyle) {
                    if (cellStyle.bold) td.style.fontWeight = 'bold';
                    if (cellStyle.italic) td.style.fontStyle = 'italic';
                    if (cellStyle.underline) td.style.textDecoration = 'underline';
                    if (cellStyle.fontColor) td.style.color = cellStyle.fontColor;
                    if (cellStyle.fill) td.style.backgroundColor = cellStyle.fill;
                    if (cellStyle.horizontalAlignment) td.style.textAlign = cellStyle.horizontalAlignment;
                    if (cellStyle.fontSize) td.style.fontSize = `${cellStyle.fontSize}px`;
                    if (cellStyle.fontFamily) td.style.fontFamily = cellStyle.fontFamily;
                }

                tr.appendChild(td);
            }

            table.appendChild(tr);
        }

        // Add the table to the container
        container.appendChild(table);
    }

    /**
     * Simple password hashing function
     * @param {string} password - The password to hash
     * @returns {string} - Hashed password
     */
    hashPassword(password) {
        // This is a simple hash for demonstration
        // In a real application, use a proper crypto library
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
        }
        return hash.toString(16);
    }

    /**
     * Read a file as text
     * @param {File} file - The file to read
     * @returns {Promise<string>} - The file contents as text
     */
    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => resolve(event.target.result);
            reader.onerror = (error) => reject(error);
            reader.readAsText(file);
        });
    }

    /**
     * Show a message to the user
     * @param {string} message - The message to show
     * @param {string} type - The message type (info, success, error)
     */
    showMessage(message, type = 'info') {
        const statusElement = document.getElementById('status');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = `status-${type}`;

            // Clear the message after 5 seconds
            setTimeout(() => {
                statusElement.textContent = '';
                statusElement.className = '';
            }, 5000);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}

// Export the class
export default ProtectedFileOpener;
