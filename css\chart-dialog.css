/**
 * Enhanced Excel-like Chart Dialog Styles
 */

/* Chart dialog content styles only - modal container styling is handled by standardized-modal.css */
.excel-modal-content {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    width: 100%; /* Use full width of parent */
    max-width: 100%;
    max-height: 95%; /* Increased height */
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid #e0e0e0;
    animation: modal-appear 0.3s ease-out forwards;
}

@keyframes modal-appear {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes modal-disappear {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-30px);
    }
}

.excel-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 32px; /* Increased padding */
    border-bottom: 1px solid #e0e0e0;
    background-color: #f8f9fa;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); /* Added subtle shadow */
    position: relative; /* For shadow effect */
    z-index: 1; /* Ensure shadow appears above content */
}

.excel-modal-header h2 {
    margin: 0;
    font-size: 24px; /* Larger font */
    font-weight: 600;
    color: #333;
}

.excel-modal-close {
    background: none;
    border: none;
    font-size: 28px; /* Larger font */
    cursor: pointer;
    color: #666;
    width: 44px; /* Larger button */
    height: 44px; /* Larger button */
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.25s ease; /* Smoother transition */
}

.excel-modal-close:hover {
    background-color: rgba(0, 0, 0, 0.08);
    color: #333;
    transform: rotate(90deg); /* Rotation effect */
}

.excel-modal-body {
    padding: 0;
    overflow-y: auto;
    flex: 1;
}

.excel-modal-footer {
    display: flex;
    justify-content: flex-end;
    padding: 22px 32px; /* Increased padding */
    border-top: 1px solid #e0e0e0;
    gap: 20px; /* Increased gap */
    background-color: #f8f9fa;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05); /* Added subtle shadow */
    position: relative; /* For shadow effect */
    z-index: 1; /* Ensure shadow appears above content */
}

/* Chart Dialog */
.chart-dialog-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.chart-dialog-tabs {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
    background-color: #f8f9fa;
}

.chart-dialog-tab {
    padding: 18px 28px;
    cursor: pointer;
    font-weight: 500;
    color: #666;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 16px;
}

.chart-dialog-tab:hover {
    background-color: #f0f0f0;
    color: #333;
}

.chart-dialog-tab.active {
    color: #1a73e8;
    border-bottom-color: #1a73e8;
    background-color: #f0f7ff;
}

.chart-dialog-tab .material-icons {
    font-size: 22px;
}

.chart-dialog-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.chart-dialog-sidebar {
    width: 340px; /* Increased width */
    flex-shrink: 0;
    border-right: 1px solid #e0e0e0;
    overflow-y: auto;
    padding: 32px; /* Increased padding */
    background-color: #f8f9fa;
}

.chart-dialog-main {
    flex: 1;
    padding: 32px; /* Increased padding */
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.chart-type-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); /* Wider items */
    gap: 24px; /* Increased gap */
    margin-bottom: 32px; /* Increased margin */
}

.chart-type-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 24px 20px; /* Increased padding */
    border-radius: 12px; /* Increased border radius */
    border: 1px solid #e0e0e0;
    cursor: pointer;
    transition: all 0.25s ease; /* Smoother transition */
    background-color: #fff;
    position: relative;
    height: 160px; /* Fixed height for consistency */
    justify-content: center; /* Center content vertically */
}

.chart-type-item:hover {
    border-color: #1a73e8;
    background-color: #f0f7ff;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
}

.chart-type-item.selected {
    border-color: #1a73e8;
    background-color: #e8f0fe;
    box-shadow: 0 6px 16px rgba(26, 115, 232, 0.25);
}

.chart-type-icon {
    font-size: 38px; /* Larger icon */
    color: #5f6368;
    margin-bottom: 20px; /* Increased margin */
    transition: transform 0.3s ease; /* Added transition */
}

.chart-type-item:hover .chart-type-icon {
    transform: scale(1.1); /* Slight scale effect on hover */
}

.chart-type-item.selected .chart-type-icon {
    color: #1a73e8;
}

.chart-type-label {
    font-size: 16px; /* Larger font */
    text-align: center;
    color: #5f6368;
    font-weight: 500;
    margin-bottom: 8px; /* Added margin */
}

.chart-type-item.selected .chart-type-label {
    color: #1a73e8;
    font-weight: 600;
}

.chart-type-description {
    font-size: 13px;
    text-align: center;
    color: #888;
    margin-top: 10px;
    line-height: 1.4;
}

.chart-preview-container {
    flex: 1;
    min-height: 500px; /* Increased height */
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 28px; /* Increased padding */
    margin-top: 32px; /* Increased margin */
    background-color: #fff;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); /* Enhanced shadow */
}

.chart-preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px; /* Increased margin */
    padding-bottom: 16px; /* Added padding */
    border-bottom: 1px solid #f0f0f0; /* Added subtle border */
}

.chart-preview-title {
    font-weight: 600;
    color: #333;
    font-size: 20px; /* Increased font size */
}

.chart-preview-content {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px; /* Increased height */
    background-color: #fafafa; /* Added subtle background */
    border-radius: 8px; /* Added border radius */
}

.chart-settings-section {
    margin-bottom: 36px; /* Increased margin */
    background: white;
    padding: 24px; /* Increased padding */
    border-radius: 14px; /* Increased border radius */
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); /* Enhanced shadow */
    border: 1px solid #f0f0f0; /* Added subtle border */
}

.chart-settings-title {
    font-weight: 600;
    margin-bottom: 24px; /* Increased margin */
    color: #333;
    display: flex;
    align-items: center;
    gap: 12px; /* Increased gap */
    font-size: 18px; /* Larger font */
    padding-bottom: 12px; /* Added padding */
    border-bottom: 1px solid #f5f5f5; /* Added subtle border */
}

.chart-settings-title .material-icons {
    font-size: 24px; /* Larger icon */
    color: #1a73e8; /* Changed color to match theme */
}

.chart-template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.chart-template-item {
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: #fff;
}

.chart-template-item:hover {
    border-color: #1a73e8;
    background-color: #f0f7ff;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
}

.chart-template-item.selected {
    border-color: #1a73e8;
    background-color: #e8f0fe;
    box-shadow: 0 6px 16px rgba(26, 115, 232, 0.25);
}

.chart-template-preview {
    height: 120px;
    margin-bottom: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f8f9fa;
    border-radius: 6px;
}

.chart-template-name {
    font-size: 15px;
    text-align: center;
    color: #333;
    font-weight: 500;
}

.chart-data-section {
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 28px;
    background-color: #fff;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.06);
}

.chart-data-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-data-title {
    font-weight: 600;
    color: #333;
    font-size: 18px;
}

.chart-data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    overflow: hidden;
}

.chart-data-table th,
.chart-data-table td {
    padding: 14px 18px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}

.chart-data-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.chart-data-table tr:last-child td {
    border-bottom: none;
}

.chart-data-table tr:hover td {
    background-color: #f0f7ff;
}

.chart-axis-settings {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-top: 20px;
}

.chart-theme-options {
    display: flex;
    gap: 20px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.chart-theme-option {
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    padding: 20px;
    width: 140px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
}

.chart-theme-option:hover {
    border-color: #1a73e8;
    background-color: #f0f7ff;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
}

.chart-theme-option.selected {
    border-color: #1a73e8;
    background-color: #e8f0fe;
    box-shadow: 0 6px 16px rgba(26, 115, 232, 0.25);
}

.chart-theme-preview {
    height: 80px;
    margin-bottom: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
    background-color: #f8f9fa;
}

.chart-theme-name {
    font-size: 15px;
    color: #333;
    font-weight: 500;
}

/* Form elements */
.form-group {
    margin-bottom: 24px; /* Increased margin */
}

.form-group label {
    display: block;
    margin-bottom: 12px; /* Increased margin */
    font-size: 16px; /* Larger font */
    font-weight: 500;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 14px 18px; /* Increased padding */
    border: 1px solid #e0e0e0;
    border-radius: 8px; /* Increased border radius */
    font-size: 16px; /* Larger font */
    transition: all 0.25s ease; /* Smoother transition */
    background-color: #fafafa; /* Subtle background */
    color: #333;
}

.form-control:hover {
    border-color: #d0d0d0;
    background-color: #f5f5f5;
}

.form-control:focus {
    border-color: #1a73e8;
    box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.25);
    outline: none;
    background-color: #fff;
}

.color-picker-control {
    display: flex;
    gap: 16px;
    align-items: center;
}

.color-box {
    width: 36px;
    height: 36px;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    cursor: pointer;
}

/* Switch control */
.switch-control {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.switch-label {
    margin-left: 16px;
    font-size: 15px;
    color: #333;
    font-weight: 500;
}

.switch {
    position: relative;
    display: inline-block;
    width: 52px;
    height: 26px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .3s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .3s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #1a73e8;
}

input:focus + .slider {
    box-shadow: 0 0 1px #1a73e8;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* Button styles */
.excel-btn {
    padding: 14px 32px; /* Increased padding */
    border-radius: 8px; /* Increased border radius */
    font-size: 16px; /* Larger font */
    font-weight: 500;
    cursor: pointer;
    border: none;
    transition: all 0.25s ease; /* Smoother transition */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 12px; /* Increased gap */
    min-width: 120px; /* Minimum width */
}

.excel-btn .material-icons {
    font-size: 22px; /* Larger icon */
}

.excel-btn-primary {
    background-color: #1a73e8;
    color: white;
}

.excel-btn-primary:hover {
    background-color: #1765cc;
    box-shadow: 0 6px 12px rgba(26, 115, 232, 0.35); /* Enhanced shadow */
    transform: translateY(-2px); /* Slight lift effect */
}

.excel-btn-primary:active {
    transform: translateY(0); /* Reset on active */
    box-shadow: 0 2px 6px rgba(26, 115, 232, 0.3);
}

.excel-btn-secondary {
    background-color: #f5f5f5;
    color: #333;
    border: 1px solid #e0e0e0;
}

.excel-btn-secondary:hover {
    background-color: #e8e8e8;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12); /* Enhanced shadow */
    transform: translateY(-2px); /* Slight lift effect */
}

.excel-btn-secondary:active {
    transform: translateY(0); /* Reset on active */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Excel Chart */
.excel-chart {
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    background-color: #fff;
}

.excel-chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
}

.excel-chart-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.excel-chart-controls {
    display: flex;
    gap: 10px;
}

.excel-chart-btn {
    background: none;
    border: none;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 6px;
    transition: background-color 0.2s;
}

.excel-chart-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.excel-chart-btn .material-icons {
    font-size: 20px;
    color: #666;
}

.excel-chart-content {
    padding: 24px;
    height: 380px;
}

/* Draggable Chart */
.draggable-chart {
    transition: box-shadow 0.2s ease, transform 0.2s ease;
}

.draggable-chart.dragging {
    box-shadow: 0 16px 32px rgba(0, 0, 0, 0.25);
    opacity: 0.9;
    cursor: move;
    transform: scale(1.02);
}

.draggable-chart .excel-chart-header {
    cursor: grab;
}

.draggable-chart.dragging .excel-chart-header {
    cursor: grabbing;
}

/* Range Selection */
.range-selection-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.4);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding-top: 40px;
}

.range-selection-message {
    background-color: white;
    padding: 20px 28px;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
    display: flex;
    align-items: center;
    gap: 20px;
}

.range-selection-message p {
    margin: 0;
    font-weight: 500;
    font-size: 16px;
}

.range-selection-info {
    margin-top: 16px;
    font-size: 14px;
    color: #666;
    line-height: 1.5;
}

.range-selection-info p {
    margin: 8px 0;
}

/* Chart animation effects */
.chart-appear {
    animation: chart-appear 0.8s cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
}

@keyframes chart-appear {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Color palette display */
.color-palette-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-top: 20px;
}

.color-palette-item {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    width: 150px;
    cursor: pointer;
    transition: all 0.2s;
}

.color-palette-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.12);
}

.color-palette-item.selected {
    border-color: #1a73e8;
    box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.3);
}

.color-palette-preview {
    height: 36px;
    display: flex;
}

.color-palette-preview div {
    flex: 1;
    height: 100%;
}

.color-palette-name {
    padding: 10px;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    background-color: #f8f9fa;
    border-top: 1px solid #e0e0e0;
}

/* Chart type groups */
.chart-type-group {
    margin-bottom: 28px;
}

.chart-type-group-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.chart-type-group-title .material-icons {
    font-size: 22px;
    color: #1a73e8;
}

/* Chart type group description */
.chart-type-group-description {
    font-size: 14px;
    color: #5f6368;
    margin: 0 0 16px 0;
    line-height: 1.5;
}

/* Chart type selection icon */
.chart-type-selected-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    color: #1a73e8;
    font-size: 20px;
}

/* Chart data range selector */
.data-range-selector {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-top: 20px;
}

.data-range-input {
    flex: 1;
    position: relative;
}

.data-range-input input {
    padding-right: 44px;
}

.data-range-input .material-icons {
    position: absolute;
    right: 14px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    cursor: pointer;
    font-size: 20px;
}

.data-range-button {
    white-space: nowrap;
}

/* Mobile responsiveness enhancements */
@media (max-width: 1200px) {
    .chart-dialog-sidebar {
        width: 280px;
    }

    .chart-type-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .chart-template-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }
}

@media (max-width: 768px) {
    .excel-modal-content {
        max-width: 100%;
        max-height: 100%;
        border-radius: 0;
    }

    .chart-dialog-content {
        flex-direction: column;
    }

    .chart-dialog-sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid #e0e0e0;
    }
}

/* Chart Dialog - Chart Type Intro Section */
.chart-intro-section {
    background-color: #f0f7ff;
    border-radius: 12px;
    padding: 20px 24px;
    margin-bottom: 28px;
    border-left: 4px solid #1a73e8;
}

.chart-intro-section h3 {
    font-size: 18px;
    color: #1a73e8;
    margin: 0 0 10px 0;
    font-weight: 600;
}

.chart-intro-section p {
    font-size: 14px;
    color: #5f6368;
    margin: 0;
    line-height: 1.5;
}

/* Chart Style Tab */
.chart-style-intro {
    background-color: #f0f7ff;
    border-radius: 12px;
    padding: 20px 24px;
    margin-bottom: 28px;
    border-left: 4px solid #1a73e8;
}

.chart-style-intro h3 {
    font-size: 18px;
    color: #1a73e8;
    margin: 0 0 10px 0;
    font-weight: 600;
}

.chart-style-intro p {
    font-size: 14px;
    color: #5f6368;
    margin: 0;
    line-height: 1.5;
}

.chart-style-options-container {
    display: flex;
    gap: 24px;
    margin-top: 28px;
}

.chart-style-options-container .chart-settings-section {
    flex: 1;
}

.chart-settings-section p {
    color: #5f6368;
    font-size: 14px;
    margin: 0 0 16px 0;
    line-height: 1.5;
}

/* Enhanced settings for wide display */
.ai-style-change-2 .chart-style-options-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 28px;
}

.color-palette-display {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 16px;
    margin-top: 20px;
}

/* Enhanced sizing for wider display */
.ai-style-change-2 .color-palette-display {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 18px;
}

.ai-style-change-2 .chart-theme-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
    gap: 18px;
}

/* Make palette preview swatches larger */
.color-palette-preview div {
    flex: 1;
    height: 100%;
    transition: transform 0.15s ease;
}

.color-palette-preview div:hover {
    transform: scaleY(1.2);
}

@media (max-width: 1100px) {
    .chart-style-options-container {
        flex-direction: column;
    }
}

/* Apply specific style for chart dialog with ai-style-change-2 */
.excel-modal.chart-dialog.ai-style-change-2 .excel-modal-content {
    width: 95%;
    max-width: 1900px;
    left: 0 !important;
    right: 0 !important;
    margin: 0 auto !important;
    position: relative !important;
}

/* Special styling for the wider modal */
.ai-style-change-2 .chart-type-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 18px;
}

.ai-style-change-2 .chart-template-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
}

.ai-style-change-2 .chart-dialog-sidebar {
    width: 340px;
}

.ai-style-change-2 .chart-preview-container {
    min-height: 480px;
}

.ai-style-change-2 .chart-preview-content {
    min-height: 400px;
}

