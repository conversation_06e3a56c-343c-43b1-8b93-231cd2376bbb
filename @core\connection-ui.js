// @core/connection-ui.js

import { connectionManager } from './connection.js';
import { NotificationSystem } from './notification-system.js';

/**
 * UI component for managing connections
 */
export class ConnectionUI {
  constructor() {
    this.notificationSystem = new NotificationSystem();
    this.isPopupOpen = false;
  }

  /**
   * Initialize the connection UI
   */
  async init() {
    await connectionManager.init();
    this.updateConnectionIcon();
    this.setupEventListeners();
  }

  /**
   * Set up event listeners
   */
  setupEventListeners() {
    // Set up the connection button click event
    const connectionButton = document.getElementById('connectionButton');
    if (connectionButton) {
      connectionButton.addEventListener('click', () => {
        if (this.isPopupOpen) {
          this.removeExistingPopup();
        } else {
          this.showConnectionPopup();
        }
      });
    }

    // Listen for connection changes from other components
    document.addEventListener('acumaticaConnectionChanged', () => {
      this.updateConnectionIcon();
    });
  }

  /**
   * Update the connection icon based on connection status
   */
  updateConnectionIcon() {
    const connectionButton = document.getElementById('connectionButton');
    if (!connectionButton) return;

    const status = connectionManager.getConnectionStatus();
    
    // Update icon based on connection status
    if (status.acumatica.isConnected || status.monday.isConnected) {
      connectionButton.classList.add('connected');
      connectionButton.title = 'Connected to external services';
    } else {
      connectionButton.classList.remove('connected');
      connectionButton.title = 'Not connected to any external services';
    }
  }

  /**
   * Show the connection popup
   */
  showConnectionPopup() {
    // Remove any existing popup
    this.removeExistingPopup();

    // Get connection status
    const status = connectionManager.getConnectionStatus();

    // Create popup
    const popup = document.createElement('div');
    popup.id = 'connectionPopup';
    popup.className = 'connection-popup';
    popup.innerHTML = `
      <div class="connection-popup-header">
        <h3>External Connections</h3>
        <button id="closeConnectionPopup" class="close-button">
          <span class="material-icons">close</span>
        </button>
      </div>
      <div class="connection-popup-content">
        <div class="p-2 bg-gray-50 dark:bg-gray-700 rounded-md">
          <div class="flex justify-between items-center mb-1">
            <div class="flex items-center">
              <svg class="w-5 h-5 mr-2 ${status.acumatica.isConnected ? 'text-green-500' : 'text-gray-400'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M12 5l7 7-7 7"></path>
              </svg>
              <h4 class="font-medium">Acumatica ERP</h4>
            </div>
            <span class="px-2 py-1 text-xs rounded-full ${status.acumatica.isConnected ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
              ${status.acumatica.isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
          
          ${status.acumatica.isConnected ? 
            `<div class="text-sm text-gray-600 dark:text-gray-300 mb-1">
              <p>Connected to: ${status.acumatica.instance}</p>
              <p>User: ${status.acumatica.username}</p>
            </div>
            <button id="disconnectAcumaticaBtn" class="mt-1 px-3 py-1 bg-red-600 text-white rounded-md text-sm hover:bg-red-700">
              Disconnect
            </button>` 
            : 
            `<form id="acumaticaForm" class="mt-1 space-y-1">
              <div>
                <label class="block text-xs mb-1">Instance URL</label>
                <input type="text" id="acumaticaInstance" value="https://envent-eng.acumatica.com" class="w-full px-2 py-1 text-sm border rounded-md dark:bg-gray-800 dark:border-gray-600">
              </div>
              <div>
                <label class="block text-xs mb-1">Username</label>
                <input type="text" id="acumaticaUsername" class="w-full px-2 py-1 text-sm border rounded-md dark:bg-gray-800 dark:border-gray-600">
              </div>
              <div>
                <label class="block text-xs mb-1">Password</label>
                <input type="password" id="acumaticaPassword" class="w-full px-2 py-1 text-sm border rounded-md dark:bg-gray-800 dark:border-gray-600">
              </div>
              <div>
                <label class="block text-xs mb-1">Company</label>
                <input type="text" id="acumaticaCompany" value="Envent CA - Live" class="w-full px-2 py-1 text-sm border rounded-md dark:bg-gray-800 dark:border-gray-600">
              </div>
              <button type="submit" id="connectAcumaticaBtn" class="w-full mt-1 px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                Connect
              </button>
            </form>`
          }
        </div>
        
        <div class="mt-4 p-2 bg-gray-50 dark:bg-gray-700 rounded-md">
          <div class="flex justify-between items-center mb-1">
            <div class="flex items-center">
              <svg class="w-5 h-5 mr-2 ${status.monday.isConnected ? 'text-green-500' : 'text-gray-400'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
              <h4 class="font-medium">Monday.com</h4>
            </div>
            <span class="px-2 py-1 text-xs rounded-full ${status.monday.isConnected ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
              ${status.monday.isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
          
          ${status.monday.isConnected ? 
            `<div class="text-sm text-gray-600 dark:text-gray-300 mb-1">
              <p>API Key: ••••••••••••••••</p>
            </div>
            <button id="disconnectMondayBtn" class="mt-1 px-3 py-1 bg-red-600 text-white rounded-md text-sm hover:bg-red-700">
              Disconnect
            </button>` 
            : 
            `<form id="mondayForm" class="mt-1 space-y-1">
              <div>
                <label class="block text-xs mb-1">API Key</label>
                <input type="password" id="mondayApiKey" class="w-full px-2 py-1 text-sm border rounded-md dark:bg-gray-800 dark:border-gray-600">
              </div>
              <button type="submit" id="connectMondayBtn" class="w-full mt-1 px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                Connect
              </button>
            </form>`
          }
        </div>
      </div>
    `;

    // Add styles
    popup.style.position = 'absolute';
    popup.style.top = '50px';
    popup.style.right = '10px';
    popup.style.width = '300px';
    popup.style.backgroundColor = '#fff';
    popup.style.borderRadius = '8px';
    popup.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
    popup.style.zIndex = '1000';
    popup.style.padding = '16px';

    // Add to document
    document.body.appendChild(popup);
    this.isPopupOpen = true;

    // Add event listeners
    this.addPopupEventListeners();
  }

  /**
   * Add event listeners to the popup
   */
  addPopupEventListeners() {
    // Close button
    const closeBtn = document.getElementById('closeConnectionPopup');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        this.removeExistingPopup();
      });
    }
    
    // Acumatica connection form
    const acumaticaForm = document.getElementById('acumaticaForm');
    if (acumaticaForm) {
      acumaticaForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const instance = document.getElementById('acumaticaInstance').value;
        const username = document.getElementById('acumaticaUsername').value;
        const password = document.getElementById('acumaticaPassword').value;
        const company = document.getElementById('acumaticaCompany').value;
        
        if (!instance || !username || !password || !company) {
          this.notificationSystem.addNotification('Please fill in all Acumatica fields.', 'error');
          return;
        }
        
        // Disable form
        const submitBtn = document.getElementById('connectAcumaticaBtn');
        submitBtn.disabled = true;
        submitBtn.textContent = 'Connecting...';
        
        // Connect
        const result = await connectionManager.connectToAcumatica(instance, username, password, company);
        
        // Handle result
        if (result.success) {
          this.notificationSystem.addNotification('Successfully connected to Acumatica.', 'success');
          this.updateConnectionIcon();
          this.showConnectionPopup(); // Refresh popup
          
          // Dispatch event for components to know about the connection
          document.dispatchEvent(new CustomEvent('acumaticaConnectionChanged', { 
            detail: { connected: true } 
          }));
        } else {
          this.notificationSystem.addNotification(`Failed to connect: ${result.error}`, 'error');
          
          // Re-enable form
          submitBtn.disabled = false;
          submitBtn.textContent = 'Connect';
        }
      });
    }
    
    // Acumatica disconnect button
    const disconnectAcumaticaBtn = document.getElementById('disconnectAcumaticaBtn');
    if (disconnectAcumaticaBtn) {
      disconnectAcumaticaBtn.addEventListener('click', async () => {
        disconnectAcumaticaBtn.disabled = true;
        disconnectAcumaticaBtn.textContent = 'Disconnecting...';
        
        const result = await connectionManager.disconnectFromAcumatica();
        
        if (result.success) {
          this.notificationSystem.addNotification('Disconnected from Acumatica.', 'success');
          this.updateConnectionIcon();
          this.showConnectionPopup(); // Refresh popup
          
          // Dispatch event
          document.dispatchEvent(new CustomEvent('acumaticaConnectionChanged', { 
            detail: { connected: false } 
          }));
        } else {
          this.notificationSystem.addNotification(`Failed to disconnect: ${result.error}`, 'error');
          disconnectAcumaticaBtn.textContent = 'Disconnect';
          disconnectAcumaticaBtn.disabled = false;
        }
      });
    }
    
    // Monday.com connection form
    const mondayForm = document.getElementById('mondayForm');
    if (mondayForm) {
      mondayForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const apiKey = document.getElementById('mondayApiKey').value;
        
        if (!apiKey) {
          this.notificationSystem.addNotification('Please enter an API key.', 'error');
          return;
        }
        
        // Disable form
        const submitBtn = document.getElementById('connectMondayBtn');
        submitBtn.disabled = true;
        submitBtn.textContent = 'Connecting...';
        
        // Connect
        const result = await connectionManager.connectToMonday(apiKey);
        
        // Handle result
        if (result.success) {
          this.notificationSystem.addNotification('Successfully connected to Monday.com.', 'success');
          this.updateConnectionIcon();
          this.showConnectionPopup(); // Refresh popup
        } else {
          this.notificationSystem.addNotification(`Failed to connect: ${result.error}`, 'error');
          
          // Re-enable form
          submitBtn.disabled = false;
          submitBtn.textContent = 'Connect';
        }
      });
    }
    
    // Monday.com disconnect button
    const disconnectMondayBtn = document.getElementById('disconnectMondayBtn');
    if (disconnectMondayBtn) {
      disconnectMondayBtn.addEventListener('click', async () => {
        disconnectMondayBtn.disabled = true;
        disconnectMondayBtn.textContent = 'Disconnecting...';
        
        const result = await connectionManager.disconnectFromMonday();
        
        if (result.success) {
          this.notificationSystem.addNotification('Disconnected from Monday.com.', 'success');
          this.updateConnectionIcon();
          this.showConnectionPopup(); // Refresh popup
        } else {
          this.notificationSystem.addNotification(`Failed to disconnect: ${result.error}`, 'error');
          disconnectMondayBtn.textContent = 'Disconnect';
          disconnectMondayBtn.disabled = false;
        }
      });
    }
  }

  /**
   * Remove the existing popup
   */
  removeExistingPopup() {
    const popup = document.getElementById('connectionPopup');
    if (popup) {
      document.body.removeChild(popup);
      this.isPopupOpen = false;
    }
  }
}

// Create and export singleton instance
export const connectionUI = new ConnectionUI();
