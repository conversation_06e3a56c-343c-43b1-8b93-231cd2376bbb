/**
 * Standardized Modal Utility
 * Creates consistent modals across the application
 */

/**
 * Create a standardized modal
 * @param {Object} config - Modal configuration
 * @param {string} config.title - Modal title
 * @param {string} config.content - Modal content HTML
 * @param {string} config.size - Modal size ('default', 'large', 'extra-large', 'full')
 * @param {Array} config.buttons - Footer buttons configuration
 * @param {boolean} config.showLogo - Whether to show the logo in the header
 * @param {string} config.logoIcon - Material icon name for the logo
 * @param {string} config.subtitle - Optional subtitle for the header
 * @param {string} config.id - Optional ID for the modal (defaults to a timestamp-based ID)
 * @param {Function} config.onClose - Optional callback function to execute when modal is closed
 * @returns {Object} Modal object with show, hide, and close methods
 */
export function createStandardModal(config) {
    const {
        title,
        content,
        size = 'default',
        buttons = [],
        showLogo = false,
        logoIcon = 'table_chart',
        subtitle = '',
        id = `modal-${Date.now()}`,
        onClose = null,
        customWidth = null
    } = config;

    // Create modal elements
    const overlay = document.createElement('div');
    overlay.className = 'standard-modal-overlay';
    overlay.id = `${id}-overlay`;

    // Ensure consistent overlay styling
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.right = '0';
    overlay.style.bottom = '0';
    overlay.style.width = '100vw';
    overlay.style.height = '100vh';
    overlay.style.display = 'flex';
    overlay.style.justifyContent = 'center';
    overlay.style.alignItems = 'center';
    overlay.style.background = 'rgba(0, 0, 0, 0.5)';
    overlay.style.zIndex = '9999';
    overlay.style.overflow = 'hidden';
    overlay.style.margin = '0';
    overlay.style.padding = '0';

    const modal = document.createElement('div');
    modal.className = 'standard-modal';
    modal.id = id;

    // Set custom width if provided
    if (customWidth) {
        modal.style.width = customWidth;
        modal.style.maxWidth = '95%';
    } else {
        // Add size class if specified
        if (size === 'large') {
            modal.classList.add('modal-large');
        } else if (size === 'extra-large') {
            modal.classList.add('modal-extra-large');
        } else if (size === 'full') {
            modal.classList.add('modal-full');
        }
    }

    // Create header
    const header = document.createElement('div');
    header.className = 'standard-modal-header';

    // Add logo if requested
    if (showLogo) {
        const logo = document.createElement('div');
        logo.className = 'standard-modal-logo';
        logo.innerHTML = `<span class="material-icons">${logoIcon}</span>`;
        header.appendChild(logo);
    }

    // Add title
    const titleElement = document.createElement('h1');
    titleElement.textContent = title;
    header.appendChild(titleElement);

    // Add subtitle if provided
    if (subtitle) {
        const subtitleElement = document.createElement('p');
        subtitleElement.textContent = subtitle;
        header.appendChild(subtitleElement);
    }

    // Add close button
    const closeBtn = document.createElement('button');
    closeBtn.className = 'standard-modal-close';
    closeBtn.innerHTML = '<span class="material-icons">close</span>';
    closeBtn.setAttribute('aria-label', 'Close');
    header.appendChild(closeBtn);

    // Create content area
    const contentArea = document.createElement('div');
    contentArea.className = 'standard-modal-content';
    contentArea.innerHTML = content;

    // Create footer if buttons are provided
    let footer = null;
    if (buttons.length > 0) {
        footer = document.createElement('div');
        footer.className = 'standard-modal-footer';

        buttons.forEach(button => {
            const btn = document.createElement('button');
            btn.textContent = button.text;
            btn.className = button.primary ? 'primary-button' : 'secondary-button';

            if (button.id) {
                btn.id = button.id;
            }

            if (button.icon) {
                btn.innerHTML = `<span class="material-icons">${button.icon}</span> ${button.text}`;
            }

            btn.addEventListener('click', () => {
                if (button.onClick) {
                    button.onClick();
                }

                if (button.closeModal !== false) {
                    closeModal();
                }
            });

            footer.appendChild(btn);
        });
    }

    // Assemble modal
    modal.appendChild(header);
    modal.appendChild(contentArea);
    if (footer) {
        modal.appendChild(footer);
    }
    overlay.appendChild(modal);

    // Close modal function
    function closeModal() {
        // Add fade-out animation
        modal.style.animation = 'standardModalFadeOut 0.3s ease-out forwards';

        // Remove modal-open class from body
        document.body.classList.remove('modal-open');

        // Remove after animation completes
        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);

                // Call onClose callback if provided
                if (typeof onClose === 'function') {
                    onClose();
                }
            }
        }, 300);
    }

    // Close on close button click
    closeBtn.addEventListener('click', closeModal);

    // Close on overlay click (optional)
    overlay.addEventListener('click', (event) => {
        if (event.target === overlay) {
            closeModal();
        }
    });

    // Public methods
    return {
        show() {
            document.body.appendChild(overlay);
            document.body.classList.add('modal-open');
            return this;
        },
        hide() {
            overlay.style.display = 'none';
            return this;
        },
        close: closeModal,
        getElement() {
            return modal;
        },
        getOverlay() {
            return overlay;
        }
    };
}

/**
 * Create a standardized chart modal
 * @param {Object} config - Chart modal configuration
 * @returns {Object} Modal object
 */
export function createChartModal(config) {
    const {
        title = 'Create Chart',
        content = '',
        size = 'full',
        buttons = [],
        onClose = null
    } = config;

    // Default buttons for chart modal
    const defaultButtons = [
        {
            text: 'Cancel',
            primary: false,
            id: 'cancelChartBtn',
            icon: 'cancel'
        },
        {
            text: 'Create Chart',
            primary: true,
            id: 'createChartBtn',
            icon: 'bar_chart'
        }
    ];

    // Use provided buttons or defaults
    const modalButtons = buttons.length > 0 ? buttons : defaultButtons;

    // Create the modal with the same style as export-modal
    const modal = createStandardModal({
        title,
        content,
        size,
        buttons: modalButtons,
        showLogo: true,
        logoIcon: 'bar_chart',
        id: 'chart-modal',
        onClose
    });

    // Get the modal element to ensure consistent styling
    const modalElement = modal.getElement();
    const overlayElement = modal.getOverlay();

    // Ensure the modal has the correct styling
    overlayElement.style.position = 'fixed';
    overlayElement.style.top = '0';
    overlayElement.style.left = '0';
    overlayElement.style.right = '0';
    overlayElement.style.bottom = '0';
    overlayElement.style.width = '100vw';
    overlayElement.style.height = '100vh';
    overlayElement.style.display = 'flex';
    overlayElement.style.justifyContent = 'center';
    overlayElement.style.alignItems = 'center';
    overlayElement.style.background = 'rgba(0, 0, 0, 0.5)';
    overlayElement.style.zIndex = '9999';
    overlayElement.style.overflow = 'hidden';
    overlayElement.style.margin = '0';
    overlayElement.style.padding = '0';
    overlayElement.style.minWidth = '100vw';
    overlayElement.style.minHeight = '100vh';
    overlayElement.style.boxSizing = 'border-box';

    // Add chart-dialog class for specific styling
    overlayElement.classList.add('chart-dialog');
    overlayElement.classList.add('excel-modal');

    // Add event listener to ensure body has modal-open class when chart modal is shown
    const originalShow = modal.show;
    modal.show = function() {
        document.body.classList.add('modal-open');
        return originalShow.call(this);
    };

    return modal;
}

/**
 * Create a standardized export modal
 * @param {Object} config - Export modal configuration
 * @returns {Object} Modal object
 */
export function createExportModal(config = {}) {
    const {
        title = 'Export Workbook',
        size = 'large',
        onClose = null
    } = config;

    const content = `
        <div class="export-options">
            <div class="export-option" id="exportExcelBtn">
                <span class="material-icons">description</span>
                <span>Excel (.xlsx)</span>
            </div>
            <div class="export-option" id="exportEnventBtn">
                <span class="material-icons">event_note</span>
                <span>Envent (.envent)</span>
            </div>
            <div class="export-option" id="exportCsvBtn">
                <span class="material-icons">insert_drive_file</span>
                <span>CSV (.csv)</span>
            </div>
            <div class="export-option" id="exportPdfBtn">
                <span class="material-icons">picture_as_pdf</span>
                <span>PDF (.pdf)</span>
            </div>
            <div class="export-option" id="exportHtmlBtn">
                <span class="material-icons">html</span>
                <span>HTML Web Page (.html)</span>
            </div>
        </div>
    `;

    return createStandardModal({
        title,
        content,
        size,
        showLogo: false,
        id: 'export-modal',
        onClose
    });
}

/**
 * Create a standardized password protection modal
 * @param {Object} config - Password modal configuration
 * @returns {Object} Modal object
 */
export function createPasswordModal(config = {}) {
    const {
        title = 'Password Protect File',
        size = 'default',
        onClose = null,
        onProtect = null
    } = config;

    const content = `
        <div class="info-message">
            <p>Important: This file will be locked with a password and saved with a .xlsm extension.</p>
            <p>Make sure to remember your password as only you will be able to open this file using this application.</p>
            <p>Standard Excel applications will not be able to open this file.</p>
        </div>
        <div class="password-form">
            <div class="form-group">
                <label for="password">Password:</label>
                <div class="password-input-container">
                    <input type="password" id="password" class="form-control" placeholder="Enter password">
                    <button type="button" class="toggle-password" title="Show Password">
                        <span class="material-icons">visibility</span>
                    </button>
                </div>
            </div>
            <div class="form-group">
                <label for="confirmPassword">Confirm Password:</label>
                <div class="password-input-container">
                    <input type="password" id="confirmPassword" class="form-control" placeholder="Confirm password">
                    <button type="button" class="toggle-password" title="Show Password">
                        <span class="material-icons">visibility</span>
                    </button>
                </div>
            </div>
            <div id="passwordError" class="error-message" style="display: none;"></div>
        </div>
    `;

    const buttons = [
        {
            text: 'Cancel',
            primary: false,
            id: 'cancelProtectionBtn',
            icon: 'cancel'
        },
        {
            text: 'Protect & Export',
            primary: true,
            id: 'exportProtectedBtn',
            icon: 'lock',
            closeModal: false,
            onClick: () => {
                if (typeof onProtect === 'function') {
                    onProtect();
                }
            }
        }
    ];

    const modal = createStandardModal({
        title,
        content,
        size,
        buttons,
        showLogo: true,
        logoIcon: 'lock',
        id: 'password-modal',
        onClose
    });

    // Add event listeners for password toggle buttons after the modal is shown
    modal.show();

    const togglePasswordBtns = document.querySelectorAll('.toggle-password');
    togglePasswordBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const input = this.parentElement.querySelector('input');
            const icon = this.querySelector('.material-icons');
            if (input.type === 'password') {
                input.type = 'text';
                icon.textContent = 'visibility_off';
                this.title = 'Hide Password';
            } else {
                input.type = 'password';
                icon.textContent = 'visibility';
                this.title = 'Show Password';
            }
        });
    });

    // Focus the password input
    document.getElementById('password').focus();

    return modal;
}

/**
 * Create a standardized password prompt modal for opening protected files
 * @param {Object} config - Password prompt configuration
 * @returns {Object} Modal object
 */
export function createPasswordPromptModal(config = {}) {
    const {
        title = 'Password Protected File',
        fileName = 'Unknown file',
        onUnlock = null,
        onClose = null
    } = config;

    const content = `
        <div class="info-message">
            <p>This file is password protected.</p>
            <p>Please enter the password to unlock and open this file.</p>
            <p>File: ${fileName}</p>
        </div>
        <div class="password-form">
            <div class="form-group">
                <label for="password">Password:</label>
                <div class="password-input-container">
                    <input type="password" id="password" class="form-control" placeholder="Enter password">
                    <button type="button" class="toggle-password" title="Show Password">
                        <span class="material-icons">visibility</span>
                    </button>
                </div>
            </div>
            <div id="passwordError" class="error-message" style="display: none;"></div>
        </div>
    `;

    const buttons = [
        {
            text: 'Cancel',
            primary: false,
            id: 'cancelUnlockBtn',
            icon: 'cancel'
        },
        {
            text: 'Unlock File',
            primary: true,
            id: 'unlockFileBtn',
            icon: 'lock_open',
            closeModal: false,
            onClick: () => {
                if (typeof onUnlock === 'function') {
                    onUnlock();
                }
            }
        }
    ];

    const modal = createStandardModal({
        title,
        content,
        size: 'default',
        buttons,
        showLogo: true,
        logoIcon: 'lock',
        id: 'password-prompt-modal',
        onClose
    });

    // Add event listeners for password toggle buttons after the modal is shown
    modal.show();

    const togglePasswordBtn = document.querySelector('.toggle-password');
    if (togglePasswordBtn) {
        togglePasswordBtn.addEventListener('click', function() {
            const input = this.parentElement.querySelector('input');
            const icon = this.querySelector('.material-icons');
            if (input.type === 'password') {
                input.type = 'text';
                icon.textContent = 'visibility_off';
                this.title = 'Hide Password';
            } else {
                input.type = 'password';
                icon.textContent = 'visibility';
                this.title = 'Show Password';
            }
        });
    }

    // Focus the password input
    document.getElementById('password').focus();

    return modal;
}

/**
 * Create a standardized library demo modal
 * @param {Object} config - Library demo modal configuration
 * @returns {Object} Modal object
 */
export function createLibraryDemoModal(config = {}) {
    const {
        title = 'Library Demo',
        onClose = null
    } = config;

    const content = `
        <div class="demo-section">
            <h3>Excel Libraries</h3>
            <div class="demo-controls">
                <button id="createWorkbookBtn">Create Workbook</button>
                <button id="exportCsvBtn">Export to CSV</button>
            </div>
            <div id="excelDemoResult" class="demo-result"></div>
        </div>
        <div class="demo-section">
            <h3>Chart Libraries</h3>
            <div class="demo-controls">
                <button id="createChartBtn">Create Chart</button>
                <button id="createGridBtn">Create Grid</button>
            </div>
            <div id="chartDemoResult" class="demo-result"></div>
        </div>
        <div class="demo-section">
            <h3>Grid Libraries</h3>
            <div class="demo-controls">
                <button id="createTableBtn">Create Table</button>
                <button id="createPivotBtn">Create Pivot</button>
            </div>
            <div id="gridDemoResult" class="demo-result"></div>
        </div>
        <div class="demo-section">
            <h3>Utility Libraries</h3>
            <div class="demo-controls">
                <button id="dateFormatBtn">Format Date</button>
                <button id="exportPdfBtn">Export to PDF</button>
                <button id="createTourBtn">Create Tour</button>
            </div>
            <div id="utilityDemoResult" class="demo-result"></div>
        </div>
    `;

    return createStandardModal({
        title,
        content,
        size: 'large',
        showLogo: true,
        logoIcon: 'library_books',
        id: 'library-demo-modal',
        onClose
    });
}

/**
 * Create a standardized page setup modal
 * @param {Object} config - Page setup modal configuration
 * @returns {Object} Modal object
 */
export function createPageSetupModal(config = {}) {
    const {
        title = 'Page Setup',
        onApply = null,
        onClose = null
    } = config;

    const content = `
        <div class="page-setup-form">
            <div class="form-group">
                <label for="pageSize">Paper Size:</label>
                <select id="pageSize" class="form-control">
                    <option value="letter">Letter (8.5" x 11")</option>
                    <option value="legal">Legal (8.5" x 14")</option>
                    <option value="a4">A4 (210mm x 297mm)</option>
                    <option value="a3">A3 (297mm x 420mm)</option>
                </select>
            </div>
            <div class="form-group">
                <label for="pageOrientation">Orientation:</label>
                <select id="pageOrientation" class="form-control">
                    <option value="portrait">Portrait</option>
                    <option value="landscape">Landscape</option>
                </select>
            </div>
            <div class="form-group">
                <label>Margins:</label>
                <div class="margin-inputs">
                    <div>
                        <label for="marginTop">Top:</label>
                        <input type="number" id="marginTop" class="form-control" value="1.0" min="0" step="0.1">
                    </div>
                    <div>
                        <label for="marginRight">Right:</label>
                        <input type="number" id="marginRight" class="form-control" value="1.0" min="0" step="0.1">
                    </div>
                    <div>
                        <label for="marginBottom">Bottom:</label>
                        <input type="number" id="marginBottom" class="form-control" value="1.0" min="0" step="0.1">
                    </div>
                    <div>
                        <label for="marginLeft">Left:</label>
                        <input type="number" id="marginLeft" class="form-control" value="1.0" min="0" step="0.1">
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label for="headerText">Header:</label>
                <input type="text" id="headerText" class="form-control" placeholder="Enter header text">
            </div>
            <div class="form-group">
                <label for="footerText">Footer:</label>
                <input type="text" id="footerText" class="form-control" placeholder="Enter footer text">
            </div>
        </div>
    `;

    const buttons = [
        {
            text: 'Cancel',
            primary: false,
            id: 'pageSetupCancelBtn',
            icon: 'cancel'
        },
        {
            text: 'Apply',
            primary: true,
            id: 'pageSetupApplyBtn',
            icon: 'check',
            closeModal: false,
            onClick: () => {
                if (typeof onApply === 'function') {
                    onApply();
                }
            }
        }
    ];

    return createStandardModal({
        title,
        content,
        size: 'default',
        buttons,
        showLogo: true,
        logoIcon: 'settings_applications',
        id: 'page-setup-modal',
        onClose
    });
}

/**
 * Create a standardized workbook settings modal
 * @param {Object} config - Workbook settings modal configuration
 * @returns {Object} Modal object
 */
export function createWorkbookSettingsModal(config = {}) {
    const {
        title = 'Workbook Settings',
        onApply = null,
        onClose = null
    } = config;

    const content = `
        <div class="workbook-settings-form">
            <div class="form-group">
                <label for="workbookName">Workbook Name:</label>
                <input type="text" id="workbookName" class="form-control" placeholder="Enter workbook name">
            </div>
            <div class="form-group">
                <label for="workbookAuthor">Author:</label>
                <input type="text" id="workbookAuthor" class="form-control" placeholder="Enter author name">
            </div>
            <div class="form-group">
                <label for="workbookCompany">Company:</label>
                <input type="text" id="workbookCompany" class="form-control" placeholder="Enter company name">
            </div>
            <div class="form-group">
                <label for="workbookDescription">Description:</label>
                <textarea id="workbookDescription" class="form-control" rows="3" placeholder="Enter workbook description"></textarea>
            </div>
            <div class="form-group">
                <label>Calculation Options:</label>
                <div class="radio-group">
                    <div>
                        <input type="radio" id="calcAuto" name="calcMode" value="auto" checked>
                        <label for="calcAuto">Automatic</label>
                    </div>
                    <div>
                        <input type="radio" id="calcManual" name="calcMode" value="manual">
                        <label for="calcManual">Manual</label>
                    </div>
                </div>
            </div>
        </div>
    `;

    const buttons = [
        {
            text: 'Cancel',
            primary: false,
            id: 'workbookSettingsCancelBtn',
            icon: 'cancel'
        },
        {
            text: 'Apply',
            primary: true,
            id: 'workbookSettingsApplyBtn',
            icon: 'check',
            closeModal: false,
            onClick: () => {
                if (typeof onApply === 'function') {
                    onApply();
                }
            }
        }
    ];

    return createStandardModal({
        title,
        content,
        size: 'default',
        buttons,
        showLogo: true,
        logoIcon: 'settings',
        id: 'workbook-settings-modal',
        onClose
    });
}

// Export the utility functions
export default {
    createStandardModal,
    createChartModal,
    createExportModal,
    createPasswordModal,
    createPasswordPromptModal,
    createLibraryDemoModal,
    createPageSetupModal,
    createWorkbookSettingsModal
};
