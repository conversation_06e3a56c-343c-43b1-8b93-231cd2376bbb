// js/renderer.js
import { applyCellStylesToElement } from './styler.js';
import { setActiveCell, setupCellSelection } from './selection_manager.js';

let currentSheetRef;
let mainScriptUpdateToolbarFunc; // Function reference from main_script.js to update toolbar

export function initializeRenderer(updateToolbarFunc) {
    mainScriptUpdateToolbarFunc = updateToolbarFunc;
}

export function renderSheetData(sheet, containerElement) {
    currentSheetRef = sheet;
    if (!currentSheetRef) {
        containerElement.innerHTML = '<p>No sheet data to display (renderer).</p>';
        return null;
    }
    containerElement.innerHTML = ''; // Clear previous content
    const table = document.createElement('table');
    table.className = 'excel-table';

    const usedRange = currentSheetRef.usedRange();
    let maxRenderCols = 15; // Default if sheet is effectively empty
    let maxRenderRows = 30; // Default if sheet is effectively empty

    if (usedRange) {
        maxRenderCols = Math.max(maxRenderCols, usedRange.endCell().columnNumber());
        maxRenderRows = Math.max(maxRenderRows, usedRange.endCell().rowNumber());
    } else if (typeof currentSheetRef.cell !== 'function' || currentSheetRef.cell("A1").value() === undefined) {
        // If usedRange is null and A1 is also undefined, treat as empty and use defaults
        console.warn("Sheet usedRange is null and A1 is empty, rendering default grid size.");
    }


    const aCharCode = 'A'.charCodeAt(0);
    const headerRowEl = table.insertRow();
    const cornerCell = headerRowEl.insertCell();
    cornerCell.className = 'excel-header-corner'; // For styling the top-left corner

    // Add column headers (A, B, C, etc.)
    for (let c = 0; c < maxRenderCols; c++) {
        const th = document.createElement('th');
        th.textContent = String.fromCharCode(aCharCode + c);
        th.className = 'excel-col-header column-header'; // Add both classes for compatibility
        th.dataset.col = c + 1; // Store 1-based column index
        th.title = `Select entire column ${String.fromCharCode(aCharCode + c)}`;
        th.style.cursor = 'pointer'; // Make it clear it's clickable
        th.setAttribute('role', 'columnheader'); // For accessibility

        // Add a direct click handler to ensure it works
        th.addEventListener('click', function(event) {
            console.log(`Direct column header click on ${this.textContent}, column: ${this.dataset.col}`);
            if (window.selectEntireColumn) {
                window.selectEntireColumn(parseInt(this.dataset.col), table);
                event.preventDefault();
                event.stopPropagation();
            }
        });

        console.log(`Creating column header: ${th.textContent}, dataset.col=${th.dataset.col}`);
        headerRowEl.appendChild(th);
    }

    // Add rows with row headers
    for (let r = 1; r <= maxRenderRows; r++) {
        const rowElement = table.insertRow();

        // Create row header cell - using insertCell instead of createElement to ensure proper DOM structure
        const rowHeaderCell = rowElement.insertCell(0);
        rowHeaderCell.textContent = r; // Display row number
        rowHeaderCell.className = 'excel-row-header row-header'; // Add both classes for compatibility
        rowHeaderCell.dataset.row = r; // Store row number
        rowHeaderCell.title = `Select entire row ${r}`;
        rowHeaderCell.style.cursor = 'pointer'; // Make it clear it's clickable
        rowHeaderCell.setAttribute('role', 'rowheader'); // For accessibility

        // Add a direct click handler to ensure it works
        rowHeaderCell.addEventListener('click', function(event) {
            console.log(`Direct row header click on ${this.textContent}, row: ${this.dataset.row}`);
            if (window.selectEntireRow) {
                window.selectEntireRow(parseInt(this.dataset.row), table);
                event.preventDefault();
                event.stopPropagation();
            }
        });

        console.log(`Creating row header: ${rowHeaderCell.textContent}, dataset.row=${rowHeaderCell.dataset.row}`);

        for (let c = 1; c <= maxRenderCols; c++) {
            const cell = currentSheetRef.cell(r, c); // Get cell object
            const cellAddress = cell.address(); // Get address like "A1"

            const tdElement = rowElement.insertCell();
            const input = document.createElement('input');
            input.type = 'text';
            input.className = 'cell-input';
            input.dataset.address = cellAddress;
            input.dataset.row = r; // Store 1-based row
            input.dataset.col = c; // Store 1-based col

            // Apply styles from XlsxPopulate model to HTML input
            applyCellStylesToElement(cell, input); // From styler.js

            input.addEventListener('change', (e) => {
                cell.value(e.target.value); // Update XlsxPopulate model
                if (window.updateFormulaBar) window.updateFormulaBar(cell, input);
                if (window.statusUpdated) window.statusUpdated(`Cell ${cellAddress} updated.`);
            });
            input.addEventListener('focus', (e) => {
                setActiveCell(e.target); // From selection_manager.js
                if (mainScriptUpdateToolbarFunc) mainScriptUpdateToolbarFunc(); // Update toolbar
                if (window.updateFormulaBar) window.updateFormulaBar(cell, input);
                if (window.statusUpdated) window.statusUpdated(`Cell ${cellAddress} selected.`);
            });

            tdElement.appendChild(input);
        }
    }
    containerElement.appendChild(table);

    // Initialize cell selection functionality
    setupCellSelection(table);

    console.log('Cell selection functionality initialized');

    return table; // Return the created table element for selection_manager
}