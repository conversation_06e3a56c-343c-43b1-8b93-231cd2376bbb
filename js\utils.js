// js/utils.js
// Comprehensive utility functions for all libraries

/**
 * Utility class that provides a unified interface to work with all libraries
 */
class Utils {
    constructor() {
        // Initialize library availability
        this.availableLibraries = {
            // Excel libraries
            xlsxPopulate: typeof XlsxPopulate !== 'undefined',
            xlsx: typeof XLSX !== 'undefined',
            exceljs: typeof ExcelJS !== 'undefined',
            luckysheet: typeof luckysheet !== 'undefined',
            handsontable: typeof Handsontable !== 'undefined',

            // Chart libraries
            apexcharts: typeof ApexCharts !== 'undefined',
            echarts: typeof echarts !== 'undefined',

            // Grid libraries
            gridjs: typeof gridjs !== 'undefined',
            gridstack: typeof GridStack !== 'undefined',

            // Date libraries
            dayjs: typeof dayjs !== 'undefined',

            // Editor libraries
            monaco: typeof monaco !== 'undefined',
            tiptap: typeof tiptap !== 'undefined',

            // Utility libraries
            jszip: typeof JSZip !== 'undefined',
            papaparse: typeof Papa !== 'undefined',
            pdfLib: typeof PDFLib !== 'undefined',
            tesseract: typeof Tesseract !== 'undefined',
            html2canvas: typeof html2canvas !== 'undefined',
            sortable: typeof Sortable !== 'undefined',
            motion: typeof Motion !== 'undefined',
            intro: typeof introJs !== 'undefined'
        };

        console.log('Available libraries:', this.availableLibraries);

        // Initialize sub-utilities
        this.excel = this._initExcelUtils();
        this.chart = this._initChartUtils();
        this.grid = this._initGridUtils();
        this.date = this._initDateUtils();
        this.editor = this._initEditorUtils();
        this.export = this._initExportUtils();
        this.ocr = this._initOcrUtils();
        this.ui = this._initUiUtils();
    }

    /**
     * Initialize Excel utilities
     * @private
     * @returns {Object} Excel utilities
     */
    _initExcelUtils() {
        return {
            // Current active library
            activeLibrary: 'xlsx-populate',

            // Set active library
            setActiveLibrary: (library) => {
                if (['xlsx-populate', 'xlsx', 'exceljs', 'luckysheet', 'handsontable'].includes(library)) {
                    this.excel.activeLibrary = library;
                    return true;
                }
                return false;
            },

            // Create a new blank workbook
            createBlankWorkbook: async () => {
                try {
                    switch (this.excel.activeLibrary) {
                        case 'xlsx-populate':
                            return await XlsxPopulate.fromBlankAsync();
                        case 'xlsx':
                            return XLSX.utils.book_new();
                        case 'exceljs':
                            return new ExcelJS.Workbook();
                        case 'luckysheet':
                            // Initialize luckysheet with a blank sheet
                            return [{
                                name: 'Sheet1',
                                index: 0,
                                status: 1,
                                order: 0,
                                celldata: [],
                                config: {},
                                luckysheet_select_save: [],
                                calcChain: [],
                                scrollLeft: 0,
                                scrollTop: 0
                            }];
                        case 'handsontable':
                            return { data: [[]], workbook: XLSX.utils.book_new() };
                        default:
                            throw new Error(`Library ${this.excel.activeLibrary} not supported`);
                    }
                } catch (error) {
                    console.error('Error creating blank workbook:', error);
                    throw error;
                }
            },

            // Load a workbook from a file
            loadWorkbook: async (file) => {
                try {
                    const data = await file.arrayBuffer();

                    switch (this.excel.activeLibrary) {
                        case 'xlsx-populate':
                            return await XlsxPopulate.fromDataAsync(data);
                        case 'xlsx':
                            return XLSX.read(data, { type: 'array' });
                        case 'exceljs':
                            const workbook = new ExcelJS.Workbook();
                            await workbook.xlsx.load(data);
                            return workbook;
                        case 'luckysheet':
                            // Convert Excel to Luckysheet format
                            const xlsxWb = XLSX.read(data, { type: 'array' });
                            return this.excel._convertXlsxToLuckysheet(xlsxWb);
                        case 'handsontable':
                            // Convert to Handsontable format
                            const hotWb = XLSX.read(data, { type: 'array' });
                            const firstSheetName = hotWb.SheetNames[0];
                            const hotData = XLSX.utils.sheet_to_json(hotWb.Sheets[firstSheetName], { header: 1 });
                            return { data: hotData, workbook: hotWb };
                        default:
                            throw new Error(`Library ${this.excel.activeLibrary} not supported`);
                    }
                } catch (error) {
                    console.error('Error loading workbook:', error);
                    throw error;
                }
            },

            // Save a workbook to a file
            saveWorkbook: async (workbook, filename = 'workbook.xlsx') => {
                try {
                    let blob;

                    switch (this.excel.activeLibrary) {
                        case 'xlsx-populate':
                            blob = await workbook.outputAsync();
                            break;
                        case 'xlsx':
                            const wbout = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
                            blob = new Blob([wbout], { type: 'application/octet-stream' });
                            break;
                        case 'exceljs':
                            const buffer = await workbook.xlsx.writeBuffer();
                            blob = new Blob([buffer], { type: 'application/octet-stream' });
                            break;
                        case 'luckysheet':
                            // Convert Luckysheet to XLSX
                            const lsWb = this.excel._convertLuckysheetToXlsx(workbook);
                            const lsOut = XLSX.write(lsWb, { bookType: 'xlsx', type: 'array' });
                            blob = new Blob([lsOut], { type: 'application/octet-stream' });
                            break;
                        case 'handsontable':
                            // Convert Handsontable to XLSX
                            const hotWb = workbook.workbook || XLSX.utils.book_new();
                            const hotSheet = XLSX.utils.aoa_to_sheet(workbook.data);
                            XLSX.utils.book_append_sheet(hotWb, hotSheet, 'Sheet1');
                            const hotOut = XLSX.write(hotWb, { bookType: 'xlsx', type: 'array' });
                            blob = new Blob([hotOut], { type: 'application/octet-stream' });
                            break;
                        default:
                            throw new Error(`Library ${this.excel.activeLibrary} not supported`);
                    }

                    // Create download link
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    a.click();
                    URL.revokeObjectURL(url);

                    return true;
                } catch (error) {
                    console.error('Error saving workbook:', error);
                    throw error;
                }
            },

            // Get the first sheet from a workbook
            getFirstSheet: (workbook) => {
                try {
                    switch (this.excel.activeLibrary) {
                        case 'xlsx-populate':
                            return workbook.sheet(0);
                        case 'xlsx':
                            const firstSheetName = workbook.SheetNames[0];
                            return workbook.Sheets[firstSheetName];
                        case 'exceljs':
                            return workbook.getWorksheet(1);
                        case 'luckysheet':
                            return workbook[0];
                        case 'handsontable':
                            return workbook;
                        default:
                            throw new Error(`Library ${this.excel.activeLibrary} not supported`);
                    }
                } catch (error) {
                    console.error('Error getting first sheet:', error);
                    throw error;
                }
            },

            // Convert XLSX to Luckysheet format
            _convertXlsxToLuckysheet: (workbook) => {
                const result = [];

                workbook.SheetNames.forEach(sheetName => {
                    const worksheet = workbook.Sheets[sheetName];
                    const luckysheetData = {
                        name: sheetName,
                        index: workbook.SheetNames.indexOf(sheetName),
                        status: 1,
                        order: workbook.SheetNames.indexOf(sheetName),
                        celldata: [],
                        config: {},
                        luckysheet_select_save: [],
                        calcChain: [],
                        scrollLeft: 0,
                        scrollTop: 0
                    };

                    // Convert cells
                    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1');
                    for (let r = range.s.r; r <= range.e.r; ++r) {
                        for (let c = range.s.c; c <= range.e.c; ++c) {
                            const cellAddress = XLSX.utils.encode_cell({ r, c });
                            const cell = worksheet[cellAddress];

                            if (cell) {
                                luckysheetData.celldata.push({
                                    r,
                                    c,
                                    v: {
                                        v: cell.v,
                                        ct: { fa: 'General', t: 'g' },
                                        m: cell.w || cell.v
                                    }
                                });
                            }
                        }
                    }

                    result.push(luckysheetData);
                });

                return result;
            },

            // Convert Luckysheet to XLSX format
            _convertLuckysheetToXlsx: (luckysheetData) => {
                const workbook = XLSX.utils.book_new();

                luckysheetData.forEach(sheet => {
                    const worksheet = XLSX.utils.aoa_to_sheet([]);
                    const range = { s: { r: 0, c: 0 }, e: { r: 0, c: 0 } };

                    // Process cell data
                    sheet.celldata.forEach(cell => {
                        const cellAddress = XLSX.utils.encode_cell({ r: cell.r, c: cell.c });
                        const xlsxCell = {
                            v: cell.v.v,
                            t: cell.v.ct.t === 'n' ? 'n' : 's',
                            w: cell.v.m
                        };

                        worksheet[cellAddress] = xlsxCell;

                        // Update range
                        if (cell.r > range.e.r) range.e.r = cell.r;
                        if (cell.c > range.e.c) range.e.c = cell.c;
                    });

                    // Set range
                    worksheet['!ref'] = XLSX.utils.encode_range(range);

                    // Add sheet to workbook
                    XLSX.utils.book_append_sheet(workbook, worksheet, sheet.name);
                });

                return workbook;
            }
        };
    }

    /**
     * Initialize Chart utilities
     * @private
     * @returns {Object} Chart utilities
     */
    _initChartUtils() {
        return {
            // Active chart library
            activeLibrary: 'apex',

            // Create a chart
            createChart: (container, type, data, options = {}) => {
                try {
                    // Clear container first
                    container.innerHTML = '';

                    // Default options
                    const defaultOptions = {
                        title: 'Chart',
                        colors: options.colors || ['#008FFB', '#00E396', '#FEB019', '#FF4560', '#775DD0'],
                        height: options.height || 350,
                        width: options.width || '100%',
                        seriesName: options.seriesName || 'Series 1'
                    };

                    // Merge options
                    const chartOptions = { ...defaultOptions, ...options };

                    // Ensure data is in the correct format
                    const chartData = {
                        categories: data.categories || [],
                        values: data.values || data
                    };

                    if (this.availableLibraries.apexcharts) {
                        this.activeLibrary = 'apex';

                        // Map chart type to ApexCharts type
                        let chartType = type;
                        if (type === 'column') chartType = 'bar';

                        // Create chart options
                        const apexOptions = {
                            chart: {
                                type: chartType,
                                height: chartOptions.height,
                                width: chartOptions.width,
                                toolbar: {
                                    show: true
                                },
                                animations: {
                                    enabled: true,
                                    easing: 'easeinout',
                                    speed: 800
                                }
                            },
                            series: [{
                                name: chartOptions.seriesName,
                                data: chartData.values
                            }],
                            xaxis: {
                                categories: chartData.categories,
                                labels: {
                                    style: {
                                        fontSize: '12px'
                                    }
                                }
                            },
                            title: {
                                text: chartOptions.title,
                                align: 'center',
                                style: {
                                    fontSize: '16px',
                                    fontWeight: 'bold'
                                }
                            },
                            colors: chartOptions.colors,
                            theme: {
                                mode: 'light'
                            },
                            plotOptions: {
                                bar: {
                                    horizontal: type === 'bar',
                                    columnWidth: '70%',
                                    borderRadius: 4
                                },
                                pie: {
                                    donut: {
                                        size: type === 'donut' ? '50%' : '0%'
                                    }
                                }
                            },
                            dataLabels: {
                                enabled: type === 'pie' || type === 'donut'
                            },
                            stroke: {
                                curve: 'smooth',
                                width: type === 'line' || type === 'area' ? 3 : 0
                            },
                            fill: {
                                type: type === 'area' ? 'gradient' : 'solid',
                                opacity: type === 'area' ? 0.7 : 1
                            },
                            tooltip: {
                                enabled: true,
                                theme: 'light'
                            }
                        };

                        // Create and render the chart
                        const chart = new ApexCharts(container, apexOptions);
                        chart.render();
                        return chart;
                    } else if (this.availableLibraries.echarts) {
                        this.activeLibrary = 'echarts';

                        // Initialize ECharts instance
                        const chart = echarts.init(container);

                        // Map chart type to ECharts type
                        let chartType = type;
                        if (type === 'column') chartType = 'bar';
                        if (type === 'area') chartType = 'line'; // ECharts uses areaStyle for area charts

                        // Prepare chart options
                        let option = {
                            title: {
                                text: chartOptions.title,
                                left: 'center',
                                textStyle: {
                                    fontSize: 16,
                                    fontWeight: 'bold'
                                }
                            },
                            tooltip: {
                                trigger: 'axis'
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '3%',
                                containLabel: true
                            },
                            color: chartOptions.colors
                        };

                        // Configure based on chart type
                        if (type === 'pie' || type === 'donut') {
                            // Pie chart configuration
                            option.series = [{
                                name: chartOptions.seriesName,
                                type: 'pie',
                                radius: type === 'donut' ? ['40%', '70%'] : '70%',
                                center: ['50%', '50%'],
                                data: chartData.categories.map((cat, index) => ({
                                    name: cat,
                                    value: chartData.values[index]
                                })),
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                },
                                label: {
                                    show: true,
                                    formatter: '{b}: {c} ({d}%)'
                                }
                            }];
                        } else {
                            // Bar, line, area, column charts
                            option.xAxis = {
                                type: 'category',
                                data: chartData.categories,
                                axisLabel: {
                                    rotate: chartData.categories.length > 5 ? 45 : 0
                                }
                            };

                            option.yAxis = {
                                type: 'value'
                            };

                            option.series = [{
                                name: chartOptions.seriesName,
                                type: chartType,
                                data: chartData.values,
                                areaStyle: type === 'area' ? {} : null,
                                smooth: type === 'line' || type === 'area'
                            }];
                        }

                        // Set options and render
                        chart.setOption(option);

                        // Handle resize
                        window.addEventListener('resize', () => {
                            chart.resize();
                        });

                        return chart;
                    } else {
                        // Fallback to simple HTML chart
                        this.activeLibrary = 'html';
                        return this._createSimpleChart(container, type, chartData, chartOptions);
                    }
                } catch (error) {
                    console.error('Error creating chart:', error);
                    // Fallback to simple HTML chart
                    return this._createSimpleChart(container, type, data, options);
                }
            },

            // Create a simple HTML chart as fallback
            _createSimpleChart: (container, type, data, options = {}) => {
                try {
                    // Clear container first
                    container.innerHTML = '';

                    // Default options
                    const defaultOptions = {
                        title: 'Chart',
                        colors: options.colors || ['#008FFB', '#00E396', '#FEB019', '#FF4560', '#775DD0']
                    };

                    // Merge options
                    const chartOptions = { ...defaultOptions, ...options };

                    // Ensure data is in the correct format
                    const chartData = {
                        categories: data.categories || [],
                        values: data.values || data
                    };

                    // Create chart container
                    const chartDiv = document.createElement('div');
                    chartDiv.className = 'simple-chart';
                    chartDiv.style.width = '100%';
                    chartDiv.style.height = '100%';
                    chartDiv.style.padding = '10px';
                    chartDiv.style.boxSizing = 'border-box';

                    // Add title
                    const titleEl = document.createElement('h3');
                    titleEl.textContent = chartOptions.title;
                    titleEl.style.textAlign = 'center';
                    titleEl.style.margin = '0 0 15px 0';
                    titleEl.style.fontSize = '16px';
                    chartDiv.appendChild(titleEl);

                    // Create chart based on type
                    if (type === 'pie' || type === 'donut') {
                        this._createSimplePieChart(chartDiv, chartData, chartOptions, type === 'donut');
                    } else {
                        this._createSimpleBarChart(chartDiv, chartData, chartOptions, type);
                    }

                    container.appendChild(chartDiv);

                    return {
                        container: container,
                        update: (newData) => {
                            return this._createSimpleChart(container, type, newData, options);
                        }
                    };
                } catch (error) {
                    console.error('Error creating simple chart:', error);
                    container.innerHTML = `<div style="color: red; padding: 20px;">Error creating chart: ${error.message}</div>`;
                    return null;
                }
            },

            // Create a simple pie chart
            _createSimplePieChart: (container, data, options, isDonut) => {
                // Create pie container
                const pieContainer = document.createElement('div');
                pieContainer.style.display = 'flex';
                pieContainer.style.flexDirection = 'column';
                pieContainer.style.alignItems = 'center';
                pieContainer.style.height = 'calc(100% - 50px)';

                // Create pie chart
                const pieChart = document.createElement('div');
                pieChart.style.position = 'relative';
                pieChart.style.width = '200px';
                pieChart.style.height = '200px';
                pieChart.style.borderRadius = '50%';
                pieChart.style.overflow = 'hidden';
                pieChart.style.background = '#f0f0f0';

                // Calculate total
                const total = data.values.reduce((sum, val) => sum + val, 0);

                // Create pie segments
                let startAngle = 0;
                data.values.forEach((value, index) => {
                    if (value <= 0) return;

                    const percentage = value / total;
                    const endAngle = startAngle + percentage * 360;
                    const color = options.colors[index % options.colors.length];

                    // Create segment
                    const segment = document.createElement('div');
                    segment.style.position = 'absolute';
                    segment.style.top = '0';
                    segment.style.left = '0';
                    segment.style.width = '100%';
                    segment.style.height = '100%';
                    segment.style.background = color;
                    segment.style.clipPath = `conic-gradient(from ${startAngle}deg, ${color} ${percentage * 100}%, transparent ${percentage * 100}%)`;

                    pieChart.appendChild(segment);
                    startAngle = endAngle;
                });

                // Create donut hole if needed
                if (isDonut) {
                    const hole = document.createElement('div');
                    hole.style.position = 'absolute';
                    hole.style.top = '25%';
                    hole.style.left = '25%';
                    hole.style.width = '50%';
                    hole.style.height = '50%';
                    hole.style.borderRadius = '50%';
                    hole.style.background = 'white';
                    hole.style.zIndex = '2';
                    pieChart.appendChild(hole);
                }

                pieContainer.appendChild(pieChart);

                // Create legend
                const legend = document.createElement('div');
                legend.style.display = 'flex';
                legend.style.flexWrap = 'wrap';
                legend.style.justifyContent = 'center';
                legend.style.marginTop = '20px';

                data.categories.forEach((category, index) => {
                    const legendItem = document.createElement('div');
                    legendItem.style.display = 'flex';
                    legendItem.style.alignItems = 'center';
                    legendItem.style.margin = '5px 10px';

                    const colorBox = document.createElement('div');
                    colorBox.style.width = '12px';
                    colorBox.style.height = '12px';
                    colorBox.style.backgroundColor = options.colors[index % options.colors.length];
                    colorBox.style.marginRight = '5px';

                    const label = document.createElement('span');
                    label.textContent = `${category}: ${data.values[index]}`;
                    label.style.fontSize = '12px';

                    legendItem.appendChild(colorBox);
                    legendItem.appendChild(label);
                    legend.appendChild(legendItem);
                });

                pieContainer.appendChild(legend);
                container.appendChild(pieContainer);
            },

            // Create a simple bar/column/line chart
            _createSimpleBarChart: (container, data, options, type) => {
                // Create chart container
                const chartContainer = document.createElement('div');
                chartContainer.style.display = 'flex';
                chartContainer.style.flexDirection = 'column';
                chartContainer.style.height = 'calc(100% - 50px)';
                chartContainer.style.position = 'relative';

                // Create chart area
                const chartArea = document.createElement('div');
                chartArea.style.flex = '1';
                chartArea.style.display = 'flex';
                chartArea.style.alignItems = 'flex-end';
                chartArea.style.justifyContent = 'space-around';
                chartArea.style.padding = '0 10px 20px 30px';
                chartArea.style.position = 'relative';

                // Add y-axis line
                const yAxis = document.createElement('div');
                yAxis.style.position = 'absolute';
                yAxis.style.left = '0';
                yAxis.style.top = '0';
                yAxis.style.bottom = '20px';
                yAxis.style.width = '1px';
                yAxis.style.backgroundColor = '#ccc';
                chartArea.appendChild(yAxis);

                // Add x-axis line
                const xAxis = document.createElement('div');
                xAxis.style.position = 'absolute';
                xAxis.style.left = '0';
                xAxis.style.right = '0';
                xAxis.style.bottom = '20px';
                xAxis.style.height = '1px';
                xAxis.style.backgroundColor = '#ccc';
                chartArea.appendChild(xAxis);

                // Find max value for scaling
                const maxValue = Math.max(...data.values, 1);

                // Create bars/columns/points
                data.values.forEach((value, index) => {
                    const itemContainer = document.createElement('div');
                    itemContainer.style.display = 'flex';
                    itemContainer.style.flexDirection = 'column';
                    itemContainer.style.alignItems = 'center';
                    itemContainer.style.flex = '1';
                    itemContainer.style.maxWidth = `${100 / data.values.length}%`;
                    itemContainer.style.position = 'relative';
                    itemContainer.style.height = '100%';

                    const heightPercent = (value / maxValue) * 100;
                    const color = options.colors[0];

                    if (type === 'line' || type === 'area') {
                        // For line charts, create dots
                        const dot = document.createElement('div');
                        dot.style.width = '8px';
                        dot.style.height = '8px';
                        dot.style.borderRadius = '50%';
                        dot.style.backgroundColor = color;
                        dot.style.position = 'absolute';
                        dot.style.bottom = `calc(${heightPercent}% - 4px)`;
                        dot.style.left = '50%';
                        dot.style.transform = 'translateX(-50%)';
                        dot.style.zIndex = '2';

                        // Add line to next point if not the last point
                        if (index < data.values.length - 1) {
                            const nextValue = data.values[index + 1];
                            const nextHeightPercent = (nextValue / maxValue) * 100;

                            const line = document.createElement('div');
                            line.style.position = 'absolute';
                            line.style.height = '2px';
                            line.style.backgroundColor = color;
                            line.style.bottom = `${heightPercent}%`;
                            line.style.left = '50%';
                            line.style.width = `${100 / data.values.length}%`;
                            line.style.transformOrigin = 'left';

                            // Calculate angle for the line
                            const angle = Math.atan2(nextHeightPercent - heightPercent, 100) * (180 / Math.PI);
                            line.style.transform = `rotate(${-angle}deg)`;

                            itemContainer.appendChild(line);
                        }

                        // For area charts, add fill
                        if (type === 'area') {
                            const area = document.createElement('div');
                            area.style.position = 'absolute';
                            area.style.bottom = '0';
                            area.style.left = '0';
                            area.style.width = '100%';
                            area.style.height = `${heightPercent}%`;
                            area.style.backgroundColor = color;
                            area.style.opacity = '0.2';
                            itemContainer.appendChild(area);
                        }

                        itemContainer.appendChild(dot);
                    } else {
                        // For bar/column charts
                        const bar = document.createElement('div');

                        if (type === 'bar') {
                            // Horizontal bar
                            bar.style.width = `${heightPercent}%`;
                            bar.style.height = '20px';
                            bar.style.position = 'absolute';
                            bar.style.left = '0';
                            bar.style.top = '50%';
                            bar.style.transform = 'translateY(-50%)';
                        } else {
                            // Vertical column
                            bar.style.width = '60%';
                            bar.style.height = `${heightPercent}%`;
                            bar.style.position = 'absolute';
                            bar.style.bottom = '0';
                            bar.style.left = '50%';
                            bar.style.transform = 'translateX(-50%)';
                        }

                        bar.style.backgroundColor = color;
                        bar.style.borderRadius = '2px';
                        itemContainer.appendChild(bar);
                    }

                    // Add label
                    const label = document.createElement('div');
                    label.textContent = data.categories[index] || '';
                    label.style.fontSize = '10px';
                    label.style.position = 'absolute';
                    label.style.bottom = '0';
                    label.style.left = '50%';
                    label.style.transform = 'translateX(-50%) translateY(100%)';
                    label.style.textAlign = 'center';
                    label.style.whiteSpace = 'nowrap';
                    label.style.overflow = 'hidden';
                    label.style.textOverflow = 'ellipsis';
                    label.style.maxWidth = '100%';

                    itemContainer.appendChild(label);
                    chartArea.appendChild(itemContainer);
                });

                chartContainer.appendChild(chartArea);
                container.appendChild(chartContainer);
            },

            // Create a KPI card
            createKpiCard: (container, value, title, options = {}) => {
                try {
                    if (this.availableLibraries.apexcharts) {
                        const chart = new ApexCharts(container, {
                            chart: {
                                type: 'radialBar',
                                height: options.height || 200,
                                width: options.width || 200,
                                sparkline: {
                                    enabled: true
                                }
                            },
                            series: [value],
                            labels: [title],
                            plotOptions: {
                                radialBar: {
                                    hollow: {
                                        margin: 0,
                                        size: '70%'
                                    },
                                    dataLabels: {
                                        showOn: 'always',
                                        name: {
                                            show: true,
                                            fontSize: '16px',
                                            fontWeight: 600,
                                            offsetY: 10
                                        },
                                        value: {
                                            show: true,
                                            fontSize: '22px',
                                            fontWeight: 700,
                                            offsetY: -10
                                        }
                                    }
                                }
                            }
                        });
                        chart.render();
                        return chart;
                    }
                    throw new Error('ApexCharts library not available');
                } catch (error) {
                    console.error('Error creating KPI card:', error);
                    throw error;
                }
            }
        };
    }

    /**
     * Initialize Grid utilities
     * @private
     * @returns {Object} Grid utilities
     */
    _initGridUtils() {
        return {
            // Create a data grid
            createDataGrid: (container, data, options = {}) => {
                try {
                    if (this.availableLibraries.gridjs) {
                        const grid = new gridjs.Grid({
                            columns: options.columns || Object.keys(data[0] || {}),
                            data: data,
                            search: options.search !== false,
                            sort: options.sort !== false,
                            pagination: options.pagination !== false ? {
                                limit: options.pageSize || 10
                            } : false
                        });
                        grid.render(container);
                        return grid;
                    }
                    throw new Error('GridJS library not available');
                } catch (error) {
                    console.error('Error creating data grid:', error);
                    throw error;
                }
            },

            // Create a dashboard grid
            createDashboard: (container, options = {}) => {
                try {
                    if (this.availableLibraries.gridstack) {
                        const grid = GridStack.init({
                            column: options.columns || 12,
                            cellHeight: options.cellHeight || 80,
                            animate: true,
                            float: options.float !== false,
                            resizable: options.resizable !== false,
                            removable: options.removable !== false,
                            disableOneColumnMode: options.disableOneColumnMode !== false
                        }, container);
                        return grid;
                    }
                    throw new Error('GridStack library not available');
                } catch (error) {
                    console.error('Error creating dashboard grid:', error);
                    throw error;
                }
            },

            // Add a widget to a dashboard
            addWidget: (grid, content, options = {}) => {
                try {
                    if (this.availableLibraries.gridstack) {
                        const widget = grid.addWidget({
                            x: options.x || 0,
                            y: options.y || 0,
                            width: options.width || 4,
                            height: options.height || 4,
                            content: content
                        });
                        return widget;
                    }
                    throw new Error('GridStack library not available');
                } catch (error) {
                    console.error('Error adding widget to dashboard:', error);
                    throw error;
                }
            }
        };
    }

    /**
     * Initialize Date utilities
     * @private
     * @returns {Object} Date utilities
     */
    _initDateUtils() {
        return {
            // Format a date
            format: (date, format = 'YYYY-MM-DD') => {
                try {
                    if (this.availableLibraries.dayjs) {
                        return dayjs(date).format(format);
                    }
                    // Fallback to native Date
                    const d = new Date(date);
                    return d.toISOString().split('T')[0];
                } catch (error) {
                    console.error('Error formatting date:', error);
                    throw error;
                }
            },

            // Parse a date string
            parse: (dateString, format) => {
                try {
                    if (this.availableLibraries.dayjs) {
                        return dayjs(dateString, format).toDate();
                    }
                    // Fallback to native Date
                    return new Date(dateString);
                } catch (error) {
                    console.error('Error parsing date:', error);
                    throw error;
                }
            },

            // Add days to a date
            addDays: (date, days) => {
                try {
                    if (this.availableLibraries.dayjs) {
                        return dayjs(date).add(days, 'day').toDate();
                    }
                    // Fallback to native Date
                    const d = new Date(date);
                    d.setDate(d.getDate() + days);
                    return d;
                } catch (error) {
                    console.error('Error adding days to date:', error);
                    throw error;
                }
            }
        };
    }

    /**
     * Initialize Editor utilities
     * @private
     * @returns {Object} Editor utilities
     */
    _initEditorUtils() {
        return {
            // Create a code editor
            createCodeEditor: (container, code = '', language = 'javascript') => {
                try {
                    if (this.availableLibraries.monaco) {
                        const editor = monaco.editor.create(container, {
                            value: code,
                            language: language,
                            theme: 'vs-dark',
                            automaticLayout: true
                        });
                        return editor;
                    }
                    throw new Error('Monaco editor library not available');
                } catch (error) {
                    console.error('Error creating code editor:', error);
                    throw error;
                }
            },

            // Create a rich text editor
            createRichTextEditor: (container, content = '') => {
                try {
                    if (this.availableLibraries.tiptap) {
                        const editor = new tiptap.Editor({
                            element: container,
                            content: content,
                            extensions: [
                                tiptap.StarterKit,
                                tiptap.Underline,
                                tiptap.TextAlign.configure({
                                    types: ['heading', 'paragraph']
                                })
                            ]
                        });
                        return editor;
                    }
                    throw new Error('TipTap editor library not available');
                } catch (error) {
                    console.error('Error creating rich text editor:', error);
                    throw error;
                }
            }
        };
    }

    /**
     * Initialize Export utilities
     * @private
     * @returns {Object} Export utilities
     */
    _initExportUtils() {
        return {
            // Export to PDF
            exportToPdf: async (content, filename = 'export.pdf', options = {}) => {
                try {
                    if (this.availableLibraries.pdfLib && this.availableLibraries.html2canvas) {
                        // Import the enhanced PDF export utility
                        const PdfExportModule = await import('../js/pdf-export.js');
                        const PdfExport = PdfExportModule.default;

                        // Use the enhanced PDF export function
                        return await PdfExport.exportToPdf(content, filename, options);
                    }
                    throw new Error('PDF-Lib or html2canvas library not available');
                } catch (error) {
                    console.error('Error exporting to PDF:', error);
                    throw error;
                }
            },

            // Export to CSV
            exportToCsv: (data, filename = 'export.csv') => {
                try {
                    if (this.availableLibraries.papaparse) {
                        const csv = Papa.unparse(data);
                        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = filename;
                        a.click();
                        URL.revokeObjectURL(url);

                        return true;
                    }
                    throw new Error('PapaParse library not available');
                } catch (error) {
                    console.error('Error exporting to CSV:', error);
                    throw error;
                }
            },

            // Export to ZIP
            exportToZip: async (files, filename = 'export.zip') => {
                try {
                    if (this.availableLibraries.jszip) {
                        // Import the enhanced ZIP export utility
                        const ZipExportModule = await import('../js/zip-export.js');
                        const ZipExport = ZipExportModule.default;

                        // Use the enhanced ZIP export function
                        return await ZipExport.exportToZip(files, filename);
                    }
                    throw new Error('JSZip library not available');
                } catch (error) {
                    console.error('Error exporting to ZIP:', error);
                    throw error;
                }
            }
        };
    }

    /**
     * Initialize OCR utilities
     * @private
     * @returns {Object} OCR utilities
     */
    _initOcrUtils() {
        return {
            // Extract text from an image
            extractText: async (image) => {
                try {
                    if (this.availableLibraries.tesseract) {
                        const result = await Tesseract.recognize(image, 'eng');
                        return result.data.text;
                    }
                    throw new Error('Tesseract library not available');
                } catch (error) {
                    console.error('Error extracting text from image:', error);
                    throw error;
                }
            }
        };
    }

    /**
     * Initialize UI utilities
     * @private
     * @returns {Object} UI utilities
     */
    _initUiUtils() {
        return {
            // Create a sortable list
            createSortableList: (container, options = {}) => {
                try {
                    if (this.availableLibraries.sortable) {
                        const sortable = new Sortable(container, {
                            animation: 150,
                            ghostClass: 'sortable-ghost',
                            ...options
                        });
                        return sortable;
                    }
                    throw new Error('Sortable library not available');
                } catch (error) {
                    console.error('Error creating sortable list:', error);
                    throw error;
                }
            },

            // Create an intro tour
            createIntroTour: (steps) => {
                try {
                    if (this.availableLibraries.intro) {
                        const intro = introJs();
                        intro.setOptions({
                            steps: steps,
                            showProgress: true,
                            showBullets: true,
                            showStepNumbers: false,
                            keyboardNavigation: true,
                            exitOnOverlayClick: false,
                            exitOnEsc: true,
                            nextLabel: 'Next',
                            prevLabel: 'Back',
                            doneLabel: 'Finish'
                        });
                        return intro;
                    }
                    throw new Error('IntroJS library not available');
                } catch (error) {
                    console.error('Error creating intro tour:', error);
                    throw error;
                }
            },

            // Add animation to an element
            animate: (element, animation, options = {}) => {
                try {
                    if (this.availableLibraries.motion) {
                        Motion.animate(element, animation, options);
                        return true;
                    }
                    throw new Error('Motion library not available');
                } catch (error) {
                    console.error('Error animating element:', error);
                    throw error;
                }
            }
        };
    }
}

// Create and export a singleton instance
const utils = new Utils();
export default utils;
