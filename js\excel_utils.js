// js/excel_utils.js
// Utility functions for Excel operations using multiple libraries

/**
 * Excel Utilities class that provides a unified interface to work with
 * multiple Excel libraries (xlsx-populate, xlsx, exceljs, luckysheet, handsontable)
 */
class ExcelUtils {
    constructor() {
        this.activeLibrary = null; // No default library until loaded
        this.supportedLibraries = [
            'xlsx-populate',
            'xlsx',
            'exceljs',
            'luckysheet',
            'handsontable'
        ];

        // Track loaded libraries
        this.loadedLibraries = {};

        // Map library names to their module paths
        this.libraryPaths = {
            'xlsx-populate': '../lib/xlsx-populate.min.js',
            'xlsx': '../lib/xlsx.full.min.js',
            'exceljs': '../lib/exceljs.min.js',
            'luckysheet': '../lib/luckysheet.esm.min.js',
            'handsontable': '../lib/jquery.handsontable.full.min.js'
        };

        // Initialize additional utilities
        this.initializeUtilities();
    }

    /**
     * Dynamically load a library
     * @param {string} libraryName - Name of the library to load
     * @returns {Promise<boolean>} - Promise resolving to true if library loaded successfully
     */
    async loadLibrary(libraryName) {
        if (!this.supportedLibraries.includes(libraryName)) {
            console.error(`Library ${libraryName} is not supported`);
            return false;
        }

        // If already loaded, just set as active and return
        if (this.loadedLibraries[libraryName]) {
            this.activeLibrary = libraryName;
            console.log(`Using already loaded library: ${libraryName}`);
            return true;
        }

        try {
            // Load dependencies first if needed
            if (libraryName === 'handsontable' && !this.loadedLibraries['jquery']) {
                await this._loadScript('../lib/jquery-3.6.0.min.js');
                this.loadedLibraries['jquery'] = true;
            }

            // Load the library
            const path = this.libraryPaths[libraryName];
            if (!path) {
                throw new Error(`Path not defined for library: ${libraryName}`);
            }

            await this._loadScript(path);

            // Verify the library is available
            let isAvailable = false;
            switch (libraryName) {
                case 'xlsx-populate':
                    isAvailable = typeof XlsxPopulate !== 'undefined';
                    break;
                case 'xlsx':
                    isAvailable = typeof XLSX !== 'undefined';
                    break;
                case 'exceljs':
                    isAvailable = typeof ExcelJS !== 'undefined';
                    break;
                case 'luckysheet':
                    isAvailable = typeof luckysheet !== 'undefined';
                    break;
                case 'handsontable':
                    isAvailable = typeof Handsontable !== 'undefined';
                    break;
            }

            if (isAvailable) {
                this.loadedLibraries[libraryName] = true;
                this.activeLibrary = libraryName;
                console.log(`Successfully loaded library: ${libraryName}`);
                return true;
            } else {
                throw new Error(`Failed to load library: ${libraryName}`);
            }
        } catch (error) {
            console.error(`Error loading library ${libraryName}:`, error);
            return false;
        }
    }

    /**
     * Helper method to load a script dynamically
     * @private
     * @param {string} src - Script source URL
     * @returns {Promise<void>} - Promise that resolves when script is loaded
     */
    _loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.async = true;

            script.onload = () => resolve();
            script.onerror = (error) => reject(new Error(`Failed to load script: ${src}`));

            document.head.appendChild(script);
        });
    }

    /**
     * Initialize additional utilities based on available libraries
     */
    initializeUtilities() {
        // Initialize date utilities if dayjs is available
        if (typeof dayjs !== 'undefined') {
            this.dateUtils = {
                format: (date, format) => dayjs(date).format(format),
                parse: (str, format) => dayjs(str, format),
                addDays: (date, days) => dayjs(date).add(days, 'day').toDate(),
                addMonths: (date, months) => dayjs(date).add(months, 'month').toDate(),
                addYears: (date, years) => dayjs(date).add(years, 'year').toDate()
            };
        }

        // Initialize PDF utilities if pdf-lib is available
        if (typeof PDFLib !== 'undefined') {
            this.pdfUtils = {
                createPDF: async () => {
                    const pdfDoc = await PDFLib.PDFDocument.create();
                    return pdfDoc;
                },
                addPage: (pdfDoc) => {
                    return pdfDoc.addPage();
                },
                savePDF: async (pdfDoc) => {
                    const pdfBytes = await pdfDoc.save();
                    return pdfBytes;
                }
            };
        }

        // Initialize chart utilities if chart libraries are available
        if (typeof ApexCharts !== 'undefined' || typeof echarts !== 'undefined') {
            this.chartUtils = {
                createChart: (container, type, data, options) => {
                    if (typeof ApexCharts !== 'undefined') {
                        const chart = new ApexCharts(container, {
                            chart: { type },
                            series: data,
                            ...options
                        });
                        chart.render();
                        return chart;
                    } else if (typeof echarts !== 'undefined') {
                        const chart = echarts.init(container);
                        chart.setOption({
                            series: [{ type, data }],
                            ...options
                        });
                        return chart;
                    }
                    return null;
                }
            };
        }
    }

    /**
     * Set the active library to use
     * @param {string} libraryName - Name of the library to use
     * @returns {Promise<boolean>} - Promise resolving to true if library was set successfully
     */
    async setActiveLibrary(libraryName) {
        if (!this.supportedLibraries.includes(libraryName)) {
            console.error(`Library ${libraryName} is not supported`);
            return false;
        }

        // If the library is already loaded, just set it as active
        if (this.loadedLibraries[libraryName]) {
            this.activeLibrary = libraryName;
            console.log(`Active Excel library set to: ${libraryName}`);
            return true;
        }

        // Otherwise, try to load it
        const loaded = await this.loadLibrary(libraryName);
        if (loaded) {
            console.log(`Active Excel library set to: ${libraryName}`);
            return true;
        } else {
            console.error(`Failed to load library: ${libraryName}`);
            return false;
        }
    }

    /**
     * Create a new blank workbook
     * @returns {Promise<Object>} - Promise resolving to the workbook object
     */
    async createBlankWorkbook() {
        try {
            switch (this.activeLibrary) {
                case 'xlsx-populate':
                    return await XlsxPopulate.fromBlankAsync();

                case 'xlsx':
                    return XLSX.utils.book_new();

                case 'exceljs':
                    return new ExcelJS.Workbook();

                default:
                    throw new Error(`Library ${this.activeLibrary} not supported for creating blank workbook`);
            }
        } catch (error) {
            console.error('Error creating blank workbook:', error);
            throw error;
        }
    }

    /**
     * Load a workbook from a file
     * @param {File} file - The file object to load
     * @returns {Promise<Object>} - Promise resolving to the workbook object
     */
    async loadWorkbook(file) {
        try {
            const data = await file.arrayBuffer();

            switch (this.activeLibrary) {
                case 'xlsx-populate':
                    return await XlsxPopulate.fromDataAsync(data);

                case 'xlsx':
                    const workbook = XLSX.read(data, { type: 'array' });
                    return workbook;

                case 'exceljs':
                    const excelWorkbook = new ExcelJS.Workbook();
                    await excelWorkbook.xlsx.load(data);
                    return excelWorkbook;

                case 'luckysheet':
                    // Luckysheet requires a DOM element to render the spreadsheet
                    // We'll convert the Excel file to a format Luckysheet can understand
                    const xlsxWorkbook = XLSX.read(data, { type: 'array' });
                    const luckysheetData = this._convertXlsxToLuckysheet(xlsxWorkbook);
                    return luckysheetData;

                case 'handsontable':
                    // Handsontable works with array data
                    const hotWorkbook = XLSX.read(data, { type: 'array' });
                    const firstSheetName = hotWorkbook.SheetNames[0];
                    const hotData = XLSX.utils.sheet_to_json(hotWorkbook.Sheets[firstSheetName], { header: 1 });
                    return { data: hotData, workbook: hotWorkbook };

                default:
                    throw new Error(`Library ${this.activeLibrary} not supported for loading workbook`);
            }
        } catch (error) {
            console.error('Error loading workbook:', error);
            throw error;
        }
    }

    /**
     * Save a workbook to a file
     * @param {Object} workbook - The workbook object to save
     * @param {string} filename - The filename to save as
     * @returns {Promise<boolean>} - Promise resolving to true when the file is saved
     */
    async saveWorkbook(workbook, filename = 'exported_workbook.xlsx') {
        try {
            let blob;

            switch (this.activeLibrary) {
                case 'xlsx-populate':
                    blob = await workbook.outputAsync();
                    break;

                case 'xlsx':
                    const wbout = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
                    blob = new Blob([wbout], { type: 'application/octet-stream' });
                    break;

                case 'exceljs':
                    const buffer = await workbook.xlsx.writeBuffer();
                    blob = new Blob([buffer], { type: 'application/octet-stream' });
                    break;

                case 'luckysheet':
                    // Convert Luckysheet data to XLSX format
                    const luckysheetWorkbook = this._convertLuckysheetToXlsx(workbook);
                    const luckysheetOut = XLSX.write(luckysheetWorkbook, { bookType: 'xlsx', type: 'array' });
                    blob = new Blob([luckysheetOut], { type: 'application/octet-stream' });
                    break;

                case 'handsontable':
                    // Convert Handsontable data to XLSX format
                    const hotWorkbook = workbook.workbook || XLSX.utils.book_new();
                    const hotSheet = XLSX.utils.aoa_to_sheet(workbook.data);
                    XLSX.utils.book_append_sheet(hotWorkbook, hotSheet, 'Sheet1');
                    const hotOut = XLSX.write(hotWorkbook, { bookType: 'xlsx', type: 'array' });
                    blob = new Blob([hotOut], { type: 'application/octet-stream' });
                    break;

                default:
                    throw new Error(`Library ${this.activeLibrary} not supported for saving workbook`);
            }

            // Create a download link and trigger the download
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.click();
            window.URL.revokeObjectURL(url);

            return true;
        } catch (error) {
            console.error('Error saving workbook:', error);
            throw error;
        }
    }

    /**
     * Convert XLSX workbook to Luckysheet format
     * @private
     * @param {Object} workbook - XLSX workbook
     * @returns {Array} - Luckysheet data
     */
    _convertXlsxToLuckysheet(workbook) {
        const result = [];

        workbook.SheetNames.forEach(sheetName => {
            const worksheet = workbook.Sheets[sheetName];
            const luckysheetData = {
                name: sheetName,
                index: workbook.SheetNames.indexOf(sheetName),
                status: 1,
                order: workbook.SheetNames.indexOf(sheetName),
                celldata: [],
                config: {},
                luckysheet_select_save: [],
                calcChain: [],
                scrollLeft: 0,
                scrollTop: 0,
                zoomRatio: 1,
                showGridLines: 1,
                defaultColWidth: 72,
                defaultRowHeight: 20
            };

            // Convert cells
            const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1');
            for (let r = range.s.r; r <= range.e.r; ++r) {
                for (let c = range.s.c; c <= range.e.c; ++c) {
                    const cellAddress = XLSX.utils.encode_cell({ r, c });
                    const cell = worksheet[cellAddress];

                    if (cell) {
                        const luckyCell = {
                            r,
                            c,
                            v: {
                                v: cell.v,
                                ct: { fa: 'General', t: 'g' },
                                m: cell.w || cell.v
                            }
                        };

                        luckysheetData.celldata.push(luckyCell);
                    }
                }
            }

            result.push(luckysheetData);
        });

        return result;
    }

    /**
     * Convert Luckysheet data to XLSX workbook
     * @private
     * @param {Array} luckysheetData - Luckysheet data
     * @returns {Object} - XLSX workbook
     */
    _convertLuckysheetToXlsx(luckysheetData) {
        const workbook = XLSX.utils.book_new();

        luckysheetData.forEach(sheet => {
            const worksheet = XLSX.utils.aoa_to_sheet([]);
            const range = { s: { r: 0, c: 0 }, e: { r: 0, c: 0 } };

            // Process cell data
            sheet.celldata.forEach(cell => {
                const cellAddress = XLSX.utils.encode_cell({ r: cell.r, c: cell.c });
                const xlsxCell = {
                    v: cell.v.v,
                    t: cell.v.ct.t === 'n' ? 'n' : 's',
                    w: cell.v.m
                };

                worksheet[cellAddress] = xlsxCell;

                // Update range
                if (cell.r > range.e.r) range.e.r = cell.r;
                if (cell.c > range.e.c) range.e.c = cell.c;
            });

            // Set range
            worksheet['!ref'] = XLSX.utils.encode_range(range);

            // Add sheet to workbook
            XLSX.utils.book_append_sheet(workbook, worksheet, sheet.name);
        });

        return workbook;
    }

    /**
     * Get the first sheet from a workbook
     * @param {Object} workbook - The workbook object
     * @returns {Object} - The sheet object
     */
    getFirstSheet(workbook) {
        try {
            switch (this.activeLibrary) {
                case 'xlsx-populate':
                    return workbook.sheet(0);

                case 'xlsx':
                    const firstSheetName = workbook.SheetNames[0];
                    return workbook.Sheets[firstSheetName];

                case 'exceljs':
                    return workbook.getWorksheet(1);

                case 'luckysheet':
                    // Luckysheet data is an array of sheets, return the first one
                    return workbook[0];

                case 'handsontable':
                    // Handsontable workbook is an object with data property
                    return workbook;

                default:
                    throw new Error(`Library ${this.activeLibrary} not supported for getting first sheet`);
            }
        } catch (error) {
            console.error('Error getting first sheet:', error);
            throw error;
        }
    }

    /**
     * Create a chart from Excel data
     * @param {string} type - Chart type (line, bar, pie, etc.)
     * @param {HTMLElement} container - Container element for the chart
     * @param {Object} sheet - Sheet object containing the data
     * @param {Object} options - Chart options
     * @returns {Object} - Chart object
     */
    createChart(type, container, sheet, options = {}) {
        try {
            // Extract data from the sheet based on the active library
            let data;

            switch (this.activeLibrary) {
                case 'xlsx-populate':
                    data = this._extractDataFromXlsxPopulate(sheet, options.dataRange);
                    break;

                case 'xlsx':
                    data = this._extractDataFromXlsx(sheet, options.dataRange);
                    break;

                case 'exceljs':
                    data = this._extractDataFromExcelJS(sheet, options.dataRange);
                    break;

                case 'luckysheet':
                    data = this._extractDataFromLuckysheet(sheet, options.dataRange);
                    break;

                case 'handsontable':
                    data = sheet.data;
                    break;

                default:
                    throw new Error(`Library ${this.activeLibrary} not supported for creating charts`);
            }

            // Create chart using available chart libraries
            if (typeof ApexCharts !== 'undefined') {
                const chartOptions = {
                    chart: {
                        type,
                        height: options.height || 350,
                        width: options.width || '100%',
                        toolbar: {
                            show: true
                        }
                    },
                    series: [{
                        name: options.seriesName || 'Series 1',
                        data: data.values
                    }],
                    xaxis: {
                        categories: data.categories
                    },
                    title: {
                        text: options.title || 'Chart',
                        align: 'center'
                    }
                };

                const chart = new ApexCharts(container, chartOptions);
                chart.render();
                return chart;
            } else if (typeof echarts !== 'undefined') {
                const chart = echarts.init(container);
                const chartOptions = {
                    title: {
                        text: options.title || 'Chart'
                    },
                    tooltip: {},
                    xAxis: {
                        data: data.categories
                    },
                    yAxis: {},
                    series: [{
                        name: options.seriesName || 'Series 1',
                        type,
                        data: data.values
                    }]
                };

                chart.setOption(chartOptions);
                return chart;
            }

            throw new Error('No chart library available');
        } catch (error) {
            console.error('Error creating chart:', error);
            throw error;
        }
    }

    /**
     * Extract data from XlsxPopulate sheet
     * @private
     * @param {Object} sheet - XlsxPopulate sheet
     * @param {string} range - Cell range (e.g., 'A1:B10')
     * @returns {Object} - Data object with categories and values
     */
    _extractDataFromXlsxPopulate(sheet, range) {
        const result = { categories: [], values: [] };

        if (!range) {
            // If no range is specified, try to use the used range
            const usedRange = sheet.usedRange();
            if (usedRange) {
                const startCell = usedRange.startCell();
                const endCell = usedRange.endCell();
                const startRow = startCell.rowNumber();
                const endRow = endCell.rowNumber();
                const startCol = startCell.columnNumber();
                const endCol = endCell.columnNumber();

                // Assume first row contains categories
                for (let col = startCol; col <= endCol; col++) {
                    result.categories.push(sheet.cell(startRow, col).value() || '');
                }

                // Assume second row contains values
                for (let col = startCol; col <= endCol; col++) {
                    const value = sheet.cell(startRow + 1, col).value();
                    result.values.push(typeof value === 'number' ? value : 0);
                }
            }
        } else {
            // Parse the range
            const [start, end] = range.split(':');
            const startCell = sheet.cell(start);
            const endCell = sheet.cell(end);
            const startRow = startCell.rowNumber();
            const endRow = endCell.rowNumber();
            const startCol = startCell.columnNumber();
            const endCol = endCell.columnNumber();

            // Assume first row contains categories
            for (let col = startCol; col <= endCol; col++) {
                result.categories.push(sheet.cell(startRow, col).value() || '');
            }

            // Assume second row contains values
            for (let col = startCol; col <= endCol; col++) {
                const value = sheet.cell(startRow + 1, col).value();
                result.values.push(typeof value === 'number' ? value : 0);
            }
        }

        return result;
    }

    /**
     * Extract data from XLSX sheet
     * @private
     * @param {Object} sheet - XLSX sheet
     * @param {string} range - Cell range (e.g., 'A1:B10')
     * @returns {Object} - Data object with categories and values
     */
    _extractDataFromXlsx(sheet, range) {
        const result = { categories: [], values: [] };

        // Convert sheet to array of arrays
        const data = XLSX.utils.sheet_to_json(sheet, { header: 1 });

        if (data.length >= 2) {
            // Assume first row contains categories
            result.categories = data[0].map(cell => cell || '');

            // Assume second row contains values
            result.values = data[1].map(cell => typeof cell === 'number' ? cell : 0);
        }

        return result;
    }

    /**
     * Extract data from ExcelJS sheet
     * @private
     * @param {Object} sheet - ExcelJS sheet
     * @param {string} range - Cell range (e.g., 'A1:B10')
     * @returns {Object} - Data object with categories and values
     */
    _extractDataFromExcelJS(sheet, range) {
        const result = { categories: [], values: [] };

        if (!range) {
            // If no range is specified, try to use all rows
            if (sheet.rowCount >= 2) {
                // Assume first row contains categories
                sheet.getRow(1).eachCell((cell, colNumber) => {
                    result.categories.push(cell.value || '');
                });

                // Assume second row contains values
                sheet.getRow(2).eachCell((cell, colNumber) => {
                    const value = cell.value;
                    result.values.push(typeof value === 'number' ? value : 0);
                });
            }
        } else {
            // Parse the range
            const rangeObj = sheet.getRange(range);
            const values = rangeObj.values;

            if (values.length >= 2) {
                // Assume first row contains categories
                result.categories = values[0].map(cell => cell || '');

                // Assume second row contains values
                result.values = values[1].map(cell => typeof cell === 'number' ? cell : 0);
            }
        }

        return result;
    }

    /**
     * Extract data from Luckysheet sheet
     * @private
     * @param {Object} sheet - Luckysheet sheet
     * @param {string} range - Cell range (e.g., 'A1:B10')
     * @returns {Object} - Data object with categories and values
     */
    _extractDataFromLuckysheet(sheet, range) {
        const result = { categories: [], values: [] };

        // Convert celldata to a 2D array
        const data = [];

        // Find the maximum row and column
        let maxRow = 0;
        let maxCol = 0;

        sheet.celldata.forEach(cell => {
            if (cell.r > maxRow) maxRow = cell.r;
            if (cell.c > maxCol) maxCol = cell.c;
        });

        // Initialize the 2D array
        for (let r = 0; r <= maxRow; r++) {
            data[r] = [];
            for (let c = 0; c <= maxCol; c++) {
                data[r][c] = null;
            }
        }

        // Fill the 2D array with cell values
        sheet.celldata.forEach(cell => {
            data[cell.r][cell.c] = cell.v.v;
        });

        if (data.length >= 2) {
            // Assume first row contains categories
            result.categories = data[0].map(cell => cell || '');

            // Assume second row contains values
            result.values = data[1].map(cell => typeof cell === 'number' ? cell : 0);
        }

        return result;
    }
}

// Create and export a singleton instance
const excelUtils = new ExcelUtils();
export default excelUtils;
