/* css/menu.css */

/* Menu Bar Styles */
.menu-bar {
    display: flex;
    gap: 2px;
    padding: 4px 8px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dadce0;
    font-size: 13px;
    user-select: none;
    height: auto;
    flex-wrap: wrap;
}

.menu-item {
    position: relative;
    padding: 6px 12px;
    cursor: pointer;
    color: #5f6368;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.15s;
}

.menu-item:hover {
    background-color: #e8eaed;
}

.menu-item span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.submenu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    background-color: #ffffff;
    border: 1px solid #dadce0;
    border-radius: 4px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    z-index: 1000;
    margin-top: 4px;
    padding: 4px 0;
}

.menu-item:hover .submenu {
    display: block;
}

.submenu-item {
    padding: 8px 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #5f6368;
    transition: all 0.15s;
    cursor: pointer;
    white-space: nowrap;
}

.submenu-item .material-icons {
    font-size: 18px;
    color: inherit;
}

.submenu-item:hover {
    background-color: #e8f0fe;
    color: #1a73e8;
}

/* Toolbar Styles */
#toolbar {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 0.5rem;
    gap: 0.25rem;
}

.toolbar-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border: none;
    background: none;
    border-radius: 0.25rem;
    cursor: pointer;
    color: #495057;
}

.toolbar-button:hover {
    background-color: #e9ecef;
}

.toolbar-button:active {
    background-color: #dee2e6;
}

.toolbar-separator {
    width: 1px;
    height: 24px;
    background-color: #dee2e6;
    margin: 0 8px;
}

.toolbar-select {
    height: 2rem;
    padding: 0 0.5rem;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    background-color: #ffffff;
    font-size: 0.875rem;
    color: #495057;
    cursor: pointer;
}

.toolbar-select:hover {
    border-color: #adb5bd;
}

.toolbar-select:focus {
    outline: none;
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Context Menu Styles */
.context-menu {
    position: fixed;
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    min-width: 10rem;
    z-index: 1000;
    font-size: 0.875rem;
}

.context-menu-item {
    padding: 0.5rem 1rem;
    cursor: pointer;
    user-select: none;
}

.context-menu-item:hover {
    background-color: #e9ecef;
}

.context-menu-separator {
    height: 1px;
    background-color: #dee2e6;
    margin: 0.25rem 0;
}

/* Modal and Panel Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: none;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.modal {
    background-color: #ffffff;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
    pointer-events: auto;
}

.modal-header {
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-body {
    padding: 1rem;
}

.modal-footer {
    padding: 1rem;
    border-top: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 0.5rem;
}

.panel {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    width: 20rem;
    background-color: #ffffff;
    border-left: 1px solid #dee2e6;
    box-shadow: -0.5rem 0 1rem rgba(0, 0, 0, 0.15);
    z-index: 1000;
    display: flex;
    flex-direction: column;
}

.panel-header {
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.panel-body {
    flex: 1;
    padding: 1rem;
    overflow: auto;
}

.panel-footer {
    padding: 1rem;
    border-top: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 0.5rem;
}