/**
 * Direct Welcome Modal Handler
 * This script handles the welcome modal functionality directly without using modules
 */

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Welcome-direct.js loaded');
    // Only initialize if the module version is not active
    if (!window.welcomeModal && !window.WelcomeModal) {
        console.log('Module welcome modal not detected, initializing direct version');
        // Add a small delay to ensure DOM is fully ready
        setTimeout(function() {
            initWelcomeModal();
        }, 300);
    } else {
        console.log('Module welcome modal detected, skipping direct initialization');
    }
});

// Initialize the welcome modal
function initWelcomeModal() {
    console.log('Initializing welcome modal directly');

    // Get modal elements
    const modal = document.getElementById('welcomeModal');
    const newBtn = document.getElementById('welcomeNewBtn');
    const openBtn = document.getElementById('welcomeOpenBtn');
    const closeBtn = document.getElementById('welcomeCloseBtn');

    if (!modal) {
        console.error('Welcome modal not found in the DOM');
        return;
    }

    console.log('Welcome modal found:', modal);

    // Add event listener to new workbook button
    if (newBtn) {
        console.log('New button found, adding event listener');
        newBtn.addEventListener('click', function() {
            console.log('New workbook button clicked');
            hideModal();
            createNewWorkbook();
        });
    } else {
        console.error('New workbook button not found in the DOM');
    }

    // Add event listener to open file button
    if (openBtn) {
        console.log('Open button found, adding event listener');
        openBtn.addEventListener('click', function() {
            console.log('Open file button clicked');
            hideModal();
            openExistingFile();
        });
    } else {
        console.error('Open file button not found in the DOM');
    }

    // Add event listener to close button
    if (closeBtn) {
        console.log('Close button found, adding event listener');
        closeBtn.addEventListener('click', function() {
            console.log('Close button clicked');
            hideModal();
        });
    } else {
        console.error('Close button not found in the DOM');
    }

    // Add event listeners to recent file items
    const recentItems = document.querySelectorAll('.recent-file-item');
    recentItems.forEach(function(item) {
        item.addEventListener('click', function() {
            const fileName = item.querySelector('.recent-file-name').textContent;
            console.log('Recent file clicked:', fileName);
            hideModal();
            createNewWorkbook(); // For now, just create a new workbook
        });
    });

    // Function to hide the modal
    function hideModal() {
        console.log('Hiding welcome modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    // Function to create a new workbook
    function createNewWorkbook() {
        console.log('Creating new workbook from welcome modal');

        // Try to click the new workbook button
        const newWorkbookBtn = document.getElementById('newWorkbookBtn');
        if (newWorkbookBtn) {
            console.log('Clicking new workbook button');
            newWorkbookBtn.click();
            return;
        }

        // Try to use the main_script's createNewWorkbook function
        if (window.mainModule && typeof window.mainModule.createNewWorkbook === 'function') {
            console.log('Using mainModule.createNewWorkbook()');
            window.mainModule.createNewWorkbook();
            return;
        }

        // If button not found, create a simple spreadsheet
        console.log('New workbook button not found, creating simple spreadsheet');
        const spreadsheetContainer = document.getElementById('spreadsheetContainer');
        if (spreadsheetContainer) {
            // Clear the container
            spreadsheetContainer.innerHTML = '';

            // Create a simple table
            const table = document.createElement('table');
            table.id = 'excelTable';
            table.className = 'excel-table';

            // Create header row
            const thead = document.createElement('thead');
            const headerRow = document.createElement('tr');

            // Add corner cell
            const cornerCell = document.createElement('th');
            cornerCell.className = 'corner-cell';
            headerRow.appendChild(cornerCell);

            // Add column headers (A-J)
            for (let i = 0; i < 10; i++) {
                const th = document.createElement('th');
                th.className = 'column-header';
                th.textContent = String.fromCharCode(65 + i); // A, B, C, ...
                headerRow.appendChild(th);
            }

            thead.appendChild(headerRow);
            table.appendChild(thead);

            // Create table body
            const tbody = document.createElement('tbody');

            // Add rows
            for (let row = 1; row <= 20; row++) {
                const tr = document.createElement('tr');

                // Add row header
                const rowHeader = document.createElement('th');
                rowHeader.className = 'row-header';
                rowHeader.textContent = row;
                tr.appendChild(rowHeader);

                // Add cells
                for (let col = 0; col < 10; col++) {
                    const td = document.createElement('td');
                    td.dataset.row = row;
                    td.dataset.col = col + 1;

                    // Add input for cell editing
                    const input = document.createElement('input');
                    input.type = 'text';
                    input.className = 'cell-input';
                    input.readOnly = true;
                    input.dataset.row = row;
                    input.dataset.col = col + 1;

                    td.appendChild(input);
                    tr.appendChild(td);
                }

                tbody.appendChild(tr);
            }

            table.appendChild(tbody);
            spreadsheetContainer.appendChild(table);

            // Show toolbar
            const toolbar = document.getElementById('toolbar');
            if (toolbar) toolbar.style.display = 'flex';
        }
    }

    // Function to open an existing file
    function openExistingFile() {
        console.log('Opening file from welcome modal');

        // Check if there's an existing file input with id 'fileInput'
        let fileInput = document.getElementById('fileInput');

        // If the main file input exists, use it
        if (fileInput) {
            console.log('Using existing file input element');
            fileInput.click();
            return;
        }

        // Try to click the open file button
        const openFileBtn = document.getElementById('openFileBtn');
        if (openFileBtn) {
            console.log('Clicking open file button');
            // Check the global file dialog flag
            if (window._fileDialogOpen) {
                console.log('File dialog already open, ignoring click');
                return;
            }

            window._fileDialogOpen = true;
            openFileBtn.click();

            return;
        }

        // Try to use the main_script's openFile function
        if (window.mainModule && typeof window.mainModule.openFile === 'function') {
            console.log('Using mainModule.openFile()');

                // Check the global file dialog flag
            if (window._fileDialogOpen) {
                console.log('File dialog already open, ignoring request');
                return;
            }

            window._fileDialogOpen = true;

            // Create a temporary file input to trigger the file selection
            const tempFileInput = document.createElement('input');
            tempFileInput.type = 'file';
            tempFileInput.accept = '.xlsx,.xls';
            tempFileInput.style.display = 'none';
            document.body.appendChild(tempFileInput);

            tempFileInput.addEventListener('change', (event) => {
                window._fileDialogOpen = false;
                window.mainModule.openFile(event);
                document.body.removeChild(tempFileInput);
            });

            // Add cancel event listener to reset the flag when the dialog is canceled
            tempFileInput.addEventListener('cancel', () => {
                window._fileDialogOpen = false;
                document.body.removeChild(tempFileInput);
            });

            tempFileInput.click();

            // Reset the flag after a timeout in case the user cancels the dialog
            setTimeout(() => {
                window._fileDialogOpen = false;
            }, 10000);
            return;
        }

        // If button not found, create a file input directly
        console.log('Open file button not found, creating file input');

        // Check the global file dialog flag
        if (window._fileDialogOpen) {
            console.log('File dialog already open, ignoring request');
            return;
        }

        window._fileDialogOpen = true;

        fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = '.xlsx,.xls';
        fileInput.style.display = 'none';
        fileInput.id = 'temp-file-input-' + Date.now(); // Use unique ID
        document.body.appendChild(fileInput);

        // Set up the change event handler
        fileInput.addEventListener('change', function(event) {
            // Reset the file dialog flag
            window._fileDialogOpen = false;

            const file = event.target.files[0];
            if (!file) {
                console.log('No file selected');
                document.body.removeChild(fileInput);
                return;
            }

            console.log('File selected:', file.name);

            // Try to use the main module first
            if (window.mainModule && typeof window.mainModule.openFile === 'function') {
                console.log('Using mainModule.openFile()');
                window.mainModule.openFile(event);
                document.body.removeChild(fileInput);
                return;
            }

            // Show file info
            const spreadsheetContainer = document.getElementById('spreadsheetContainer');
            if (spreadsheetContainer) {
                spreadsheetContainer.innerHTML = `
                    <div style="padding: 20px; text-align: center;">
                        <h2>File Selected</h2>
                        <p>File: ${file.name}</p>
                        <p>Size: ${(file.size / 1024).toFixed(2)} KB</p>
                        <p>Type: ${file.type || 'Unknown'}</p>
                        <p>Last Modified: ${new Date(file.lastModified).toLocaleString()}</p>
                    </div>
                `;
            }

            document.body.removeChild(fileInput);
        });

        // Add cancel event listener to reset the flag when the dialog is canceled
        fileInput.addEventListener('cancel', () => {
            window._fileDialogOpen = false;
            document.body.removeChild(fileInput);
        });

        // Trigger the file dialog
        fileInput.click();

        // Reset the flag
        setTimeout(() => {
            window._fileDialogOpen = false;
        }, 1000);
    }
}
