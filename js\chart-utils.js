/**
 * Chart Utility Functions
 * Helper functions for chart creation and data extraction
 */

/**
 * Extract data from a cell range
 * @param {Object} range - The cell range object with start and end properties
 * @returns {Object} The extracted data with categories and values
 */
export function extractDataFromRange(range) {
    if (!range) {
        console.error('Invalid range provided to extractDataFromRange');
        return { categories: [], values: [], series: [] };
    }

    // Get the current sheet - try multiple ways to ensure we get it
    const sheet = window.currentSheet || (window.workbook ? window.workbook.activeSheet() : null);

    if (!sheet) {
        console.error('No active sheet available for data extraction');
        return { categories: [], values: [], series: [] };
    }

    try {
        // Determine the range boundaries
        const startRow = Math.min(range.start.r, range.end.r);
        const endRow = Math.max(range.start.r, range.end.r);
        const startCol = Math.min(range.start.c, range.end.c);
        const endCol = Math.max(range.start.c, range.end.c);

        console.log(`Extracting data from range: R${startRow}C${startCol}:R${endRow}C${endCol}`);

        // Validate the range is within sheet bounds
        try {
            // Check if the range is valid by trying to access the corner cells
            const cornerCells = [
                sheet.cell(startRow, startCol),
                sheet.cell(startRow, endCol),
                sheet.cell(endRow, startCol),
                sheet.cell(endRow, endCol)
            ];

            // If any of these cells don't exist, it will throw an error
            if (!cornerCells.every(cell => cell !== null && cell !== undefined)) {
                throw new Error('Invalid cell range');
            }
        } catch (e) {
            console.error('Range validation error:', e);
            return {
                categories: ['Invalid Range'],
                values: [0],
                series: [{ name: 'Error', data: [0] }]
            };
        }

        // Check if we have enough data to create a chart
        if (startRow === endRow && startCol === endCol) {
            console.warn('Single cell selected, not enough data for a chart');
            return {
                categories: ['Not enough data'],
                values: [0],
                series: [{ name: 'Error', data: [0] }]
            };
        }

        // Determine if we're dealing with a row-oriented or column-oriented dataset
        const rowCount = endRow - startRow + 1;
        const colCount = endCol - startCol + 1;

        // For very small ranges, we need special handling
        if (rowCount <= 2 && colCount <= 2) {
            return handleSmallRange(sheet, startRow, endRow, startCol, endCol);
        }

        // For larger ranges, determine orientation
        const isRowOriented = rowCount > colCount;

        // Read all cell values in the range first to avoid repeated cell access
        const cellValues = [];
        for (let r = startRow; r <= endRow; r++) {
            const rowValues = [];
            for (let c = startCol; c <= endCol; c++) {
                try {
                    const cell = sheet.cell(r, c);
                    const value = cell ? cell.value() : null;
                    rowValues.push(value);
                } catch (e) {
                    console.warn(`Error reading cell at R${r}C${c}:`, e);
                    rowValues.push(null);
                }
            }
            cellValues.push(rowValues);
        }

        // Analyze the data to determine if we have headers
        let hasHeaders = detectHeaders(cellValues, isRowOriented);

        let categories = [];
        let values = [];
        let seriesData = [];

        if (isRowOriented) {
            // Row-oriented data processing
            if (hasHeaders) {
                // First column contains categories
                for (let r = 0; r < cellValues.length; r++) {
                    const category = cellValues[r][0];
                    categories.push(formatCellValue(category, 'category') || `Item ${r + 1}`);
                }

                // Each additional column is a data series
                for (let c = 1; c < cellValues[0].length; c++) {
                    const seriesValues = [];
                    for (let r = 0; r < cellValues.length; r++) {
                        seriesValues.push(formatCellValue(cellValues[r][c], 'value'));
                    }

                    // Try to get series name from row above the range if available
                    let seriesName = `Series ${c}`;
                    if (startRow > 1) {
                        try {
                            const headerCell = sheet.cell(startRow - 1, startCol + c);
                            if (headerCell) {
                                const headerValue = headerCell.value();
                                if (headerValue) seriesName = headerValue.toString();
                            }
                        } catch (e) {
                            console.warn('Error reading series header:', e);
                        }
                    }

                    seriesData.push({
                        name: seriesName,
                        data: seriesValues
                    });

                    // For backward compatibility, use the first series for values
                    if (c === 1) {
                        values = seriesValues;
                    }
                }
            } else {
                // No headers - use row numbers as categories
                for (let r = 0; r < cellValues.length; r++) {
                    categories.push(`Row ${startRow + r}`);

                    if (cellValues[r].length > 0) {
                        values.push(formatCellValue(cellValues[r][0], 'value'));
                    } else {
                        values.push(0);
                    }
                }

                seriesData.push({
                    name: 'Data',
                    data: values
                });
            }
        } else {
            // Column-oriented data processing
            if (hasHeaders) {
                // First row contains categories
                for (let c = 0; c < cellValues[0].length; c++) {
                    const category = cellValues[0][c];
                    categories.push(formatCellValue(category, 'category') || `Item ${c + 1}`);
                }

                // Each additional row is a data series
                for (let r = 1; r < cellValues.length; r++) {
                    const seriesValues = [];
                    for (let c = 0; c < cellValues[0].length; c++) {
                        seriesValues.push(formatCellValue(cellValues[r][c], 'value'));
                    }

                    // Try to get series name from column to the left of the range if available
                    let seriesName = `Series ${r}`;
                    if (startCol > 1) {
                        try {
                            const headerCell = sheet.cell(startRow + r, startCol - 1);
                            if (headerCell) {
                                const headerValue = headerCell.value();
                                if (headerValue) seriesName = headerValue.toString();
                            }
                        } catch (e) {
                            console.warn('Error reading series header:', e);
                        }
                    }

                    seriesData.push({
                        name: seriesName,
                        data: seriesValues
                    });

                    // For backward compatibility, use the first series for values
                    if (r === 1) {
                        values = seriesValues;
                    }
                }
            } else {
                // No headers - use column letters as categories
                for (let c = 0; c < cellValues[0].length; c++) {
                    categories.push(numberToColumnLetter(startCol + c));

                    if (cellValues.length > 1) {
                        values.push(formatCellValue(cellValues[0][c], 'value'));
                    } else {
                        values.push(0);
                    }
                }

                seriesData.push({
                    name: 'Data',
                    data: values
                });
            }
        }

        // Validate the extracted data
        if (categories.length === 0 || values.length === 0 ||
            !categories.some(c => c !== null && c !== undefined) ||
            !values.some(v => v !== null && v !== undefined && v !== 0)) {

            console.warn('No valid data extracted from range');
            return {
                categories: ['No Data'],
                values: [0],
                series: [{ name: 'No Data', data: [0] }],
                isValidData: false
            };
        }

        return {
            categories,
            values,
            series: seriesData,
            isValidData: true,
            dataOrientation: isRowOriented ? 'row' : 'column',
            hasHeaders
        };
    } catch (error) {
        console.error('Error extracting data from range:', error);
        return {
            categories: ['Error'],
            values: [0],
            series: [{
                name: 'Error',
                data: [0]
            }],
            isValidData: false,
            error: error.message
        };
    }
}

/**
 * Handle small data ranges (1-2 rows/columns)
 */
function handleSmallRange(sheet, startRow, endRow, startCol, endCol) {
    const rowCount = endRow - startRow + 1;
    const colCount = endCol - startCol + 1;

    // For a 2x2 range
    if (rowCount === 2 && colCount === 2) {
        // Assume first row/col are headers
        const categories = [];
        const values = [];

        try {
            // Get the values
            const topLeft = sheet.cell(startRow, startCol).value();
            const topRight = sheet.cell(startRow, endCol).value();
            const bottomLeft = sheet.cell(endRow, startCol).value();
            const bottomRight = sheet.cell(endRow, endCol).value();

            // Determine if we have text headers - improved validation
            const isTopLeftText = typeof topLeft === 'string' && topLeft.trim() !== '' && !/^\d+\.?\d*$/.test(topLeft.trim());
            const isTopRightText = typeof topRight === 'string' && topRight.trim() !== '' && !/^\d+\.?\d*$/.test(topRight.trim());
            const isBottomLeftText = typeof bottomLeft === 'string' && bottomLeft.trim() !== '' && !/^\d+\.?\d*$/.test(bottomLeft.trim());

            // If top row has text, use column orientation
            if (isTopLeftText && isTopRightText) {
                categories.push(topLeft || 'Category 1');
                categories.push(topRight || 'Category 2');
                values.push(formatCellValue(bottomLeft, 'value'));
                values.push(formatCellValue(bottomRight, 'value'));
            }
            // If left column has text, use row orientation
            else if (isTopLeftText && isBottomLeftText) {
                categories.push(topLeft || 'Category 1');
                categories.push(bottomLeft || 'Category 2');
                values.push(formatCellValue(topRight, 'value'));
                values.push(formatCellValue(bottomRight, 'value'));
            }
            // Default to column orientation
            else {
                categories.push(topLeft || 'Category 1');
                categories.push(topRight || 'Category 2');
                values.push(formatCellValue(bottomLeft, 'value'));
                values.push(formatCellValue(bottomRight, 'value'));
            }

            return {
                categories,
                values,
                series: [{
                    name: 'Data',
                    data: values
                }],
                isValidData: true
            };
        } catch (e) {
            console.error('Error handling 2x2 range:', e);
        }
    }

    // For a 1x2 or 2x1 range
    if (rowCount === 1 || colCount === 1) {
        const categories = [];
        const values = [];

        try {
            if (rowCount === 1) {
                // Single row - use as categories and generate values
                for (let c = startCol; c <= endCol; c++) {
                    const value = sheet.cell(startRow, c).value();
                    categories.push(formatCellValue(value, 'category') || `Item ${c - startCol + 1}`);
                    values.push(c - startCol + 1); // Use index as value
                }
            } else {
                // Single column - use as categories and generate values
                for (let r = startRow; r <= endRow; r++) {
                    const value = sheet.cell(r, startCol).value();
                    categories.push(formatCellValue(value, 'category') || `Item ${r - startRow + 1}`);
                    values.push(r - startRow + 1); // Use index as value
                }
            }

            return {
                categories,
                values,
                series: [{
                    name: 'Data',
                    data: values
                }],
                isValidData: true,
                isSingleDimension: true
            };
        } catch (e) {
            console.error('Error handling single dimension range:', e);
        }
    }

    // Fallback
    return {
        categories: ['Not enough data'],
        values: [0],
        series: [{
            name: 'Error',
            data: [0]
        }],
        isValidData: false
    };
}

/**
 * Detect if the data has headers
 */
function detectHeaders(cellValues, isRowOriented) {
    if (cellValues.length === 0 || cellValues[0].length === 0) {
        return false;
    }

    if (isRowOriented) {
        // Check if first column has text headers
        let textCount = 0;
        let numCount = 0;

        // Check first column values
        for (let r = 0; r < Math.min(cellValues.length, 4); r++) {
            const value = cellValues[r][0];
            if (typeof value === 'string' && value.trim() !== '' && !/^\d+\.?\d*$/.test(value.trim())) {
                textCount++;
            } else if (typeof value === 'number' || (typeof value === 'string' && /^\d+\.?\d*$/.test(value.trim()))) {
                numCount++;
            }
        }

        return textCount > numCount;
    } else {
        // Check if first row has text headers
        let textCount = 0;
        let numCount = 0;

        // Check first row values
        for (let c = 0; c < Math.min(cellValues[0].length, 4); c++) {
            const value = cellValues[0][c];
            if (typeof value === 'string' && value.trim() !== '' && !/^\d+\.?\d*$/.test(value.trim())) {
                textCount++;
            } else if (typeof value === 'number' || (typeof value === 'string' && /^\d+\.?\d*$/.test(value.trim()))) {
                numCount++;
            }
        }

        return textCount > numCount;
    }
}

/**
 * Format cell value based on type
 */
function formatCellValue(value, type) {
    if (value === null || value === undefined) {
        return type === 'category' ? '' : 0;
    }

    if (type === 'category') {
        // For categories, convert to string
        return value.toString();
    } else {
        // For values, convert to number if possible
        if (typeof value === 'number') {
            return value;
        } else if (typeof value === 'string' && /^\d+\.?\d*$/.test(value.trim())) {
            return parseFloat(value.trim());
        } else if (typeof value === 'boolean') {
            return value ? 1 : 0;
        } else {
            return 0;
        }
    }
}

/**
 * Format a range address in Excel notation (e.g., A1:B10)
 * @param {Object} range - The range object with start and end properties
 * @returns {string} The formatted range address
 */
export function formatRangeAddress(range) {
    if (!range) return '';

    try {
        const startCol = numberToColumnLetter(Math.min(range.start.c, range.end.c));
        const startRow = Math.min(range.start.r, range.end.r);
        const endCol = numberToColumnLetter(Math.max(range.start.c, range.end.c));
        const endRow = Math.max(range.start.r, range.end.r);

        return `${startCol}${startRow}:${endCol}${endRow}`;
    } catch (error) {
        console.error('Error formatting range address:', error);
        return '';
    }
}

/**
 * Convert a column number to Excel column letter (e.g., 1 -> A, 27 -> AA)
 * @param {number} column - The column number (1-based)
 * @returns {string} The column letter
 */
export function numberToColumnLetter(column) {
    let temp, letter = '';

    while (column > 0) {
        temp = (column - 1) % 26;
        letter = String.fromCharCode(temp + 65) + letter;
        column = (column - temp - 1) / 26;
    }

    return letter;
}

/**
 * Convert Excel column letter to column number (e.g., A -> 1, AA -> 27)
 * @param {string} column - The column letter
 * @returns {number} The column number (1-based)
 */
export function columnLetterToNumber(column) {
    let result = 0;

    for (let i = 0; i < column.length; i++) {
        result = result * 26 + (column.charCodeAt(i) - 64);
    }

    return result;
}

/**
 * Create an ApexCharts chart
 * @param {HTMLElement} container - The container element
 * @param {string} type - The chart type
 * @param {Object} data - The chart data
 * @param {Object} options - The chart options
 * @returns {Object} The created chart
 */
export function createApexChart(container, type, data, options = {}) {
    console.log('createApexChart called with:', {
        container: container ? container.id || 'no-id' : 'null',
        type,
        data: data ? 'data present' : 'no data',
        options,
        apexChartsAvailable: typeof window.ApexCharts !== 'undefined'
    });

    if (!container) {
        console.error('Container element is not available');
        return null;
    }

    if (!window.ApexCharts) {
        console.error('ApexCharts library is not available');
        console.log('Available global objects:', Object.keys(window).filter(k => k.includes('Chart')));
        return null;
    }

    try {
        // Map chart type to ApexCharts type
        let chartType = type;
        if (type === 'column') {
            chartType = 'bar';
            console.log('Mapped column chart type to bar for ApexCharts');
        }
        if (type === 'doughnut') {
            chartType = 'donut';
            console.log('Mapped doughnut chart type to donut for ApexCharts');
        }

        // Initialize chart options
        let chartOptions = {
            chart: {
                type: chartType,
                height: options.height || 350,
                width: options.width || '100%'
            }
        };

        // Prepare series data
        let series = [];

        console.log('Chart type:', type);
        console.log('Data format received:', JSON.stringify({
            hasCategories: !!data.categories,
            hasValues: !!data.values,
            hasSeries: !!data.series,
            seriesType: data.series ? (Array.isArray(data.series) ? 'array' : typeof data.series) : 'none'
        }));

        // For pie/donut charts, we need a special format
        if (type === 'pie' || type === 'doughnut') {
            // For ApexCharts pie/donut, we need:
            // 1. series: simple array of numbers
            // 2. labels: array of strings

            // First, try to extract the series data
            if (Array.isArray(data)) {
                // If data is already an array of numbers, use it directly
                series = data;
                console.log('Using direct array data for pie/donut chart');
            } else if (data.series && Array.isArray(data.series)) {
                if (data.series.length === 0) {
                    series = [0]; // Default empty series
                    console.warn('Empty series data for pie/donut chart');
                } else if (typeof data.series[0] === 'number') {
                    // If series is already an array of numbers, use it directly
                    series = data.series;
                    console.log('Using numeric series array for pie/donut chart');
                } else if (typeof data.series[0] === 'object' && data.series[0].data) {
                    // If series is an array of objects with data property
                    if (Array.isArray(data.series[0].data)) {
                        if (typeof data.series[0].data[0] === 'number') {
                            // If data is an array of numbers, use it
                            series = data.series[0].data;
                            console.log('Using series[0].data numeric array for pie/donut chart');
                        } else if (typeof data.series[0].data[0] === 'object') {
                            // If data is an array of objects, extract y values
                            series = data.series[0].data.map(item => {
                                if (typeof item === 'object') {
                                    return item.y !== undefined ? item.y : (item.value !== undefined ? item.value : 0);
                                }
                                return 0;
                            });
                            console.log('Extracted y/value from series[0].data objects for pie/donut chart');
                        } else {
                            series = [1]; // Default fallback
                            console.warn('Unexpected data format in series[0].data for pie/donut chart');
                        }
                    } else {
                        series = [1]; // Default fallback
                        console.warn('series[0].data is not an array for pie/donut chart');
                    }
                } else {
                    // Try to extract numeric values from whatever format we have
                    series = data.series.map(item => {
                        if (typeof item === 'number') return item;
                        if (typeof item === 'object') {
                            if (item.data && Array.isArray(item.data) && item.data.length > 0) {
                                return typeof item.data[0] === 'number' ? item.data[0] : 0;
                            }
                            return item.y !== undefined ? item.y : (item.value !== undefined ? item.value : 0);
                        }
                        return 0;
                    });
                    console.log('Extracted values from series objects for pie/donut chart');
                }
            } else if (data.values && Array.isArray(data.values)) {
                // If we have values array, use it
                series = data.values;
                console.log('Using values array for pie/donut chart');
            } else {
                // Default fallback
                series = [1];
                console.warn('No valid data found for pie/donut chart, using default');
            }

            // Ensure all values are numbers with better validation
            series = series.map(val => {
                if (val === null || val === undefined) return 0;
                if (typeof val === 'number' && !isNaN(val)) return val;
                if (typeof val === 'string' && /^\d+\.?\d*$/.test(val.trim())) return parseFloat(val.trim());
                return 0;
            });

            // Set up labels for pie/donut
            chartOptions.labels = [];

            // Try to get labels from various sources
            if (data.labels && Array.isArray(data.labels)) {
                chartOptions.labels = data.labels;
            } else if (data.categories && Array.isArray(data.categories)) {
                chartOptions.labels = data.categories;
            } else if (data.series && Array.isArray(data.series) &&
                      data.series[0] && typeof data.series[0] === 'object' &&
                      data.series[0].data && Array.isArray(data.series[0].data) &&
                      data.series[0].data[0] && typeof data.series[0].data[0] === 'object') {
                // Extract labels from x or name properties in data objects
                chartOptions.labels = data.series[0].data.map(item => item.x || item.name || 'Unnamed');
            } else {
                // Generate default labels
                chartOptions.labels = Array.from({length: series.length}, (_, i) => `Category ${i+1}`);
            }

            // Ensure labels and series have the same length
            if (chartOptions.labels.length !== series.length) {
                console.warn('Labels and series length mismatch, adjusting');
                if (chartOptions.labels.length > series.length) {
                    chartOptions.labels = chartOptions.labels.slice(0, series.length);
                } else {
                    while (chartOptions.labels.length < series.length) {
                        chartOptions.labels.push(`Category ${chartOptions.labels.length + 1}`);
                    }
                }
            }

            // For pie/donut charts, series is the array of values
            chartOptions.series = series;
        } else {
            // For other chart types (bar, line, area, etc.)
            // Check if we have series data in the expected format
            if (data.series && Array.isArray(data.series) && data.series.length > 0) {
                console.log('Using series data from data object:', data.series.length, 'series found');

                // Validate and normalize each series
                series = data.series.map((s, index) => {
                    // If s is not an object, try to convert it
                    if (typeof s !== 'object' || s === null) {
                        return {
                            name: `Series ${index + 1}`,
                            data: Array.isArray(s) ? s : [s]
                        };
                    }

                    // Ensure each series has a name and data property
                    const seriesObj = {
                        name: '',  // Will be set properly below
                        data: []
                    };

                    // Handle series name - ensure it's a string
                    if (s.name) {
                        if (typeof s.name === 'string') {
                            seriesObj.name = s.name;
                        } else if (typeof s.name === 'object' && s.name !== null) {
                            // Handle case where name is an object with text property
                            if (s.name.text) {
                                seriesObj.name = s.name.text;
                            } else if (s.name.toString) {
                                // Try to convert to string
                                seriesObj.name = s.name.toString();
                            } else {
                                // Fallback to default
                                seriesObj.name = `Series ${index + 1}`;
                            }
                        } else {
                            // Convert other types to string
                            seriesObj.name = String(s.name);
                        }
                    } else {
                        seriesObj.name = `Series ${index + 1}`;
                    }

                    // Handle different data formats
                    if (s.data) {
                        if (Array.isArray(s.data)) {
                            // Normalize data to ensure all values are numbers
                            seriesObj.data = s.data.map(val => {
                                if (val === null || val === undefined) return 0;
                                if (typeof val === 'object') {
                                    // Handle {x, y} format
                                    return val.y !== undefined ? Number(val.y) : (val.value !== undefined ? Number(val.value) : 0);
                                }
                                return isNaN(val) ? 0 : Number(val);
                            });
                        } else if (typeof s.data === 'number') {
                            seriesObj.data = [s.data];
                        } else {
                            seriesObj.data = [0];
                        }
                    } else {
                        seriesObj.data = [0];
                    }

                    return seriesObj;
                });
            } else {
                // Fallback to old format
                console.log('Using legacy data format');
                series = [{
                    name: options.title || 'Series',
                    data: Array.isArray(data.values) ? data.values.map(v => Number(v) || 0) : [0]
                }];
            }

            // Set up categories for x-axis if available
            if (data.categories && Array.isArray(data.categories)) {
                chartOptions.xaxis = {
                    ...chartOptions.xaxis,
                    categories: data.categories
                };
            }

            // Set the series for the chart
            chartOptions.series = series;
        }

        // Update chart options with additional properties
        chartOptions.chart = {
            ...chartOptions.chart,
            width: options.width || '100%',
            toolbar: {
                show: true,
                tools: {
                    download: true,
                    selection: false,
                    zoom: false,
                    zoomin: false,
                    zoomout: false,
                    pan: false,
                    reset: false
                }
            },
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            },
            background: '#fff',
            fontFamily: 'Roboto, Arial, sans-serif'
        };

        // Set additional chart options
        chartOptions.series = series;
        chartOptions.xaxis = {
            categories: data.categories || [],
            labels: {
                style: {
                    fontSize: '12px',
                    fontFamily: 'Roboto, Arial, sans-serif'
                },
                trim: true,
                hideOverlappingLabels: true
            },
            axisBorder: {
                show: true,
                color: '#e0e0e0'
            },
            axisTicks: {
                show: true,
                color: '#e0e0e0'
            }
        };

        chartOptions.yaxis = {
            labels: {
                style: {
                    fontSize: '12px',
                    fontFamily: 'Roboto, Arial, sans-serif'
                },
                formatter: function(val) {
                    // Format large numbers with K, M, etc.
                    if (val >= 1000000) return (val / 1000000).toFixed(1) + 'M';
                    if (val >= 1000) return (val / 1000).toFixed(1) + 'K';
                    return val.toFixed(1);
                }
            },
            axisBorder: {
                show: true,
                color: '#e0e0e0'
            },
            axisTicks: {
                show: true,
                color: '#e0e0e0'
            }
        };

        // Process chart title - ensure it's a string
        let chartTitle = 'Chart';

        if (options.title) {
            if (typeof options.title === 'string') {
                chartTitle = options.title;
            } else if (typeof options.title === 'object' && options.title !== null) {
                // Handle case where title is an object with text property
                if (options.title.text) {
                    chartTitle = options.title.text;
                } else if (options.title.toString) {
                    // Try to convert to string
                    chartTitle = options.title.toString();
                }
            } else {
                // Convert other types to string
                chartTitle = String(options.title);
            }
        }

        chartOptions.title = {
            text: chartTitle,
            align: 'center',
            style: {
                fontSize: '16px',
                fontWeight: 'bold',
                fontFamily: 'Roboto, Arial, sans-serif',
                color: '#333'
            }
        };

        chartOptions.colors = options.colors || ['#4472C4', '#ED7D31', '#A5A5A5', '#FFC000', '#5B9BD5', '#70AD47'];

        chartOptions.theme = {
            mode: 'light'
        };

        chartOptions.plotOptions = {
            bar: {
                horizontal: type === 'bar',
                columnWidth: '70%',
                borderRadius: 4,
                dataLabels: {
                    position: 'top'
                }
            },
            pie: {
                donut: {
                    size: type === 'doughnut' ? '50%' : '0%',
                    labels: {
                        show: true,
                        name: {
                            show: true
                        },
                        value: {
                            show: true,
                            formatter: function(val) {
                                return val;
                            }
                        },
                        total: {
                            show: true,
                            label: 'Total',
                            formatter: function(w) {
                                return w.globals.seriesTotals.reduce((a, b) => a + b, 0);
                            }
                        }
                    }
                }
            }
        };

        chartOptions.dataLabels = {
            enabled: type === 'pie' || type === 'doughnut',
            style: {
                fontSize: '12px',
                fontFamily: 'Roboto, Arial, sans-serif',
                fontWeight: 'normal',
                colors: ['#fff']
            },
            background: {
                enabled: false
            }
        };

        chartOptions.stroke = {
            curve: 'smooth',
            width: type === 'line' || type === 'area' ? 3 : 0,
            colors: ['#fff']
        };

        chartOptions.fill = {
            type: type === 'area' ? 'gradient' : 'solid',
            opacity: type === 'area' ? 0.7 : 1,
            gradient: {
                shade: 'light',
                type: 'vertical',
                shadeIntensity: 0.5,
                opacityFrom: 0.7,
                opacityTo: 0.9
            }
        };

        chartOptions.tooltip = {
            enabled: true,
            theme: 'light',
            style: {
                fontSize: '12px',
                fontFamily: 'Roboto, Arial, sans-serif'
            }
        };

        chartOptions.legend = {
            show: series.length > 1 || type === 'pie' || type === 'doughnut',
            position: 'bottom',
            horizontalAlign: 'center',
            fontSize: '12px',
            fontFamily: 'Roboto, Arial, sans-serif',
            markers: {
                width: 12,
                height: 12,
                radius: 12
            },
            itemMargin: {
                horizontal: 10,
                vertical: 0
            }
        };

        chartOptions.grid = {
            borderColor: '#e0e0e0',
            row: {
                colors: ['#f8f9fa', 'transparent'],
                opacity: 0.5
            }
        };

        chartOptions.responsive = [
            {
                breakpoint: 480,
                options: {
                    chart: {
                        width: '100%'
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        ];

        // Additional validation for pie/donut charts
        if (type === 'pie' || type === 'doughnut') {
            // For pie/donut charts, series should be a simple array of numbers
            // and we need labels
            if (!chartOptions.labels || !Array.isArray(chartOptions.labels) || chartOptions.labels.length === 0) {
                console.error('Pie/donut chart requires labels array');
                return createBasicChart(container, type, data, options);
            }

            // Validate series data for pie/donut
            if (!chartOptions.series || !Array.isArray(chartOptions.series)) {
                console.error('Invalid series data for pie/donut chart');
                return createBasicChart(container, type, data, options);
            }

            // For pie charts, ApexCharts expects series to be a simple array of numbers
            if (Array.isArray(chartOptions.series) && chartOptions.series.length > 0) {
                // If series is an array of objects with data property, extract the data
                if (typeof chartOptions.series[0] === 'object' && chartOptions.series[0].data) {
                    console.log('Converting series object to simple array for pie/donut chart');
                    chartOptions.series = chartOptions.series[0].data;
                }

                // Ensure series is an array of numbers
                if (Array.isArray(chartOptions.series) && chartOptions.series.some(item => typeof item === 'object')) {
                    console.error('Pie/donut chart series must be an array of numbers, not objects');
                    console.log('Current series:', chartOptions.series);

                    // Try to convert objects to numbers
                    try {
                        chartOptions.series = chartOptions.series.map(item => {
                            if (typeof item === 'object') {
                                return item.y || item.value || 0;
                            }
                            return typeof item === 'number' ? item : parseFloat(item) || 0;
                        });
                    } catch (e) {
                        console.error('Failed to convert series to numbers:', e);
                        return createBasicChart(container, type, data, options);
                    }
                }
            }

            // Ensure labels and series have the same length
            if (chartOptions.labels.length !== chartOptions.series.length) {
                console.warn('Labels and series length mismatch, adjusting');
                if (chartOptions.labels.length > chartOptions.series.length) {
                    chartOptions.labels = chartOptions.labels.slice(0, chartOptions.series.length);
                } else {
                    while (chartOptions.labels.length < chartOptions.series.length) {
                        chartOptions.series.pop();
                    }
                }
            }
        }

        try {
            // Create and render the chart
            console.log('Creating ApexChart with options:', JSON.stringify({
                type: chartOptions.chart.type,
                series: Array.isArray(chartOptions.series) ?
                    (chartOptions.series.length > 10 ?
                        `[Array with ${chartOptions.series.length} items]` :
                        chartOptions.series) :
                    chartOptions.series,
                labels: chartOptions.labels,
                categories: chartOptions.xaxis?.categories
            }));

            // Return a promise for better async handling
            return new Promise((resolve, reject) => {
                try {
                    // Create the chart instance
                    const chart = new ApexCharts(container, chartOptions);

                    // Render with a small delay to ensure container is ready
                    setTimeout(() => {
                        try {
                            chart.render();
                            console.log('ApexChart rendered successfully');
                            resolve(chart);
                        } catch (renderError) {
                            console.error('Error rendering ApexChart:', renderError);
                            console.error('Chart options that caused the error:', JSON.stringify({
                                type: chartOptions.chart.type,
                                series: typeof chartOptions.series,
                                labels: chartOptions.labels ? 'present' : 'missing'
                            }));

                            // Try to create a fallback chart
                            try {
                                const fallbackChart = createBasicChart(container, type, data, options);
                                resolve(fallbackChart);
                            } catch (fallbackError) {
                                console.error('Fallback chart creation also failed:', fallbackError);
                                reject(renderError);
                            }
                        }
                    }, 50);
                } catch (error) {
                    console.error('Error creating ApexChart instance:', error);

                    // Try to create a fallback chart
                    try {
                        const fallbackChart = createBasicChart(container, type, data, options);
                        resolve(fallbackChart);
                    } catch (fallbackError) {
                        console.error('Fallback chart creation also failed:', fallbackError);
                        reject(error);
                    }
                }
            });
        } catch (error) {
            console.error('Error in createApexChart:', error);
            // Return a resolved promise with the fallback chart
            return Promise.resolve(createBasicChart(container, type, data, options));
        }
    } catch (error) {
        console.error('Error creating ApexChart:', error);
        // Fallback to basic chart
        return Promise.resolve(createBasicChart(container, type, data, options));
    }
}

/**
 * Create an ECharts chart
 * @param {HTMLElement} container - The container element
 * @param {string} type - The chart type
 * @param {Object} data - The chart data
 * @param {Object} options - The chart options
 * @returns {Object} The created chart
 */
export function createEChart(container, type, data, options = {}) {
    if (!container || !window.echarts) {
        console.error('Container element or ECharts library not available');
        return null;
    }

    try {
        // Initialize ECharts instance
        const chart = window.echarts.init(container);

        // Map chart type to ECharts type
        let chartType = type;
        if (type === 'column') chartType = 'bar';
        if (type === 'doughnut') chartType = 'pie';

        // Create chart options
        let chartOptions = {
            title: {
                text: options.title || 'Chart',
                left: 'center',
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'axis'
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            color: options.colors || ['#4472C4']
        };

        // Configure based on chart type
        if (type === 'pie' || type === 'doughnut') {
            // Pie chart configuration
            chartOptions.series = [{
                name: options.title || 'Series',
                type: 'pie',
                radius: type === 'doughnut' ? ['40%', '70%'] : '70%',
                center: ['50%', '50%'],
                data: data.categories.map((cat, index) => ({
                    name: cat,
                    value: data.values[index]
                })),
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                label: {
                    show: true,
                    formatter: '{b}: {c} ({d}%)'
                }
            }];
        } else {
            // Bar, line, area, column charts
            chartOptions.xAxis = {
                type: 'category',
                data: data.categories || [],
                axisLabel: {
                    rotate: data.categories.length > 5 ? 45 : 0
                }
            };

            chartOptions.yAxis = {
                type: 'value'
            };

            chartOptions.series = [{
                name: options.title || 'Series',
                type: chartType,
                data: data.values || [],
                areaStyle: type === 'area' ? {} : null,
                smooth: type === 'line' || type === 'area'
            }];
        }

        // Set options and render
        chart.setOption(chartOptions);

        // Handle resize with proper cleanup
        const resizeHandler = () => {
            if (chart && chart.resize) {
                chart.resize();
            }
        };
        window.addEventListener('resize', resizeHandler);

        // Store resize handler for cleanup
        chart._resizeHandler = resizeHandler;

        return chart;
    } catch (error) {
        console.error('Error creating EChart:', error);
        return null;
    }
}

/**
 * Create a basic HTML/CSS chart (fallback when no chart library is available)
 * @param {HTMLElement} container - The container element
 * @param {string} type - The chart type
 * @param {Object} data - The chart data
 * @param {Object} options - The chart options
 * @returns {Object} The created chart
 */
export function createBasicChart(container, type, data, options = {}) {
    if (!container) {
        console.error('Container element not available');
        return null;
    }

    try {
        // Clear container
        container.innerHTML = '';

        // Create chart container
        const chartDiv = document.createElement('div');
        chartDiv.className = 'basic-chart';
        chartDiv.style.width = '100%';
        chartDiv.style.height = '100%';
        chartDiv.style.padding = '10px';
        chartDiv.style.boxSizing = 'border-box';

        // Add title
        const titleEl = document.createElement('h3');
        titleEl.textContent = options.title || 'Chart';
        titleEl.style.textAlign = 'center';
        titleEl.style.margin = '0 0 15px 0';
        titleEl.style.fontSize = '16px';
        chartDiv.appendChild(titleEl);

        // Create chart based on type
        if (type === 'pie' || type === 'doughnut') {
            createBasicPieChart(chartDiv, data, options, type === 'doughnut');
        } else {
            createBasicBarChart(chartDiv, data, options, type);
        }

        container.appendChild(chartDiv);

        return {
            container: container,
            update: (newData) => {
                return createBasicChart(container, type, newData, options);
            }
        };
    } catch (error) {
        console.error('Error creating basic chart:', error);
        return null;
    }
}

/**
 * Create a basic bar/column/line chart
 * @param {HTMLElement} container - The container element
 * @param {Object} data - The chart data
 * @param {Object} options - The chart options
 * @param {string} type - The chart type
 */
function createBasicBarChart(container, data, options, type) {
    // Create chart container
    const chartContainer = document.createElement('div');
    chartContainer.style.display = 'flex';
    chartContainer.style.flexDirection = 'column';
    chartContainer.style.height = 'calc(100% - 50px)';
    chartContainer.style.position = 'relative';

    // Create chart area
    const chartArea = document.createElement('div');
    chartArea.style.flex = '1';
    chartArea.style.display = 'flex';
    chartArea.style.alignItems = 'flex-end';
    chartArea.style.justifyContent = 'space-around';
    chartArea.style.padding = '0 10px 20px 30px';
    chartArea.style.position = 'relative';

    // Add y-axis line
    const yAxis = document.createElement('div');
    yAxis.style.position = 'absolute';
    yAxis.style.left = '0';
    yAxis.style.top = '0';
    yAxis.style.bottom = '20px';
    yAxis.style.width = '1px';
    yAxis.style.backgroundColor = '#ccc';
    chartArea.appendChild(yAxis);

    // Add x-axis line
    const xAxis = document.createElement('div');
    xAxis.style.position = 'absolute';
    xAxis.style.left = '0';
    xAxis.style.right = '0';
    xAxis.style.bottom = '20px';
    xAxis.style.height = '1px';
    xAxis.style.backgroundColor = '#ccc';
    chartArea.appendChild(xAxis);

    // Find max value for scaling
    const maxValue = Math.max(...data.values, 1);

    // Create bars/columns/points
    data.values.forEach((value, index) => {
        const itemContainer = document.createElement('div');
        itemContainer.style.display = 'flex';
        itemContainer.style.flexDirection = 'column';
        itemContainer.style.alignItems = 'center';
        itemContainer.style.flex = '1';
        itemContainer.style.maxWidth = `${100 / data.values.length}%`;
        itemContainer.style.position = 'relative';
        itemContainer.style.height = '100%';

        const heightPercent = (value / maxValue) * 100;
        const color = options.colors ? options.colors[0] : '#4472C4';

        if (type === 'line' || type === 'area') {
            // For line charts, create dots
            const dot = document.createElement('div');
            dot.style.width = '8px';
            dot.style.height = '8px';
            dot.style.borderRadius = '50%';
            dot.style.backgroundColor = color;
            dot.style.position = 'absolute';
            dot.style.bottom = `calc(${heightPercent}% - 4px)`;
            dot.style.left = '50%';
            dot.style.transform = 'translateX(-50%)';
            dot.style.zIndex = '2';

            // Add line to next point if not the last point
            if (index < data.values.length - 1) {
                const nextValue = data.values[index + 1];
                const nextHeightPercent = (nextValue / maxValue) * 100;

                const line = document.createElement('div');
                line.style.position = 'absolute';
                line.style.height = '2px';
                line.style.backgroundColor = color;
                line.style.bottom = `${heightPercent}%`;
                line.style.left = '50%';
                line.style.width = `${100 / data.values.length}%`;
                line.style.transformOrigin = 'left';

                // Calculate angle for the line
                const angle = Math.atan2(nextHeightPercent - heightPercent, 100) * (180 / Math.PI);
                line.style.transform = `rotate(${-angle}deg)`;

                itemContainer.appendChild(line);
            }

            // For area charts, add fill
            if (type === 'area') {
                const area = document.createElement('div');
                area.style.position = 'absolute';
                area.style.bottom = '0';
                area.style.left = '0';
                area.style.width = '100%';
                area.style.height = `${heightPercent}%`;
                area.style.backgroundColor = color;
                area.style.opacity = '0.2';
                itemContainer.appendChild(area);
            }

            itemContainer.appendChild(dot);
        } else {
            // For bar/column charts
            const bar = document.createElement('div');

            if (type === 'bar') {
                // Horizontal bar
                bar.style.width = `${heightPercent}%`;
                bar.style.height = '20px';
                bar.style.position = 'absolute';
                bar.style.left = '0';
                bar.style.top = '50%';
                bar.style.transform = 'translateY(-50%)';
            } else {
                // Vertical column
                bar.style.width = '60%';
                bar.style.height = `${heightPercent}%`;
                bar.style.position = 'absolute';
                bar.style.bottom = '0';
                bar.style.left = '50%';
                bar.style.transform = 'translateX(-50%)';
            }

            bar.style.backgroundColor = color;
            bar.style.borderRadius = '2px';
            itemContainer.appendChild(bar);
        }

        // Add label
        const label = document.createElement('div');
        label.textContent = data.categories[index] || '';
        label.style.fontSize = '10px';
        label.style.position = 'absolute';
        label.style.bottom = '0';
        label.style.left = '50%';
        label.style.transform = 'translateX(-50%) translateY(100%)';
        label.style.textAlign = 'center';
        label.style.whiteSpace = 'nowrap';
        label.style.overflow = 'hidden';
        label.style.textOverflow = 'ellipsis';
        label.style.maxWidth = '100%';

        itemContainer.appendChild(label);
        chartArea.appendChild(itemContainer);
    });

    chartContainer.appendChild(chartArea);
    container.appendChild(chartContainer);
}

/**
 * Create a basic pie/doughnut chart
 * @param {HTMLElement} container - The container element
 * @param {Object} data - The chart data
 * @param {Object} options - The chart options
 * @param {boolean} isDoughnut - Whether to create a doughnut chart
 */
function createBasicPieChart(container, data, options, isDoughnut) {
    // Create pie container
    const pieContainer = document.createElement('div');
    pieContainer.style.display = 'flex';
    pieContainer.style.flexDirection = 'column';
    pieContainer.style.alignItems = 'center';
    pieContainer.style.height = 'calc(100% - 50px)';

    // Create pie chart
    const pieChart = document.createElement('div');
    pieChart.style.position = 'relative';
    pieChart.style.width = '200px';
    pieChart.style.height = '200px';
    pieChart.style.borderRadius = '50%';
    pieChart.style.overflow = 'hidden';
    pieChart.style.background = '#f0f0f0';

    // Calculate total
    const total = data.values.reduce((sum, val) => sum + val, 0);

    // Create pie segments
    let startAngle = 0;
    data.values.forEach((value, index) => {
        if (value <= 0) return;

        const percentage = value / total;
        const endAngle = startAngle + percentage * 360;
        const color = options.colors ? options.colors[index % options.colors.length] : '#4472C4';

        // Create segment
        const segment = document.createElement('div');
        segment.style.position = 'absolute';
        segment.style.top = '0';
        segment.style.left = '0';
        segment.style.width = '100%';
        segment.style.height = '100%';
        segment.style.background = color;
        segment.style.clipPath = `conic-gradient(from ${startAngle}deg, ${color} ${percentage * 100}%, transparent ${percentage * 100}%)`;

        pieChart.appendChild(segment);
        startAngle = endAngle;
    });

    // Create doughnut hole if needed
    if (isDoughnut) {
        const hole = document.createElement('div');
        hole.style.position = 'absolute';
        hole.style.top = '25%';
        hole.style.left = '25%';
        hole.style.width = '50%';
        hole.style.height = '50%';
        hole.style.borderRadius = '50%';
        hole.style.background = 'white';
        hole.style.zIndex = '2';
        pieChart.appendChild(hole);
    }

    pieContainer.appendChild(pieChart);

    // Create legend
    const legend = document.createElement('div');
    legend.style.display = 'flex';
    legend.style.flexWrap = 'wrap';
    legend.style.justifyContent = 'center';
    legend.style.marginTop = '20px';

    data.categories.forEach((category, index) => {
        const legendItem = document.createElement('div');
        legendItem.style.display = 'flex';
        legendItem.style.alignItems = 'center';
        legendItem.style.margin = '5px 10px';

        const colorBox = document.createElement('div');
        colorBox.style.width = '12px';
        colorBox.style.height = '12px';
        colorBox.style.backgroundColor = options.colors ? options.colors[index % options.colors.length] : '#4472C4';
        colorBox.style.marginRight = '5px';

        const label = document.createElement('span');
        label.textContent = `${category}: ${data.values[index]}`;
        label.style.fontSize = '12px';

        legendItem.appendChild(colorBox);
        legendItem.appendChild(label);
        legend.appendChild(legendItem);
    });

    pieContainer.appendChild(legend);
    container.appendChild(pieContainer);
}
