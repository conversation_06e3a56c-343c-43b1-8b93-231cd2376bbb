// Fix for JSZip module exports
document.addEventListener('DOMContentLoaded', function() {
    // Make sure J<PERSON><PERSON><PERSON> is available globally
    if (typeof JSZip === 'undefined') {
        console.error('JSZip is not defined. Check if the library is properly loaded.');
    } else {
        console.log('JSZip found in window object');

        // Create a test instance to verify J<PERSON><PERSON><PERSON> is working
        try {
            const testZip = new JSZip();

            // Test if the file method exists
            if (typeof testZip.file !== 'function') {
                console.error('JSZip instance does not have file method. This will cause ZIP export to fail.');
            } else {
                console.log('JSZip is properly initialized with file method available.');

                // Test adding a file to the ZIP
                testZip.file('test.txt', 'Test content');
                console.log('Successfully added a test file to JSZip instance.');
            }
        } catch (error) {
            console.error('JSZ<PERSON> test failed:', error);
        }
    }
});
