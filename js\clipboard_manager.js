// clipboard_manager.js - Enhanced spreadsheet clipboard functionality using Chrome storage

/**
 * This clipboard manager is specifically designed to work with spreadsheet cells and multi-cell selection.
 * It stores data in Chrome's local storage and handles copying/pasting between cells.
 * It also maintains a clipboard history and provides a dialog to show what has been copied.
 */

// Import the standardized modal functions
import { createStandardModal } from './standardized-modal.js';

document.addEventListener('DOMContentLoaded', function() {
    // Get references to the clipboard buttons
    const copyBtn = document.getElementById('copyBtn');
    const cutBtn = document.getElementById('cutBtn');
    const clipboardBtn = document.getElementById('clipboardBtn');
    const pastNotesBox = document.querySelector('.past-notes-box');

    // Storage keys
    const CLIPBOARD_STORAGE_KEY = 'excel_filler_clipboard';
    const CLIPBOARD_HISTORY_KEY = 'excel_filler_clipboard_history';
    const MAX_HISTORY_ITEMS = 10;

    // Status message element for visual feedback
    let statusMessageTimeout;

    // Initialize the event listeners
    initializeClipboardHandlers();

    /**
     * Sets up all clipboard button event listeners
     */
    function initializeClipboardHandlers() {
        // Add copy functionality
        if (copyBtn) {
            copyBtn.addEventListener('click', handleCopy);
            copyBtn.style.cursor = 'pointer';
        }

        // Add cut functionality
        if (cutBtn) {
            cutBtn.addEventListener('click', handleCut);
            cutBtn.style.cursor = 'pointer';
        }

        // Handle clipboard button to show history
        if (clipboardBtn) {
            clipboardBtn.addEventListener('click', showClipboardHistoryDialog);
            clipboardBtn.style.cursor = 'pointer';
            clipboardBtn.title = 'Show Clipboard History';
        }

        // Handle past notes button to paste content
        if (pastNotesBox) {
            pastNotesBox.addEventListener('click', handlePaste);
            pastNotesBox.style.cursor = 'pointer';
            pastNotesBox.title = 'Paste (Ctrl+V)';
        }

        // Add keyboard shortcut handlers
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                if (e.key === 'c' || e.key === 'C') {
                    // Don't override default browser behavior, just supplement it
                    setTimeout(handleCopy, 0);
                } else if (e.key === 'x' || e.key === 'X') {
                    // Don't override default browser behavior, just supplement it
                    setTimeout(handleCut, 0);
                } else if (e.key === 'v' || e.key === 'V') {
                    // Don't override default browser behavior
                    // But we can add our custom paste implementation as a backup
                    setTimeout(function() {
                        // Check if the active element's content has changed
                        // If not, our paste might need to run
                        handlePaste();
                    }, 0);
                }
            }
        });
    }

    /**
     * Shows a status message as visual feedback for clipboard operations
     * @param {string} message - The message to display
     * @param {string} type - The type of message ('success', 'error', 'info')
     */
    function showStatusMessage(message, type = 'success') {
        // Clear any existing timeout
        if (statusMessageTimeout) {
            clearTimeout(statusMessageTimeout);
        }

        // Create or get the status message element
        let statusElement = document.getElementById('clipboard-status-message');
        if (!statusElement) {
            statusElement = document.createElement('div');
            statusElement.id = 'clipboard-status-message';
            document.body.appendChild(statusElement);
        }

        // Set the message type styling using classes
        statusElement.className = ''; // Clear existing classes
        statusElement.classList.add('clipboard-status-message');
        statusElement.classList.add(type);

        // Set the message content with appropriate icon
        if (type === 'success') {
            statusElement.innerHTML = '<span class="material-icons">check_circle</span>' + message;
        } else if (type === 'error') {
            statusElement.innerHTML = '<span class="material-icons">error</span>' + message;
        } else {
            statusElement.innerHTML = '<span class="material-icons">info</span>' + message;
        }

        // Show the message
        statusElement.style.opacity = '1';

        // Hide after 3 seconds
        statusMessageTimeout = setTimeout(() => {
            statusElement.style.opacity = '0';
            setTimeout(() => {
                if (statusElement.parentNode) {
                    statusElement.parentNode.removeChild(statusElement);
                }
            }, 300);
        }, 3000);
    }

    /**
     * Add an item to the clipboard history
     * @param {Object} clipboardData - The data to add to history
     */
    function addToClipboardHistory(clipboardData) {
        try {
            // Get existing history or initialize a new one
            let history = [];
            const historyJson = localStorage.getItem(CLIPBOARD_HISTORY_KEY);

            if (historyJson) {
                try {
                    history = JSON.parse(historyJson);
                    // Validate history structure
                    if (!Array.isArray(history)) {
                        console.error('Clipboard history is not an array');
                        history = [];
                    }
                } catch (parseErr) {
                    console.error('Error parsing clipboard history:', parseErr);
                    history = [];
                }
            }

            // Add timestamp to the clipboard data
            clipboardData.timestamp = new Date().toISOString();

            // Add to the beginning of the history array
            history.unshift(clipboardData);

            // Limit the history size
            if (history.length > MAX_HISTORY_ITEMS) {
                history = history.slice(0, MAX_HISTORY_ITEMS);
            }

            // Save back to storage
            localStorage.setItem(CLIPBOARD_HISTORY_KEY, JSON.stringify(history));
        } catch (err) {
            console.error('Error adding to clipboard history:', err);
        }
    }

    /**
     * Show the clipboard history dialog
     */
    function showClipboardHistoryDialog() {
        try {
            // Get the clipboard history
            const historyJson = localStorage.getItem(CLIPBOARD_HISTORY_KEY);
            let history = [];

            if (historyJson) {
                try {
                    history = JSON.parse(historyJson);
                    // Validate history structure
                    if (!Array.isArray(history)) {
                        console.error('Clipboard history is not an array');
                        history = [];
                    }
                } catch (parseErr) {
                    console.error('Error parsing clipboard history:', parseErr);
                    history = [];
                }
            }

            // Create the content for the dialog
            let content = '<div class="clipboard-history-container">';

            if (history.length === 0) {
                content += '<p class="clipboard-empty-message">No items in clipboard history</p>';
            } else {
                // Add a message about the most recent item
                content += '<div class="clipboard-recent-message">Most recent item appears at the top</div>';
                content += '<div class="clipboard-history-list">';

                // Sort history by timestamp (most recent first)
                history.sort((a, b) => {
                    return new Date(b.timestamp) - new Date(a.timestamp);
                });

                history.forEach((item, index) => {
                    const date = new Date(item.timestamp);
                    const formattedDate = `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;

                    content += `<div class="clipboard-history-item" data-index="${index}">`;
                    content += `<div class="clipboard-item-header">`;
                    content += `<span class="clipboard-item-type">${item.type === 'cells' ? 'Cells' : 'Text'}</span>`;
                    content += `<span class="clipboard-item-time">${formattedDate}</span>`;
                    content += `</div>`;

                    content += `<div class="clipboard-item-content">`;
                    if (item.type === 'cells') {
                        content += `<div class="clipboard-cells-preview">`;
                        content += `<span>${item.rows}×${item.cols} cells</span>`;

                        // Show a preview of the first few cells
                        const maxPreviewCells = 3;
                        const previewData = item.data.slice(0, maxPreviewCells).map(row =>
                            row.slice(0, maxPreviewCells).join(', ')
                        ).join(' | ');

                        content += `<div class="clipboard-preview-text">${previewData}${item.data.length > maxPreviewCells ? '...' : ''}</div>`;
                        content += `</div>`;
                    } else {
                        // For text, show the content directly (truncated if too long)
                        const maxLength = 100;
                        const displayText = item.data.length > maxLength
                            ? item.data.substring(0, maxLength) + '...'
                            : item.data;

                        content += `<div class="clipboard-text-preview">${displayText}</div>`;
                    }
                    content += `</div>`;

                    content += `<div class="clipboard-item-actions">`;
                    content += `<button class="clipboard-paste-btn" data-index="${index}">Paste</button>`;
                    content += `<button class="clipboard-delete-btn" data-index="${index}">Delete</button>`;
                    content += `</div>`;

                    content += `</div>`;
                });

                content += '</div>';
            }

            content += '</div>';

            // Create the modal
            const modal = createStandardModal({
                title: 'Clipboard History',
                content: content,
                size: 'large',
                buttons: [
                    {
                        text: 'Clear All',
                        primary: false,
                        id: 'clearClipboardBtn',
                        onClick: () => {
                            localStorage.removeItem(CLIPBOARD_HISTORY_KEY);
                            showStatusMessage('Clipboard history cleared', 'info');
                        }
                    },
                    {
                        text: 'Close',
                        primary: true,
                        id: 'closeClipboardBtn'
                    }
                ],
                showLogo: true,
                logoIcon: 'content_paste',
                id: 'clipboard-history-modal'
            });

            // Show the modal
            modal.show();

            // Add event listeners to the paste and delete buttons
            setTimeout(() => {
                const pasteBtns = document.querySelectorAll('.clipboard-paste-btn');
                const deleteBtns = document.querySelectorAll('.clipboard-delete-btn');

                pasteBtns.forEach(btn => {
                    btn.addEventListener('click', () => {
                        const index = parseInt(btn.dataset.index);
                        // Validate index is in proper range
                        if (isNaN(index) || index < 0 || index >= history.length) {
                            showStatusMessage('Invalid clipboard history item', 'error');
                            return;
                        }
                        const item = history[index];

                        // Set as current clipboard item
                        localStorage.setItem(CLIPBOARD_STORAGE_KEY, JSON.stringify(item));

                        // Move this item to the top of history (most recent)
                        history.splice(index, 1);
                        history.unshift(item);
                        localStorage.setItem(CLIPBOARD_HISTORY_KEY, JSON.stringify(history));

                        // Close the modal
                        modal.close();

                        // Paste the item
                        handlePaste();
                    });
                });

                deleteBtns.forEach(btn => {
                    btn.addEventListener('click', () => {
                        const index = parseInt(btn.dataset.index);

                        // Validate index is in proper range
                        if (isNaN(index) || index < 0 || index >= history.length) {
                            showStatusMessage('Invalid clipboard history item', 'error');
                            return;
                        }

                        // Remove from history
                        history.splice(index, 1);

                        // Save back to storage
                        localStorage.setItem(CLIPBOARD_HISTORY_KEY, JSON.stringify(history));

                        // Remove the item from the UI
                        const item = btn.closest('.clipboard-history-item');
                        if (item && item.parentNode) {
                            item.parentNode.removeChild(item);
                        }

                        // Show message
                        showStatusMessage('Item removed from clipboard history', 'info');

                        // If history is now empty, close the modal
                        if (history.length === 0) {
                            modal.close();
                        }
                    });
                });
            }, 100);

            // CSS is now in an external file: css/clipboard.css
        } catch (err) {
            console.error('Error showing clipboard history:', err);
            showStatusMessage('Error showing clipboard history', 'error');
        }
    }

    /**
     * Handle the copy action for spreadsheet cells
     */
    function handleCopy() {
        try {
            // First try to get selected cells
            const cellData = getSelectedCellsData();

            if (cellData && cellData.length > 0) {
                // We have cell data

                // Create a text representation for system clipboard
                let textRepresentation = '';
                for (let r = 0; r < cellData.length; r++) {
                    textRepresentation += cellData[r].join('\t') + '\n';
                }

                // Copy to system clipboard using execCommand as a fallback
                // Note: execCommand is deprecated but still widely supported and more reliable
                // than the Clipboard API in many browsers for this specific use case
                const tempTextarea = document.createElement('textarea');
                tempTextarea.value = textRepresentation;
                document.body.appendChild(tempTextarea);
                tempTextarea.select();
                document.execCommand('copy');
                document.body.removeChild(tempTextarea);

                // Create clipboard data object for our app
                const colCount = cellData[0] ? cellData[0].length : 0;
                const clipboardData = {
                    type: 'cells',
                    data: cellData,
                    rows: cellData.length,
                    cols: colCount
                };

                // Store in our app clipboard
                localStorage.setItem(CLIPBOARD_STORAGE_KEY, JSON.stringify(clipboardData));

                // Add to history
                addToClipboardHistory(clipboardData);

                // Show feedback
                showStatusMessage(`${cellData.length}×${clipboardData.cols} cells copied to clipboard`);

                console.log('Cells copied to storage:', cellData);
                return;
            }

            // No cells selected, try fallback to normal text selection
            const selectedText = getSelectedText();
            if (selectedText) {
                // Copy to system clipboard using execCommand as a fallback
                const tempTextarea = document.createElement('textarea');
                tempTextarea.value = selectedText;
                document.body.appendChild(tempTextarea);
                tempTextarea.select();
                document.execCommand('copy');
                document.body.removeChild(tempTextarea);

                const clipboardData = {
                    type: 'text',
                    data: selectedText
                };

                // Store in our app clipboard
                localStorage.setItem(CLIPBOARD_STORAGE_KEY, JSON.stringify(clipboardData));

                // Add to history
                addToClipboardHistory(clipboardData);

                // Show feedback
                showStatusMessage('Text copied to clipboard');

                console.log('Text copied to storage:', selectedText);
                return;
            }

            // Nothing to copy
            showStatusMessage('Nothing selected to copy', 'info');

        } catch (err) {
            console.error('Error in copy operation:', err);
            showStatusMessage('Error copying to clipboard', 'error');
        }
    }

    /**
     * Handle the cut action for spreadsheet cells
     */
    function handleCut() {
        try {
            // First try to get selected cells
            const cellData = getSelectedCellsData();

            if (cellData && cellData.length > 0) {
                // We have cell data

                // Create a text representation for system clipboard
                let textRepresentation = '';
                for (let r = 0; r < cellData.length; r++) {
                    textRepresentation += cellData[r].join('\t') + '\n';
                }

                // Copy to system clipboard using execCommand as a fallback
                const tempTextarea = document.createElement('textarea');
                tempTextarea.value = textRepresentation;
                document.body.appendChild(tempTextarea);
                tempTextarea.select();
                document.execCommand('copy');
                document.body.removeChild(tempTextarea);

                // Create clipboard data object for our app
                const colCount = cellData[0] ? cellData[0].length : 0;
                const clipboardData = {
                    type: 'cells',
                    data: cellData,
                    rows: cellData.length,
                    cols: colCount
                };

                // Store in our app clipboard
                localStorage.setItem(CLIPBOARD_STORAGE_KEY, JSON.stringify(clipboardData));

                // Add to history
                addToClipboardHistory(clipboardData);

                // Clear the cell contents
                clearSelectedCells();

                // Show feedback
                showStatusMessage(`${cellData.length}×${clipboardData.cols} cells cut to clipboard`);

                console.log('Cells cut to storage:', cellData);
                return;
            }

            // No cells selected, try fallback to normal text selection
            const selectedText = getSelectedText();
            if (selectedText) {
                // Copy to system clipboard using execCommand as a fallback
                const tempTextarea = document.createElement('textarea');
                tempTextarea.value = selectedText;
                document.body.appendChild(tempTextarea);
                tempTextarea.select();
                document.execCommand('copy');
                document.body.removeChild(tempTextarea);

                const clipboardData = {
                    type: 'text',
                    data: selectedText
                };

                // Store in our app clipboard
                localStorage.setItem(CLIPBOARD_STORAGE_KEY, JSON.stringify(clipboardData));

                // Add to history
                addToClipboardHistory(clipboardData);

                // Try to delete the selected text
                document.execCommand('delete');

                // Show feedback
                showStatusMessage('Text cut to clipboard');

                console.log('Text cut to storage:', selectedText);
                return;
            }

            // Nothing to cut
            showStatusMessage('Nothing selected to cut', 'info');

        } catch (err) {
            console.error('Error in cut operation:', err);
            showStatusMessage('Error cutting to clipboard', 'error');
        }
    }

    /**
     * Handle the paste action for spreadsheet cells
     * This function pastes the most recently copied content from our app's clipboard
     * If nothing is in our app clipboard, it falls back to the system clipboard
     */
    function handlePaste() {
        try {
            // Get data from our app clipboard
            const clipboardJson = localStorage.getItem(CLIPBOARD_STORAGE_KEY);
            if (!clipboardJson) {
                // Try to use system paste command
                // Note: execCommand is deprecated but still widely supported and more reliable
                // than the Clipboard API in many browsers for this specific use case
                document.execCommand('paste');
                return;
            }

            const clipboardData = JSON.parse(clipboardJson);

            // Verify clipboardData is properly structured
            if (!clipboardData || !clipboardData.type) {
                console.error('Invalid clipboard data format');
                showStatusMessage('Invalid clipboard data format', 'error');
                return;
            }

            // Get the selected cell to use as target
            const targetCell = getActiveCellOrSelected();
            if (!targetCell) {
                showStatusMessage('No target cell selected for paste', 'error');
                return;
            }

            if (clipboardData.type === 'cells') {
                // Paste the cell data at the target position
                pasteCellsData(targetCell, clipboardData.data);

                // Show feedback
                showStatusMessage(`${clipboardData.rows}×${clipboardData.cols} cells pasted`);

                console.log('Cells pasted from storage');
            } else if (clipboardData.type === 'text') {
                // For text, just paste into the active cell
                targetCell.textContent = clipboardData.data;

                // Show feedback
                showStatusMessage('Text pasted');

                console.log('Text pasted to cell:', clipboardData.data);
            }
        } catch (err) {
            console.error('Error pasting from clipboard storage:', err);
            showStatusMessage('Error pasting from clipboard', 'error');

            // Try to use system paste command as fallback
            // Note: execCommand is deprecated but still widely supported and more reliable
            // than the Clipboard API in many browsers for this specific use case
            try {
                document.execCommand('paste');
            } catch (pasteErr) {
                console.error('Error using system paste command:', pasteErr);
            }
        }
    }

    /**
     * Get data from selected cells in the format of a 2D array
     * @returns {Array<Array<string>>} 2D array of cell values
     */
    function getSelectedCellsData() {
        try {
            // First, check if we have a global selection range
            if (window.selectedRange) {
                const range = window.selectedRange;
                console.log('Using global selection range:', range);

                const data = [];

                // Check if we have a column or row selection
                if (range.type === 'column') {
                    console.log('Processing column selection for clipboard');

                    // For column selection, get all cells in the column
                    // First, find all cells in the column
                    const columnCells = document.querySelectorAll(`td input[data-col="${range.start.c}"]`);
                    console.log(`Found ${columnCells.length} cells in column ${range.start.c} for clipboard`);

                    // Process each cell in the column
                    columnCells.forEach(cell => {
                        const row = [];
                        row.push(cell.value || '');
                        data.push(row);
                    });

                    // If no cells were found, add an empty row
                    if (data.length === 0) {
                        data.push(['']);
                    }
                } else if (range.type === 'row') {
                    console.log('Processing row selection for clipboard');

                    // For row selection, get all cells in the row
                    const rowCells = document.querySelectorAll(`td input[data-row="${range.start.r}"]`);
                    console.log(`Found ${rowCells.length} cells in row ${range.start.r} for clipboard`);

                    // Create a single row with all cell values
                    const row = [];
                    rowCells.forEach(cell => {
                        row.push(cell.value || '');
                    });

                    // Add the row to the data
                    data.push(row);

                    // If no cells were found, add an empty row
                    if (data.length === 0 || data[0].length === 0) {
                        data.push(['']);
                    }
                } else {
                    // Regular cell selection
                    console.log(`Processing cell selection from [${range.start.r},${range.start.c}] to [${range.end.r},${range.end.c}] for clipboard`);

                    // Extract the data from the range
                    for (let r = range.start.r; r <= range.end.r; r++) {
                        const row = [];
                        for (let c = range.start.c; c <= range.end.c; c++) {
                            const cell = document.querySelector(`td input[data-row="${r}"][data-col="${c}"]`);
                            if (cell && cell.parentElement) {
                                row.push(cell.value || '');
                            } else {
                                row.push(''); // Empty cell
                            }
                        }
                        data.push(row);
                    }

                    // If no cells were found, add an empty row
                    if (data.length === 0) {
                        data.push(['']);
                    }
                }

                // Make sure we have actual data to return
                if (data.length > 0 && !(data.length === 1 && data[0].length === 0)) {
                    return data;
                }
            }

            // Check if we have the selection_manager exported functions
            if (window.getSelectedRange) {
                const range = window.getSelectedRange();
                if (range) {
                    console.log('Using selection manager range:', range);

                    const data = [];

                    // Extract the data from the range
                    for (let r = range.start.r; r <= range.end.r; r++) {
                        const row = [];
                        for (let c = range.start.c; c <= range.end.c; c++) {
                            const cell = document.querySelector(`td[data-row="${r}"][data-col="${c}"]`);
                            row.push(getContentFromCell(cell));
                        }
                        data.push(row);
                    }

                    // Make sure we have actual data to return
                    if (data.length > 0 && !(data.length === 1 && data[0].length === 0)) {
                        return data;
                    }
                }
            }

            // If we get here, we need to determine the selection manually

            // Get both selected cells and the active cell
            let selectedCells = Array.from(document.querySelectorAll('.selected-cell'));

            // If no .selected-cell elements found, try .selected class as fallback
            if (!selectedCells || selectedCells.length === 0) {
                selectedCells = Array.from(document.querySelectorAll('.selected'));
            }

            // Get the active cell and add it to the selection if it's not already included
            const activeCell = document.querySelector('.active-cell');
            if (activeCell) {
                // Check if the active cell is already in the selection
                const activeCellInSelection = selectedCells.some(cell =>
                    cell === activeCell ||
                    (cell.dataset.row === activeCell.dataset.row &&
                     cell.dataset.col === activeCell.dataset.col)
                );

                // If active cell is not in the selection, add it
                if (!activeCellInSelection) {
                    selectedCells.push(activeCell);
                }
            }

            // If still no selection, but we have an active cell
            if ((!selectedCells || selectedCells.length === 0) && activeCell) {
                // Create a single-cell selection
                const data = [[getContentFromCell(activeCell)]];
                return data;
            } else if (!selectedCells || selectedCells.length === 0) {
                return null;
            }

            // Determine the range of the selection
            let minRow = Infinity;
            let maxRow = -Infinity;
            let minCol = Infinity;
            let maxCol = -Infinity;

            selectedCells.forEach(cell => {
                const row = parseInt(cell.dataset.row);
                const col = parseInt(cell.dataset.col);

                if (!isNaN(row) && !isNaN(col)) {
                    minRow = Math.min(minRow, row);
                    maxRow = Math.max(maxRow, row);
                    minCol = Math.min(minCol, col);
                    maxCol = Math.max(maxCol, col);
                }
            });

            console.log(`Selection range determined: [${minRow},${minCol}] to [${maxRow},${maxCol}]`);

            // Create a 2D array to hold the data
            const data = [];

            // Fill the array with cell data
            for (let r = minRow; r <= maxRow; r++) {
                const row = [];
                for (let c = minCol; c <= maxCol; c++) {
                    const cell = document.querySelector(`td[data-row="${r}"][data-col="${c}"]`);
                    row.push(getContentFromCell(cell));
                }
                data.push(row);
            }

            // Make sure we have actual data to return
            if (data.length === 0 || (data.length === 1 && data[0].length === 0)) {
                return null;
            }

            return data;
        } catch (error) {
            console.error('Error getting selected cell data:', error);
            return null;
        }
    }

    /**
     * Helper function to get content from a cell, handling different cell structures
     * @param {HTMLElement} cell - The cell element
     * @returns {string} The cell content
     */
    function getContentFromCell(cell) {
        if (!cell) return '';

        // Check for input element first
        const input = cell.querySelector('input');
        if (input) return input.value;

        // Otherwise use textContent
        return cell.textContent || '';
    }

    /**
     * Clear the contents of selected cells
     */
    function clearSelectedCells() {
        try {
            // First, check if we have a global selection range
            if (window.selectedRange) {
                const range = window.selectedRange;
                console.log('Clearing cells using global selection range:', range);

                // Check if we have a column or row selection
                if (range.type === 'column') {
                    console.log('Clearing column selection');

                    // For column selection, clear all cells in the column
                    // First, find all cells in the column
                    const columnCells = document.querySelectorAll(`td input[data-col="${range.start.c}"]`);
                    console.log(`Found ${columnCells.length} cells in column ${range.start.c} to clear`);

                    // Clear each cell in the column
                    columnCells.forEach(cell => {
                        if (cell.parentElement) {
                            clearCellContent(cell.parentElement);
                        }
                    });
                } else if (range.type === 'row') {
                    console.log('Clearing row selection');

                    // For row selection, clear all cells in the row
                    const rowCells = document.querySelectorAll(`td input[data-row="${range.start.r}"]`);
                    console.log(`Found ${rowCells.length} cells in row ${range.start.r} to clear`);

                    // Clear each cell in the row
                    rowCells.forEach(cell => {
                        if (cell.parentElement) {
                            clearCellContent(cell.parentElement);
                        }
                    });
                } else {
                    // Regular cell selection
                    console.log(`Clearing cell selection from [${range.start.r},${range.start.c}] to [${range.end.r},${range.end.c}]`);

                    // Clear all cells in the range
                    for (let r = range.start.r; r <= range.end.r; r++) {
                        for (let c = range.start.c; c <= range.end.c; c++) {
                            const cell = document.querySelector(`td input[data-row="${r}"][data-col="${c}"]`);
                            if (cell && cell.parentElement) {
                                clearCellContent(cell.parentElement);
                            }
                        }
                    }
                }
                return;
            }

            // Get both selected cells and the active cell
            let selectedCells = Array.from(document.querySelectorAll('.selected-cell'));

            // If no .selected-cell elements found, try .selected class as fallback
            if (!selectedCells || selectedCells.length === 0) {
                selectedCells = Array.from(document.querySelectorAll('.selected'));
            }

            // Get the active cell and add it to the selection if it's not already included
            const activeCell = document.querySelector('.active-cell');
            if (activeCell) {
                // Check if the active cell is already in the selection
                const activeCellInSelection = selectedCells.some(cell =>
                    cell === activeCell ||
                    (cell.dataset.row === activeCell.dataset.row &&
                     cell.dataset.col === activeCell.dataset.col)
                );

                // If active cell is not in the selection, add it
                if (!activeCellInSelection) {
                    selectedCells.push(activeCell);
                }
            }

            // If still no cells found, use active cell only
            if ((!selectedCells || selectedCells.length === 0) && activeCell) {
                clearCellContent(activeCell);
                return;
            } else if (!selectedCells || selectedCells.length === 0) {
                console.warn('No cells found to clear');
                return;
            }

            // Clear content of all selected cells
            selectedCells.forEach(cell => {
                clearCellContent(cell);
            });

            console.log(`Cleared ${selectedCells.length} cells`);
        } catch (error) {
            console.error('Error clearing selected cells:', error);
        }
    }

    /**
     * Helper function to clear cell content properly
     * @param {HTMLElement} cell - The cell to clear
     */
    function clearCellContent(cell) {
        // Check if cell has an input element
        const input = cell.querySelector('input');
        if (input) {
            input.value = '';
        } else {
            cell.textContent = '';
        }

        // If using a data model, update it
        try {
            if (window.currentSheet && typeof window.currentSheet.cell === 'function') {
                const row = parseInt(cell.dataset.row);
                const col = parseInt(cell.dataset.col);
                if (!isNaN(row) && !isNaN(col)) {
                    window.currentSheet.cell(row, col).value(null);
                }
            }
        } catch (error) {
            console.warn('Could not update cell in sheet model:', error);
        }
    }

    /**
     * Gets the active cell or first selected cell
     * @returns {HTMLElement|null} The active or selected cell
     */
    function getActiveCellOrSelected() {
        // Try getting the active cell first using the global function
        if (window.getActiveCell) {
            const activeCell = window.getActiveCell();
            if (activeCell) return activeCell;
        }

        // Otherwise check for active-cell class
        const activeCell = document.querySelector('.active-cell');
        if (activeCell) return activeCell;

        // Check for any selected-cell class
        let selectedCells = document.querySelectorAll('.selected-cell');
        if (selectedCells && selectedCells.length > 0) {
            return selectedCells[0];
        }

        // Finally, check for any selected class as fallback
        selectedCells = document.querySelectorAll('.selected');
        if (selectedCells && selectedCells.length > 0) {
            return selectedCells[0];
        }

        return null;
    }

    /**
     * Paste cell data starting at the target cell
     * @param {HTMLElement} targetCell - The cell to start pasting at
     * @param {Array<Array<string>>} data - 2D array of cell values
     */
    function pasteCellsData(targetCell, data) {
        if (!targetCell || !targetCell.dataset || !data || !data.length) {
            console.warn('Invalid parameters for pasteCellsData');
            return;
        }

        try {
            const startRow = parseInt(targetCell.dataset.row);
            const startCol = parseInt(targetCell.dataset.col);

            if (isNaN(startRow) || isNaN(startCol)) {
                console.warn('Invalid row or column in target cell');
                return;
            }

            console.log(`Pasting ${data.length}×${data[0].length} cells starting at [${startRow},${startCol}]`);

            // Track if we're updating multiple cells for history purposes
            let cellsUpdated = 0;

            // Paste data into cells
            for (let r = 0; r < data.length; r++) {
                for (let c = 0; c < data[r].length; c++) {
                    const targetRow = startRow + r;
                    const targetCol = startCol + c;
                    const cell = document.querySelector(`td[data-row="${targetRow}"][data-col="${targetCol}"]`);

                    if (cell) {
                        // Set cell content based on cell structure
                        setCellContent(cell, data[r][c]);
                        cellsUpdated++;

                        // Update the underlying data model if available
                        if (window.currentSheet && typeof window.currentSheet.cell === 'function') {
                            try {
                                window.currentSheet.cell(targetRow, targetCol).value(data[r][c]);
                            } catch (modelError) {
                                console.warn(`Could not update cell [${targetRow},${targetCol}] in sheet model:`, modelError);
                            }
                        }
                    }
                }
            }

            // Update history if multiple cells were updated
            if (cellsUpdated > 1 && window.historyManager && typeof window.historyManager.addState === 'function') {
                try {
                    window.historyManager.addState(window.workbook, 'Paste cells');
                } catch (historyError) {
                    console.warn('Could not add paste operation to history:', historyError);
                }
            }

            // Trigger a custom event to notify that cells have been updated
            document.dispatchEvent(new CustomEvent('cells-updated', {
                detail: {
                    startRow: startRow,
                    startCol: startCol,
                    endRow: startRow + data.length - 1,
                    endCol: startCol + (data[0] ? data[0].length - 1 : 0)
                }
            }));
        } catch (error) {
            console.error('Error pasting cell data:', error);
            showStatusMessage('Error pasting data', 'error');
        }
    }

    /**
     * Helper function to set cell content properly
     * @param {HTMLElement} cell - The cell to update
     * @param {string} content - The content to set
     */
    function setCellContent(cell, content) {
        // Check if cell has an input element
        const input = cell.querySelector('input');
        if (input) {
            input.value = content;

            // Trigger input event to ensure any listeners are notified
            const event = new Event('input', { bubbles: true });
            input.dispatchEvent(event);
        } else {
            // Otherwise set the text content directly
            cell.textContent = content;
        }
    }

    // Helper function to get selected text (fallback)
    function getSelectedText() {
        if (window.getSelection) {
            return window.getSelection().toString();
        }
        else if (document.selection && document.selection.type !== "Control") {
            return document.selection.createRange().text;
        }
        return '';
    }

    // This function is kept for reference but not currently used
    // It may be useful in future implementations
    /*
    function insertTextAtCursor(text) {
        const activeElement = document.activeElement;

        if (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA') {
            const start = activeElement.selectionStart;
            const end = activeElement.selectionEnd;
            activeElement.value = activeElement.value.substring(0, start) +
                             text +
                             activeElement.value.substring(end);
            activeElement.selectionStart = activeElement.selectionEnd = start + text.length;
        }
        else if (activeElement.isContentEditable || document.designMode === "on") {
            // Modern approach using Selection API directly instead of execCommand
            if (window.getSelection) {
                const selection = window.getSelection();
                if (selection.rangeCount > 0) {
                    const range = selection.getRangeAt(0);
                    range.deleteContents();
                    range.insertNode(document.createTextNode(text));
                }
            }
        }
    }
    */

    /**
     * Debug function to log selection information
     * This helps diagnose issues with multi-cell operations
     */
    function debugSelectionInfo() {
        console.group('Clipboard Selection Debug Info');

        // Check for active cell
        const activeCell = document.querySelector('.active-cell');
        console.log('Active Cell:', activeCell ?
            `Row ${activeCell.dataset.row}, Col ${activeCell.dataset.col}` : 'None');

        if (activeCell) {
            console.log('Active Cell Content:', getContentFromCell(activeCell));
        }

        // Check for selected-cell class
        const selectedCells = document.querySelectorAll('.selected-cell');
        console.log('Selected Cells (.selected-cell):', selectedCells.length);

        // Check for selected class
        const selectedClassCells = document.querySelectorAll('.selected');
        console.log('Selected Cells (.selected):', selectedClassCells.length);

        // Log details of selected cells
        if (selectedCells.length > 0 || selectedClassCells.length > 0) {
            console.group('Selected Cell Details');

            // Log selected-cell class cells
            if (selectedCells.length > 0) {
                console.log('Cells with .selected-cell class:');
                Array.from(selectedCells).forEach((cell, index) => {
                    console.log(`  Cell ${index + 1}: Row ${cell.dataset.row}, Col ${cell.dataset.col}, Content: "${getContentFromCell(cell)}"`);
                });
            }

            // Log selected class cells
            if (selectedClassCells.length > 0) {
                console.log('Cells with .selected class:');
                Array.from(selectedClassCells).forEach((cell, index) => {
                    console.log(`  Cell ${index + 1}: Row ${cell.dataset.row}, Col ${cell.dataset.col}, Content: "${getContentFromCell(cell)}"`);
                });
            }

            console.groupEnd();
        }

        // Check for selection range
        if (window.selectedRange) {
            console.log('Global Selection Range:', window.selectedRange);

            // Calculate how many cells are in the range
            const cellCount = (window.selectedRange.end.r - window.selectedRange.start.r + 1) *
                             (window.selectedRange.end.c - window.selectedRange.start.c + 1);
            console.log(`Range contains ${cellCount} cells`);
        } else if (window.getSelectedRange && window.getSelectedRange()) {
            const range = window.getSelectedRange();
            console.log('Selection Manager Range:', range);

            // Calculate how many cells are in the range
            const cellCount = (range.end.r - range.start.r + 1) *
                             (range.end.c - range.start.c + 1);
            console.log(`Range contains ${cellCount} cells`);
        } else {
            console.log('No Selection Range Found');
        }

        // Check clipboard storage
        try {
            const clipboardJson = localStorage.getItem(CLIPBOARD_STORAGE_KEY);
            if (clipboardJson) {
                const clipboardData = JSON.parse(clipboardJson);
                console.log('Clipboard Data:', clipboardData);

                if (clipboardData.type === 'cells') {
                    console.log(`Clipboard contains ${clipboardData.rows}×${clipboardData.cols} cells`);

                    // Show a preview of the clipboard data
                    console.group('Clipboard Data Preview');
                    for (let r = 0; r < Math.min(clipboardData.rows, 3); r++) {
                        const rowPreview = clipboardData.data[r].slice(0, Math.min(clipboardData.cols, 3)).join(', ');
                        console.log(`Row ${r + 1}: ${rowPreview}${clipboardData.cols > 3 ? '...' : ''}`);
                    }
                    if (clipboardData.rows > 3) {
                        console.log('...');
                    }
                    console.groupEnd();
                }
            } else {
                console.log('No Clipboard Data Found');
            }
        } catch (error) {
            console.error('Error reading clipboard data:', error);
        }

        // Test the getSelectedCellsData function
        console.group('Testing getSelectedCellsData Function');
        const testData = getSelectedCellsData();
        if (testData) {
            console.log(`Function returned ${testData.length}×${testData[0].length} cells`);
            console.log('Data:', testData);
        } else {
            console.log('Function returned null (no selection)');
        }
        console.groupEnd();

        console.groupEnd();
    }

    // Make debug function available globally
    window.debugClipboardSelection = debugSelectionInfo;
});
