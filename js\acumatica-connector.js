// js/acumatica-connector.js
// Acumatica Connector implementation

import { connectionManager } from '../@core/connection.js';
import { notificationSystem } from '../@core/notification-system.js';
import { spreadsheetIntegration } from './spreadsheet-integration.js';

class AcumaticaConnector {
    constructor() {
        this.isInitialized = false;
        this.panelElement = null;
        this.connectionStatus = {
            isConnected: false,
            instance: null,
            username: null
        };
    }

    async init() {
        if (this.isInitialized) {
            console.log('Acumatica Connector already initialized');
            return;
        }

        try {
            console.log('Initializing Acumatica Connector...');

            // Initialize connection manager
            await connectionManager.init();
            console.log('Connection manager initialized');

            // Get current connection status
            const status = connectionManager.getConnectionStatus();
            this.connectionStatus = status.acumatica || { isConnected: false, instance: null, username: null };
            console.log('Connection status retrieved:', this.connectionStatus);

            // Set up event listeners
            this.setupEventListeners();
            console.log('Event listeners set up');

            // Create panel element
            this.createPanelElement();
            console.log('Panel element created');

            console.log('Acumatica Connector initialized successfully');
            this.isInitialized = true;

            // Return true to indicate successful initialization
            return true;
        } catch (error) {
            console.error('Failed to initialize Acumatica Connector:', error);
            // Return false to indicate failed initialization
            return false;
        }
    }

    setupEventListeners() {
        // Listen for menu item click
        const menuItem = document.getElementById('acumaticaConnectorBtn');
        if (menuItem) {
            console.log('Found acumaticaConnectorBtn, adding click event listener');
            menuItem.addEventListener('click', () => {
                console.log('Acumatica connector button clicked');
                this.togglePanel();
            });
        } else {
            console.warn('acumaticaConnectorBtn not found in the DOM');

            // Add a mutation observer to watch for the button to be added to the DOM
            const observer = new MutationObserver((mutations, obs) => {
                const menuItem = document.getElementById('acumaticaConnectorBtn');
                if (menuItem) {
                    console.log('acumaticaConnectorBtn found after DOM mutation');
                    menuItem.addEventListener('click', () => {
                        console.log('Acumatica connector button clicked (added later)');
                        this.togglePanel();
                    });
                    obs.disconnect(); // Stop observing once we've found the button
                }
            });

            // Start observing the document with the configured parameters
            observer.observe(document.body, { childList: true, subtree: true });
        }

        // Listen for connection changes
        document.addEventListener('acumaticaConnectionChanged', (event) => {
            console.log('acumaticaConnectionChanged event received', event);
            this.updateConnectionStatus();
        });
    }

    createPanelElement() {
        // Create panel if it doesn't exist
        if (!this.panelElement) {
            this.panelElement = document.createElement('div');
            this.panelElement.id = 'acumaticaPanel';
            this.panelElement.className = 'side-panel';
            this.panelElement.style.display = 'none';
            this.panelElement.style.position = 'fixed';
            this.panelElement.style.top = '60px';
            this.panelElement.style.right = '0';
            this.panelElement.style.width = '300px';
            this.panelElement.style.height = 'calc(100vh - 60px)';
            this.panelElement.style.backgroundColor = '#ffffff';
            this.panelElement.style.boxShadow = '-2px 0 5px rgba(0,0,0,0.1)';
            this.panelElement.style.zIndex = '1000';
            this.panelElement.style.padding = '16px';
            this.panelElement.style.overflowY = 'auto';
            this.panelElement.style.transition = 'transform 0.3s ease-in-out';
            this.panelElement.style.transform = 'translateX(100%)';

            document.body.appendChild(this.panelElement);
        }

        // Update panel content
        this.updatePanelContent();
    }

    updatePanelContent() {
        if (!this.panelElement) return;

        const status = this.connectionStatus;

        this.panelElement.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                <h2 style="margin: 0; font-size: 18px;">Acumatica Connector</h2>
                <button id="closeAcumaticaPanel" style="background: none; border: none; cursor: pointer;">
                    <span class="material-icons">close</span>
                </button>
            </div>

            <div style="padding: 12px; background-color: #f5f5f5; border-radius: 4px; margin-bottom: 16px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                    <div style="display: flex; align-items: center;">
                        <span class="material-icons" style="margin-right: 8px; color: ${status.isConnected ? '#4caf50' : '#9e9e9e'};">
                            ${status.isConnected ? 'check_circle' : 'error_outline'}
                        </span>
                        <span style="font-weight: 500;">Connection Status</span>
                    </div>
                    <span style="padding: 4px 8px; border-radius: 12px; font-size: 12px; background-color: ${status.isConnected ? '#e8f5e9' : '#f5f5f5'}; color: ${status.isConnected ? '#2e7d32' : '#616161'};">
                        ${status.isConnected ? 'Connected' : 'Disconnected'}
                    </span>
                </div>

                ${status.isConnected ? `
                    <div style="font-size: 14px; color: #616161; margin-bottom: 8px;">
                        <p style="margin: 4px 0;">Connected to: ${status.instance}</p>
                        <p style="margin: 4px 0;">User: ${status.username}</p>
                    </div>
                    <button id="disconnectAcumaticaBtn" style="width: 100%; padding: 8px; background-color: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer; margin-top: 8px;">
                        Disconnect
                    </button>
                ` : `
                    <form id="acumaticaConnectForm" style="margin-top: 12px;">
                        <div style="margin-bottom: 8px;">
                            <label style="display: block; font-size: 12px; margin-bottom: 4px;">Instance URL</label>
                            <input type="text" id="acumaticaInstance" value="https://envent-eng.acumatica.com" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div style="margin-bottom: 8px;">
                            <label style="display: block; font-size: 12px; margin-bottom: 4px;">Username</label>
                            <input type="text" id="acumaticaUsername" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div style="margin-bottom: 8px;">
                            <label style="display: block; font-size: 12px; margin-bottom: 4px;">Password</label>
                            <input type="password" id="acumaticaPassword" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div style="margin-bottom: 12px;">
                            <label style="display: block; font-size: 12px; margin-bottom: 4px;">Company</label>
                            <input type="text" id="acumaticaCompany" value="Envent CA - Live" style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <button type="submit" id="connectAcumaticaBtn" style="width: 100%; padding: 8px; background-color: #1976d2; color: white; border: none; border-radius: 4px; cursor: pointer;">
                            Connect
                        </button>
                    </form>
                `}
            </div>

            ${status.isConnected ? `
                <div style="margin-top: 20px;">
                    <h3 style="font-size: 16px; margin-bottom: 12px;">Available Actions</h3>
                    <button id="fetchInvoicesBtn" style="width: 100%; padding: 8px; background-color: #2196f3; color: white; border: none; border-radius: 4px; cursor: pointer; margin-bottom: 8px;">
                        Fetch Invoices
                    </button>
                    <button id="loadInvoicesBtn" style="width: 100%; padding: 8px; background-color: #4caf50; color: white; border: none; border-radius: 4px; cursor: pointer; margin-bottom: 8px;">
                        Load Invoices from Storage
                    </button>
                    <button id="fetchDataBtn" style="width: 100%; padding: 8px; background-color: #2196f3; color: white; border: none; border-radius: 4px; cursor: pointer; margin-bottom: 8px;">
                        Fetch Data from Acumatica
                    </button>
                    <button id="exportToAcumaticaBtn" style="width: 100%; padding: 8px; background-color: #4caf50; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        Export Data to Acumatica
                    </button>
                </div>
            ` : ''}
        `;

        // Add event listeners to the panel elements
        this.addPanelEventListeners();
    }

    addPanelEventListeners() {
        // Close button
        const closeBtn = document.getElementById('closeAcumaticaPanel');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.hidePanel());
        }

        // Connect form
        const connectForm = document.getElementById('acumaticaConnectForm');
        if (connectForm) {
            connectForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.handleConnect();
            });
        }

        // Disconnect button
        const disconnectBtn = document.getElementById('disconnectAcumaticaBtn');
        if (disconnectBtn) {
            disconnectBtn.addEventListener('click', async () => {
                await this.handleDisconnect();
            });
        }

        // Fetch invoices button
        const fetchInvoicesBtn = document.getElementById('fetchInvoicesBtn');
        if (fetchInvoicesBtn) {
            fetchInvoicesBtn.addEventListener('click', async () => {
                fetchInvoicesBtn.disabled = true;
                fetchInvoicesBtn.textContent = 'Fetching Invoices...';
                try {
                    await this.handleFetchInvoices();
                } finally {
                    fetchInvoicesBtn.disabled = false;
                    fetchInvoicesBtn.textContent = 'Fetch Invoices';
                }
            });
        }

        // Load invoices button
        const loadInvoicesBtn = document.getElementById('loadInvoicesBtn');
        if (loadInvoicesBtn) {
            loadInvoicesBtn.addEventListener('click', async () => {
                loadInvoicesBtn.disabled = true;
                loadInvoicesBtn.textContent = 'Loading Invoices...';
                try {
                    await this.handleLoadInvoices();
                } finally {
                    loadInvoicesBtn.disabled = false;
                    loadInvoicesBtn.textContent = 'Load Invoices from Storage';
                }
            });
        }

        // Fetch data button
        const fetchDataBtn = document.getElementById('fetchDataBtn');
        if (fetchDataBtn) {
            fetchDataBtn.addEventListener('click', () => {
                this.handleFetchData();
            });
        }

        // Export data button
        const exportBtn = document.getElementById('exportToAcumaticaBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.handleExportData();
            });
        }
    }

    async handleConnect() {
        const instance = document.getElementById('acumaticaInstance').value;
        const username = document.getElementById('acumaticaUsername').value;
        const password = document.getElementById('acumaticaPassword').value;
        const company = document.getElementById('acumaticaCompany').value;

        if (!instance || !username || !password || !company) {
            this.showNotification('Please fill in all fields', 'error');
            return;
        }

        // Disable the connect button
        const connectBtn = document.getElementById('connectAcumaticaBtn');
        if (connectBtn) {
            connectBtn.disabled = true;
            connectBtn.textContent = 'Connecting...';
        }

        try {
            // Connect to Acumatica
            const result = await connectionManager.connectToAcumatica(instance, username, password, company);

            if (result.success) {
                this.showNotification('Successfully connected to Acumatica', 'success');
                this.updateConnectionStatus();

                // Dispatch event for other components
                document.dispatchEvent(new CustomEvent('acumaticaConnectionChanged', {
                    detail: { connected: true }
                }));
            } else {
                this.showNotification(`Failed to connect: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showNotification(`Error: ${error.message}`, 'error');
        } finally {
            // Re-enable the connect button
            if (connectBtn) {
                connectBtn.disabled = false;
                connectBtn.textContent = 'Connect';
            }
        }
    }

    async handleDisconnect() {
        // Disable the disconnect button
        const disconnectBtn = document.getElementById('disconnectAcumaticaBtn');
        if (disconnectBtn) {
            disconnectBtn.disabled = true;
            disconnectBtn.textContent = 'Disconnecting...';
        }

        try {
            // Disconnect from Acumatica
            const result = await connectionManager.disconnectFromAcumatica();

            if (result.success) {
                this.showNotification('Successfully disconnected from Acumatica', 'success');
                this.updateConnectionStatus();

                // Dispatch event for other components
                document.dispatchEvent(new CustomEvent('acumaticaConnectionChanged', {
                    detail: { connected: false }
                }));
            } else {
                this.showNotification(`Failed to disconnect: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showNotification(`Error: ${error.message}`, 'error');
        } finally {
            // Re-enable the disconnect button
            if (disconnectBtn) {
                disconnectBtn.disabled = false;
                disconnectBtn.textContent = 'Disconnect';
            }
        }
    }

    async handleFetchInvoices() {
        try {
            this.showNotification('Fetching invoices from Acumatica...', 'info');
            console.log('AcumaticaConnector: Fetching invoices from Acumatica');

            // Get connection status
            const { instance, isConnected } = this.connectionStatus;
            console.log('AcumaticaConnector: Connection status:', { instance, isConnected });

            if (!isConnected) {
                this.showNotification('Not connected to Acumatica. Please connect first.', 'error');
                console.error('AcumaticaConnector: Not connected to Acumatica');
                return;
            }

            // Get cookies from Chrome storage
            const cookies = await this.getCookiesFromStorage(instance);
            console.log('AcumaticaConnector: Got cookies from storage:', cookies ? Object.keys(cookies).length : 0, 'cookies');

            if (!cookies || Object.keys(cookies).length === 0) {
                this.showNotification('No cookies found. Please reconnect to Acumatica.', 'error');
                console.error('AcumaticaConnector: No cookies found');
                return;
            }

            // Create cookie header string
            const cookieHeader = Object.entries(cookies)
                .map(([name, value]) => `${name}=${value}`)
                .join('; ');

            console.log('AcumaticaConnector: Using cookie header (length):', cookieHeader.length);

            // Check if spreadsheet integration is initialized
            if (!spreadsheetIntegration.initialized) {
                console.log('AcumaticaConnector: Initializing spreadsheet integration');
                spreadsheetIntegration.init();
            }

            this.showNotification('Connecting to Acumatica API...', 'info');
            console.log('AcumaticaConnector: Fetching invoices from API endpoint');

            // Fetch invoices using the exact endpoint provided
            const response = await fetch(`${instance}/entity/test/22.200.001/invoice?$filter=CreatedDateTime ge datetimeoffset'2025-01-01T00:00:00Z' and CreatedDateTime lt datetimeoffset'2026-01-01T00:00:00Z'&$expand=ShipToContact`, {
                method: 'GET',
                headers: {
                    'Cookie': cookieHeader
                },
                credentials: 'include'
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('AcumaticaConnector: API response error:', response.status, errorText);
                throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
            }

            this.showNotification('Processing response from Acumatica...', 'info');

            // Parse response
            const invoices = await response.json();
            console.log('AcumaticaConnector: Fetched invoices:', Array.isArray(invoices) ? invoices.length : 'not an array', invoices);

            if (!Array.isArray(invoices) || invoices.length === 0) {
                this.showNotification('No invoices returned from Acumatica API', 'warning');
                console.warn('AcumaticaConnector: No invoices returned from API');
                return;
            }

            // Use the simplified spreadsheet integration
            console.log('AcumaticaConnector: Saving invoices data to spreadsheet integration');
            spreadsheetIntegration.saveInvoicesData(invoices);

            this.showNotification(`Successfully fetched ${invoices.length} invoices`, 'success');

            // If spreadsheet is not available yet, let the user know
            if (!window.spreadsheet) {
                console.log('AcumaticaConnector: Spreadsheet not available yet, data will be inserted when ready');
                this.showNotification('Data will be inserted into the spreadsheet when it becomes available', 'info');

                // Set up a check to notify the user when the data is actually inserted
                let checkCount = 0;
                const maxChecks = 30; // Check for 30 seconds max

                const checkDataInserted = () => {
                    checkCount++;
                    if (window.spreadsheet && !spreadsheetIntegration.invoicesData) {
                        // Data has been inserted
                        this.showNotification('Invoices data has been successfully inserted into the spreadsheet', 'success');
                    } else if (checkCount < maxChecks) {
                        setTimeout(checkDataInserted, 1000);
                    }
                };

                setTimeout(checkDataInserted, 2000);
            } else {
                console.log('AcumaticaConnector: Spreadsheet is available, data should be inserted immediately');
            }
        } catch (error) {
            console.error('Failed to fetch invoices:', error);
            this.showNotification(`Failed to fetch invoices: ${error.message}`, 'error');
        }
    }

    async getCookiesFromStorage(domain) {
        return new Promise((resolve, reject) => {
            try {
                // Check if chrome.storage is available
                if (!chrome || !chrome.storage || !chrome.storage.local) {
                    console.warn('Chrome storage not available, cannot get cookies');
                    resolve({});
                    return;
                }

                // Get cookies from Chrome storage
                const key = `cookies_${domain.replace(/[^a-zA-Z0-9]/g, '_')}`;

                chrome.storage.local.get([key], (result) => {
                    if (chrome.runtime.lastError) {
                        console.error('Error getting cookies:', chrome.runtime.lastError);
                        reject(chrome.runtime.lastError);
                        return;
                    }

                    if (result[key]) {
                        console.log('Got cookies from Chrome storage');
                        resolve(result[key]);
                    } else {
                        console.log('No cookies found in Chrome storage');
                        resolve({});
                    }
                });
            } catch (error) {
                console.error('Error getting cookies:', error);
                reject(error);
            }
        });
    }

    // These methods have been moved to spreadsheet-integration.js

    async handleLoadInvoices() {
        try {
            this.showNotification('Loading invoices from storage...', 'info');
            console.log('AcumaticaConnector: Loading invoices from storage');

            // Load invoices from storage
            const invoices = await spreadsheetIntegration.loadFromStorage();
            console.log('AcumaticaConnector: Loaded invoices from storage:', invoices ? invoices.length : 0, 'invoices');

            if (!invoices || invoices.length === 0) {
                this.showNotification('No invoices found in storage. Please fetch invoices first.', 'warning');
                console.warn('AcumaticaConnector: No invoices found in storage');
                return;
            }

            // Check if spreadsheet integration is initialized
            if (!spreadsheetIntegration.initialized) {
                console.log('AcumaticaConnector: Initializing spreadsheet integration');
                spreadsheetIntegration.init();
            }

            // Save to spreadsheet integration
            console.log('AcumaticaConnector: Saving invoices data to spreadsheet integration');
            spreadsheetIntegration.saveInvoicesData(invoices);

            this.showNotification(`Successfully loaded ${invoices.length} invoices from storage`, 'success');

            // If spreadsheet is not available yet, let the user know
            if (!window.spreadsheet) {
                console.log('AcumaticaConnector: Spreadsheet not available yet, data will be inserted when ready');
                this.showNotification('Data will be inserted into the spreadsheet when it becomes available', 'info');

                // Set up a check to notify the user when the data is actually inserted
                let checkCount = 0;
                const maxChecks = 30; // Check for 30 seconds max

                const checkDataInserted = () => {
                    checkCount++;
                    if (window.spreadsheet && !spreadsheetIntegration.invoicesData) {
                        // Data has been inserted
                        this.showNotification('Invoices data has been successfully inserted into the spreadsheet', 'success');
                    } else if (checkCount < maxChecks) {
                        setTimeout(checkDataInserted, 1000);
                    }
                };

                setTimeout(checkDataInserted, 2000);
            } else {
                console.log('AcumaticaConnector: Spreadsheet is available, data should be inserted immediately');
            }
        } catch (error) {
            console.error('Failed to load invoices from storage:', error);
            this.showNotification(`Failed to load invoices: ${error.message}`, 'error');
        }
    }

    handleFetchData() {
        // Placeholder for fetch data functionality
        this.showNotification('Fetch data functionality will be implemented in a future update', 'info');
    }

    handleExportData() {
        // Placeholder for export data functionality
        this.showNotification('Export data functionality will be implemented in a future update', 'info');
    }

    async updateConnectionStatus() {
        // Get current connection status
        const status = connectionManager.getConnectionStatus();
        this.connectionStatus = status.acumatica;

        // Update panel content
        this.updatePanelContent();
    }

    togglePanel() {
        console.log('togglePanel called');

        // Make sure the panel element exists
        if (!this.panelElement) {
            console.log('Panel element does not exist, creating it');
            this.createPanelElement();
        }

        if (this.panelElement) {
            console.log('Current panel transform:', this.panelElement.style.transform);

            if (this.panelElement.style.transform === 'translateX(0%)') {
                console.log('Panel is visible, hiding it');
                this.hidePanel();
            } else {
                console.log('Panel is hidden, showing it');
                this.showPanel();
            }
        } else {
            console.error('Failed to create panel element');
        }
    }

    showPanel() {
        if (this.panelElement) {
            this.panelElement.style.display = 'block';
            // Use setTimeout to ensure the display change takes effect before the transform
            setTimeout(() => {
                this.panelElement.style.transform = 'translateX(0%)';
            }, 10);
        }
    }

    hidePanel() {
        if (this.panelElement) {
            this.panelElement.style.transform = 'translateX(100%)';
            // Hide the panel after the transition completes
            setTimeout(() => {
                this.panelElement.style.display = 'none';
            }, 300);
        }
    }

    showNotification(message, type = 'info') {
        // Use the notification system
        notificationSystem.addNotification(message, type);
    }
}

// Create and export singleton instance
export const acumaticaConnector = new AcumaticaConnector();

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    acumaticaConnector.init();
});
