/**
 * Excel-like Chart Manager
 * Handles chart creation, editing, and management
 */
import EnhancedChartDialog from './enhanced-chart-dialog.js';

// Import chart utilities
import {
    extractDataFromRange,
    formatRangeAddress,
    createApex<PERSON>hart,
    create<PERSON>hart,
    createBasic<PERSON><PERSON>
} from './chart-utils.js';

const ChartManager = (function() {
    // Private variables
    let _currentSheet = null;
    let _workbook = null;
    let _chartLibrary = 'apexcharts';
    let _charts = [];
    let _enhancedDialog = null;
    let _chartManagerInitialized = false;

    // Chart types
    const CHART_TYPES = {
        COLUMN: 'column',
        BAR: 'bar',
        LINE: 'line',
        PIE: 'pie',
        AREA: 'area',
        SCATTER: 'scatter',
        DOUGHNUT: 'doughnut'
    };

    // Default chart colors
    const DEFAULT_COLORS = [
        '#4472C4', '#ED7D31', '#A5A5A5', '#FFC000',
        '#5B9BD5', '#70AD47', '#264478', '#9E480E'
    ];

    /**
     * Initialize the chart manager
     * @param {Object} workbook - The Excel workbook
     * @param {Object} sheet - The current sheet
     */
    function init(workbook, sheet) {
        if (_chartManagerInitialized) return;
        _workbook = workbook;
        _currentSheet = sheet;

        // Debug available libraries
        console.log('Checking available chart libraries:');
        console.log('ApexCharts available:', typeof ApexCharts !== 'undefined');
        console.log('ECharts available:', typeof window.echarts !== 'undefined');

        // Detect available chart libraries
        if (typeof ApexCharts !== 'undefined') {
            _chartLibrary = 'apexcharts';
        } else if (typeof window.echarts !== 'undefined') {
            _chartLibrary = 'echarts';
        } else {
            _chartLibrary = 'basic';
            console.warn('No chart libraries detected, falling back to basic HTML charts');
        }

        _enhancedDialog = new EnhancedChartDialog(this);
        _chartManagerInitialized = true;
        console.log(`Chart Manager initialized with ${_chartLibrary} library`);
    }

    /**
     * Update the current sheet reference
     */
    function setCurrentSheet(sheet) {
        _currentSheet = sheet;
    }

    /**
     * Show the enhanced chart creation dialog
     * @param {Object} selectedRange - The selected cell range
     */
    function showEnhancedChartDialog(selectedRange) {
        if (!_chartManagerInitialized) {
            console.error("Chart Manager not initialized. Call init() first.");
            alert("Chart functionality is not ready. Please try again later.");
            return;
        }
        if (!_enhancedDialog) {
            console.error("Enhanced Chart Dialog not initialized.");
            alert("Chart dialog failed to load.");
            return;
        }

        // The EnhancedChartDialog handles data extraction and processing internally
        _enhancedDialog.show(selectedRange, (finalConfig) => {
            // Callback receives the final chart configuration
            if (finalConfig) {
                createChartFromConfig(finalConfig);
            }
        });
    }

    /**
     * Create and insert a chart into the spreadsheet using the final config
     * @param {Object} config - The final chart configuration from the dialog
     */
    function createChartFromConfig(config) {
        console.log('Creating chart from config:', config);

        const { type, title, colors, data, sourceRange } = config;
        const styleOptions = config; // The config object contains the generated style options

        // Check if spreadsheet container exists
        const spreadsheetContainer = document.getElementById('spreadsheetContainer');
        if (!spreadsheetContainer) {
            console.error('Spreadsheet container not found');
            alert('Cannot create chart: Spreadsheet container missing.');
            return null;
        }

        // Validate data
        if (!data || (!data.categories && !data.labels) || (!data.series && !data.values)) {
            console.error('Invalid chart data received from dialog:', data);
            alert('Cannot create chart: Invalid data format provided by dialog.');
            return null;
        }

        // Calculate a reasonable starting position (e.g., center of viewport)
        const vpWidth = window.innerWidth || document.documentElement.clientWidth;
        const vpHeight = window.innerHeight || document.documentElement.clientHeight;
        const chartWidth = 600;
        const chartHeight = 420;
        const initialTop = Math.max(100, (vpHeight / 2) - (chartHeight / 2));
        const initialLeft = Math.max(100, (vpWidth / 2) - (chartWidth / 2));

        // Create chart container with proper styling
        const chartContainer = document.createElement('div');
        chartContainer.className = 'excel-chart draggable-chart'; // Add draggable class
        chartContainer.style.position = 'absolute';
        chartContainer.style.width = `${chartWidth}px`;
        chartContainer.style.height = `${chartHeight}px`;
        chartContainer.style.top = `${initialTop}px`;
        chartContainer.style.left = `${initialLeft}px`;
        chartContainer.style.zIndex = '100';
        chartContainer.style.borderRadius = '12px';
        chartContainer.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.08)';
        chartContainer.style.overflow = 'hidden';

        // Create chart header with proper styling
        const chartHeader = document.createElement('div');
        chartHeader.className = 'excel-chart-header';
        chartHeader.innerHTML = `
            <div class="excel-chart-title">${title}</div>
            <div class="excel-chart-controls">
                <button class="excel-chart-btn excel-chart-refresh" title="Refresh Chart">
                    <span class="material-icons">refresh</span>
                </button>
                <button class="excel-chart-btn excel-chart-edit" title="Edit Chart">
                    <span class="material-icons">edit</span>
                </button>
                <button class="excel-chart-btn excel-chart-delete" title="Delete Chart">
                    <span class="material-icons">delete</span>
                </button>
            </div>
        `;
        chartContainer.appendChild(chartHeader);

        // Add hover effects to buttons
        const buttons = chartHeader.querySelectorAll('.excel-chart-btn');
        buttons.forEach(button => {
            button.addEventListener('mouseover', () => button.style.backgroundColor = 'rgba(0, 0, 0, 0.05)');
            button.addEventListener('mouseout', () => button.style.backgroundColor = '');
        });

        // Create chart content area
        const chartContent = document.createElement('div');
        chartContent.className = 'excel-chart-content';
        chartContainer.appendChild(chartContent);

        // Add chart to the spreadsheet
        spreadsheetContainer.appendChild(chartContainer);

        // Store chart data with source range for future updates
        const chartData = {
            id: 'chart_' + Date.now(),
            config: { ...config }, // Store the full configuration
            element: chartContainer,
            chartInstance: null // Will be set after rendering
        };

        // Add to charts collection
        _charts.push(chartData);

        // Function to render the chart (can be reused for refresh)
        const renderChart = (currentData) => {
            try {
                chartContent.innerHTML = ''; // Clear previous content
                let chartInstance;

                // Prepare options specifically for the library
                let finalOptions = { ...styleOptions }; // Start with styles
                finalOptions.chart = {
                    ...styleOptions.chart,
                    height: '100%',
                    width: '100%',
                    background: '#ffffff',
                    animations: {
                        enabled: true,
                        easing: 'easeinout',
                        speed: 800
                    }
                };
                finalOptions.title = { text: title, ...styleOptions.title };

                // Log the data we're working with
                console.log('Chart data for rendering:', {
                    type: type,
                    title: title,
                    dataStructure: {
                        hasCategories: !!currentData.categories,
                        hasLabels: !!currentData.labels,
                        hasSeries: !!currentData.series,
                        seriesLength: currentData.series ? currentData.series.length : 0
                    }
                });

                let dataForLib;
                if (_chartLibrary === 'apexcharts') {
                    if (type === 'pie' || type === 'doughnut') {
                        // For pie/donut charts, ensure we have proper data format
                        if (currentData.series && Array.isArray(currentData.series)) {
                            // If series is an array of objects with data property
                            if (currentData.series.length > 0 && typeof currentData.series[0] === 'object' && currentData.series[0].data) {
                                console.log('Extracting data from series[0].data for pie/donut chart');
                                dataForLib = {
                                    series: currentData.series[0].data,
                                    labels: currentData.labels || currentData.categories || []
                                };
                            } else {
                                // Direct series array
                                dataForLib = {
                                    series: currentData.series,
                                    labels: currentData.labels || currentData.categories || []
                                };
                            }
                        } else {
                            // Fallback to empty data
                            console.warn('No valid series data for pie/donut chart');
                            dataForLib = {
                                series: [1],
                                labels: ['No Data']
                            };
                        }

                        // Ensure labels are set
                        finalOptions.labels = dataForLib.labels;
                    } else {
                        // For other chart types
                        finalOptions.xaxis = {
                            categories: currentData.categories || [],
                            ...styleOptions.xaxis
                        };

                        // Ensure series is properly formatted
                        if (currentData.series && Array.isArray(currentData.series)) {
                            dataForLib = {
                                series: currentData.series.map(series => {
                                    // Ensure each series has name and data properties
                                    if (typeof series === 'object' && series !== null) {
                                        return {
                                            name: typeof series.name === 'string' ? series.name : 'Series',
                                            data: Array.isArray(series.data) ? series.data : [0]
                                        };
                                    } else {
                                        return { name: 'Series', data: [0] };
                                    }
                                }),
                                categories: currentData.categories || []
                            };
                        } else {
                            // Fallback to empty data
                            console.warn('No valid series data for chart');
                            dataForLib = {
                                series: [{ name: 'Series', data: [0] }],
                                categories: ['Category 1']
                            };
                        }
                    }
                } else {
                    dataForLib = currentData; // ECharts/Basic use the processed structure
                }

                // Log the prepared data
                console.log('Prepared chart data for library:', {
                    library: _chartLibrary,
                    dataFormat: dataForLib ?
                        (typeof dataForLib === 'object' ?
                            (Array.isArray(dataForLib) ? 'array' : 'object') :
                            typeof dataForLib) : 'null'
                });

                // Create the chart with a delay to ensure the container is ready
                setTimeout(() => {
                    try {
                        if (_chartLibrary === 'apexcharts' && typeof window.ApexCharts !== 'undefined') {
                            chartInstance = createApexChart(chartContent, type, dataForLib, finalOptions);
                        } else if (_chartLibrary === 'echarts' && typeof window.echarts !== 'undefined') {
                            chartInstance = createEChart(chartContent, type, dataForLib, finalOptions);
                        } else {
                            // Basic chart needs simpler data structure
                            const basicData = {
                                categories: currentData.categories || currentData.labels || [],
                                values: (currentData.series && currentData.series[0]) ?
                                    currentData.series[0].data :
                                    (Array.isArray(currentData.series) ? currentData.series : [0])
                            };
                            chartInstance = createBasicChart(chartContent, type, basicData, finalOptions);
                        }

                        if (chartInstance) {
                            console.log('Chart instance created successfully');
                        } else {
                            console.error('Failed to create chart instance');
                        }
                    } catch (error) {
                        console.error('Error creating chart instance:', error);
                        chartContent.innerHTML = '<div style="padding: 20px; text-align: center; color: red;">Error creating chart: ' + error.message + '</div>';
                    }

                    // Store the chart instance (will be updated after the timeout)
                    chartData.chartInstance = chartInstance;

                    // Add animation class
                    chartContent.classList.add('chart-appear');
                }, 100); // Short delay to ensure container is ready
            } catch (error) {
                console.error('Error rendering chart:', error);
                chartContent.innerHTML = '<div style="padding: 20px; text-align: center; color: red;">Error rendering chart.</div>';
            }
        };

        // Initial render
        renderChart(data);

        // Make chart draggable
        makeChartDraggable(chartContainer, chartHeader);

        // Setup chart controls
        setupChartControls(chartContainer, chartData, renderChart);

        return chartData;
    }

    /**
     * Make a chart draggable
     * @param {HTMLElement} chartElement - The chart container element
     * @param {HTMLElement} handleElement - The drag handle element
     */
    function makeChartDraggable(chartElement, handleElement) {
        let isDragging = false;
        let offsetX = 0;
        let offsetY = 0;
        let mouseMoveHandler, mouseUpHandler;

        handleElement.style.cursor = 'move';

        handleElement.addEventListener('mousedown', (e) => {
            if (e.button !== 0) return;
            isDragging = true;
            const chartRect = chartElement.getBoundingClientRect();
            offsetX = e.clientX - chartRect.left;
            offsetY = e.clientY - chartRect.top;
            chartElement.style.zIndex = '1001'; // Bring to front
            chartElement.classList.add('dragging');
            e.preventDefault();

            mouseMoveHandler = (moveEvent) => {
                if (!isDragging) return;
                const x = moveEvent.clientX - offsetX;
                const y = moveEvent.clientY - offsetY;
                chartElement.style.left = `${x}px`;
                chartElement.style.top = `${y}px`;
            };

            mouseUpHandler = () => {
                if (!isDragging) return;
                isDragging = false;
                chartElement.style.zIndex = '100'; // Reset z-index
                chartElement.classList.remove('dragging');
                document.removeEventListener('mousemove', mouseMoveHandler);
                document.removeEventListener('mouseup', mouseUpHandler);
            };

            document.addEventListener('mousemove', mouseMoveHandler);
            document.addEventListener('mouseup', mouseUpHandler);
        });
    }

    /**
     * Setup chart control buttons
     * @param {HTMLElement} chartElement - The chart container element
     * @param {Object} chartData - The chart data object containing config etc.
     * @param {Function} renderChart - Function to re-render the chart
     */
    function setupChartControls(chartElement, chartData, renderChart) {
        const refreshBtn = chartElement.querySelector('.excel-chart-refresh');
        const editBtn = chartElement.querySelector('.excel-chart-edit');
        const deleteBtn = chartElement.querySelector('.excel-chart-delete');

        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                console.log('Refresh clicked for chart:', chartData.id);
                if (!chartData.config.sourceRange) {
                    alert('Cannot refresh: Chart source data range not stored.');
                    return;
                }
                try {
                     // Show loading/refresh indicator?
                    const freshData = extractDataFromRange(chartData.config.sourceRange);
                    if (freshData && freshData.isValidData) {
                        chartData.config.data = processChartData(freshData, chartData.config.type);
                        renderChart(chartData.config.data); // Re-render with new data
                         console.log('Chart refreshed successfully');
                    } else {
                         alert('Failed to refresh chart data from source range.');
                         console.error('Failed to extract fresh data:', freshData);
                    }
                } catch (error) {
                    console.error('Error refreshing chart data:', error);
                    alert('Error refreshing chart: ' + error.message);
                }
            });
        }

        if (editBtn) {
            editBtn.addEventListener('click', () => {
                console.log('Edit clicked for chart:', chartData.id);
                editChart(chartData, renderChart);
            });
        }

        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => {
                if (confirm('Are you sure you want to delete this chart?')) {
                    deleteChart(chartData);
                }
            });
        }
    }

    /**
     * Edit an existing chart using the enhanced dialog
     * @param {Object} chartData - The chart data object
     * @param {Function} renderChart - Function to re-render the chart
     */
    function editChart(chartData, renderChart) {
        if (!_enhancedDialog) {
            console.error("Cannot edit chart: Enhanced dialog not initialized.");
            return;
        }

        // Temporarily set the dialog\'s options to the chart being edited
        _enhancedDialog.chartOptions = { ...chartData.config };
        _enhancedDialog.selectedRange = chartData.config.sourceRange;
        _enhancedDialog.extractedData = extractDataFromRange(chartData.config.sourceRange);
        _enhancedDialog.processedData = chartData.config.data;

        // Show the dialog for editing
        _enhancedDialog.show(chartData.config.sourceRange, (updatedConfig) => {
            if (updatedConfig) {
                // Update the stored chart data
                chartData.config = { ...updatedConfig };

                // Update the chart title in the header
                 const titleElement = chartData.element.querySelector('.excel-chart-title');
                 if (titleElement) titleElement.textContent = updatedConfig.title;

                // Re-render the chart with the updated config
                renderChart(updatedConfig.data);
                console.log('Chart updated successfully:', chartData.id);
            } else {
                 console.log('Chart editing cancelled.');
            }
             // Reset dialog options to default after closing? Or keep last state?
             // For now, let it keep the last state.
        });
    }

    /**
     * Delete a chart
     * @param {Object} chartData - The chart data object
     */
    function deleteChart(chartData) {
        // Remove chart element from DOM
        if (chartData.element && chartData.element.parentNode) {
            chartData.element.parentNode.removeChild(chartData.element);
        }

        // Clean up chart instance
        if (chartData.chartInstance) {
            if (chartData.chartInstance.destroy && typeof chartData.chartInstance.destroy === 'function') {
                try { chartData.chartInstance.destroy(); } catch (e) { console.warn('Error destroying chart instance:', e); }
            } else if (chartData.chartInstance.dispose && typeof chartData.chartInstance.dispose === 'function') {
                try { chartData.chartInstance.dispose(); } catch (e) { console.warn('Error disposing chart instance:', e); }
            }
        }

        // Remove chart from charts array
        const index = _charts.findIndex(chart => chart.id === chartData.id);
        if (index !== -1) {
            _charts.splice(index, 1);
            console.log('Chart deleted:', chartData.id);
        }
    }

    // Return public API
    return {
        init,
        setCurrentSheet,
        showChartDialog: showEnhancedChartDialog,
        createChart: createChartFromConfig,
        getCharts: () => _charts,
        getChartLibrary: () => _chartLibrary
    };
})();

// Export the module
export default ChartManager;
