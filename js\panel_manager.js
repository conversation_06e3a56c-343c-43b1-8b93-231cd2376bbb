// js/panel_manager.js

class PanelManager {
    constructor() {
        this.activePanel = null;
        this.activeModal = null;
    }

    openPanel(config) {
        const { type, title, content } = config;

        // Close any existing panel
        if (this.activePanel) {
            this.closePanel();
        }

        const panel = document.createElement('div');
        panel.className = 'panel';
        panel.dataset.type = type;

        const header = document.createElement('div');
        header.className = 'panel-header';
        header.innerHTML = `
            <h3>${title}</h3>
            <button class="close-button">
                <span class="material-icons">close</span>
            </button>
        `;

        const body = document.createElement('div');
        body.className = 'panel-body';

        if (typeof content === 'string') {
            body.innerHTML = content;
        } else if (content instanceof HTMLElement) {
            body.appendChild(content);
        }

        panel.appendChild(header);
        panel.appendChild(body);
        document.body.appendChild(panel);

        // Add close button functionality
        header.querySelector('.close-button').addEventListener('click', () => {
            this.closePanel();
        });

        // Make panel draggable
        this.makeDraggable(panel, header);

        this.activePanel = panel;

        // Initialize panel content based on type
        this.initializePanelContent(type, body);

        return panel;
    }

    closePanel() {
        if (this.activePanel) {
            document.body.removeChild(this.activePanel);
            this.activePanel = null;
        }
    }

    openModal(config) {
        const { type, title, content, buttons = [] } = config;

        // Close any existing modal
        if (this.activeModal) {
            this.closeModal();
        }

        const overlay = document.createElement('div');
        overlay.className = 'modal-overlay';
        overlay.style.background = 'none';
        overlay.style.backgroundColor = 'transparent';

        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.dataset.type = type;

        const header = document.createElement('div');
        header.className = 'modal-header';
        header.innerHTML = `
            <h3>${title}</h3>
            <button class="close-button">
                <span class="material-icons">close</span>
            </button>
        `;

        const body = document.createElement('div');
        body.className = 'modal-body';

        if (typeof content === 'string') {
            body.innerHTML = content;
        } else if (content instanceof HTMLElement) {
            body.appendChild(content);
        }

        const footer = document.createElement('div');
        footer.className = 'modal-footer';

        buttons.forEach(button => {
            const btn = document.createElement('button');
            btn.className = `btn ${button.class || ''}`;
            btn.textContent = button.text;
            btn.addEventListener('click', () => {
                if (button.action) {
                    button.action();
                }
                if (button.closeOnClick !== false) {
                    this.closeModal();
                }
            });
            footer.appendChild(btn);
        });

        modal.appendChild(header);
        modal.appendChild(body);
        modal.appendChild(footer);
        overlay.appendChild(modal);

        // Add close button functionality
        header.querySelector('.close-button').addEventListener('click', () => {
            this.closeModal();
        });

        // Close on overlay click
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                this.closeModal();
            }
        });

        this.modalContainer.appendChild(overlay);
        this.activeModal = overlay;

        // Animate modal entry
        requestAnimationFrame(() => {
            modal.style.opacity = '1';
            modal.style.transform = 'translateY(0)';
        });
    }

    closeModal() {
        if (this.activeModal) {
            const modal = this.activeModal.querySelector('.modal');
            modal.style.opacity = '0';
            modal.style.transform = 'translateY(-20px)';

            setTimeout(() => {
                this.modalContainer.removeChild(this.activeModal);
                this.activeModal = null;
            }, 300);
        }
    }

    // Utility method to create panel content
    createPanelContent(type) {
        const content = document.createElement('div');

        switch (type) {
            case 'script-editor':
                content.innerHTML = `
                    <div class="editor-container">
                        <textarea class="code-editor" rows="20"></textarea>
                        <div class="editor-buttons">
                            <button class="btn btn-primary">Run Script</button>
                            <button class="btn">Save Script</button>
                        </div>
                    </div>
                `;
                break;

            case 'chart-builder':
                content.innerHTML = `
                    <div class="chart-builder">
                        <div class="chart-options">
                            <select class="chart-type">
                                <option value="line">Line Chart</option>
                                <option value="bar">Bar Chart</option>
                                <option value="pie">Pie Chart</option>
                                <option value="scatter">Scatter Plot</option>
                            </select>
                            <button class="btn btn-primary">Generate Chart</button>
                        </div>
                        <div class="chart-preview"></div>
                    </div>
                `;
                break;

            case 'data-validation':
                content.innerHTML = `
                    <div class="validation-tool">
                        <div class="validation-rules">
                            <button class="btn">Add Rule</button>
                            <div class="rule-list"></div>
                        </div>
                        <div class="validation-preview">
                            <h4>Preview</h4>
                            <div class="preview-content"></div>
                        </div>
                    </div>
                `;
                break;

            case 'connector-config':
                content.innerHTML = `
                    <div class="connector-settings">
                        <div class="form-group">
                            <label>API Key</label>
                            <input type="password" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>Endpoint URL</label>
                            <input type="text" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>Authentication Type</label>
                            <select class="form-control">
                                <option value="basic">Basic Auth</option>
                                <option value="oauth">OAuth 2.0</option>
                                <option value="api-key">API Key</option>
                            </select>
                        </div>
                        <button class="btn btn-primary">Test Connection</button>
                    </div>
                `;
                break;

            default:
                content.innerHTML = '<p>Content not available</p>';
        }

        return content;
    }
}

// Export a singleton instance
const panelManager = new PanelManager();
export default panelManager;
