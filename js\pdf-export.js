/**
 * Enhanced PDF Export Utility
 * Uses pdf-lib.min.js for better quality and performance
 */

class PdfExport {
    /**
     * Create a PDF from HTML content
     * @param {HTMLElement} content - The HTML element to convert to PDF
     * @param {string} filename - The filename for the PDF
     * @param {Object} options - Options for PDF generation
     * @returns {Promise<boolean>} - True if successful
     */
    static async exportToPdf(content, filename = 'export.pdf', options = {}) {
        try {
            console.log('Starting PDF export...');
            
            // Default options
            const defaultOptions = {
                scale: 2, // Higher scale for better quality
                useCORS: true, // Allow loading images from other domains
                allowTaint: true, // Allow tainted canvas
                logging: false, // Disable logging
                letterRendering: true, // Better text rendering
                imageTimeout: 0, // No timeout for images
                backgroundColor: '#ffffff', // White background
                pageSize: 'A4', // Default page size
                margin: {
                    top: 40,
                    right: 40,
                    bottom: 40,
                    left: 40
                }
            };
            
            // Merge options
            const pdfOptions = { ...defaultOptions, ...options };
            
            // Show loading indicator
            const loadingIndicator = document.createElement('div');
            loadingIndicator.style.position = 'fixed';
            loadingIndicator.style.top = '50%';
            loadingIndicator.style.left = '50%';
            loadingIndicator.style.transform = 'translate(-50%, -50%)';
            loadingIndicator.style.padding = '20px';
            loadingIndicator.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
            loadingIndicator.style.color = 'white';
            loadingIndicator.style.borderRadius = '5px';
            loadingIndicator.style.zIndex = '9999';
            loadingIndicator.textContent = 'Generating PDF...';
            document.body.appendChild(loadingIndicator);
            
            // Allow UI to update before starting heavy processing
            await new Promise(resolve => setTimeout(resolve, 100));
            
            console.log('Converting HTML to canvas...');
            
            // Convert HTML to canvas with higher resolution
            const canvas = await html2canvas(content, {
                scale: pdfOptions.scale,
                useCORS: pdfOptions.useCORS,
                allowTaint: pdfOptions.allowTaint,
                logging: pdfOptions.logging,
                letterRendering: pdfOptions.letterRendering,
                imageTimeout: pdfOptions.imageTimeout,
                backgroundColor: pdfOptions.backgroundColor
            });
            
            console.log('Canvas created, converting to image data...');
            
            // Get image data with high quality
            const imgData = canvas.toDataURL('image/png', 1.0);
            
            console.log('Creating PDF document...');
            
            // Create PDF document
            const pdfDoc = await PDFLib.PDFDocument.create();
            
            // Set page size
            let pageWidth = 595.28; // A4 width in points
            let pageHeight = 841.89; // A4 height in points
            
            if (pdfOptions.pageSize === 'Letter') {
                pageWidth = 612; // Letter width in points
                pageHeight = 792; // Letter height in points
            } else if (pdfOptions.pageSize === 'Legal') {
                pageWidth = 612; // Legal width in points
                pageHeight = 1008; // Legal height in points
            } else if (pdfOptions.pageSize === 'A3') {
                pageWidth = 841.89; // A3 width in points
                pageHeight = 1190.55; // A3 height in points
            }
            
            // Calculate content dimensions
            const contentWidth = pageWidth - pdfOptions.margin.left - pdfOptions.margin.right;
            const contentHeight = pageHeight - pdfOptions.margin.top - pdfOptions.margin.bottom;
            
            // Calculate scale to fit content
            const contentAspectRatio = canvas.width / canvas.height;
            const pageAspectRatio = contentWidth / contentHeight;
            
            let scaleFactor;
            if (contentAspectRatio > pageAspectRatio) {
                // Content is wider than page
                scaleFactor = contentWidth / canvas.width;
            } else {
                // Content is taller than page
                scaleFactor = contentHeight / canvas.height;
            }
            
            // Calculate dimensions for the image on the page
            const imgWidth = canvas.width * scaleFactor;
            const imgHeight = canvas.height * scaleFactor;
            
            console.log('Embedding image in PDF...');
            
            // Embed the image
            const img = await pdfDoc.embedPng(imgData);
            
            // Add page and draw image
            const page = pdfDoc.addPage([pageWidth, pageHeight]);
            
            page.drawImage(img, {
                x: pdfOptions.margin.left,
                y: pageHeight - pdfOptions.margin.top - imgHeight,
                width: imgWidth,
                height: imgHeight
            });
            
            console.log('Saving PDF...');
            
            // Save the PDF
            const pdfBytes = await pdfDoc.save();
            const blob = new Blob([pdfBytes], { type: 'application/pdf' });
            const url = URL.createObjectURL(blob);
            
            // Create download link
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.click();
            
            // Clean up
            URL.revokeObjectURL(url);
            document.body.removeChild(loadingIndicator);
            
            console.log('PDF export completed successfully');
            return true;
        } catch (error) {
            console.error('Error exporting to PDF:', error);
            
            // Remove loading indicator if it exists
            const loadingIndicator = document.querySelector('div[style*="Generating PDF"]');
            if (loadingIndicator) {
                document.body.removeChild(loadingIndicator);
            }
            
            throw error;
        }
    }
}

// Export the class
export default PdfExport;
