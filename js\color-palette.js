// Color Palette functionality
class ColorPalette {
    constructor() {
        // Famous colors with their hex values
        this.famousColors = [
            { name: 'Black', hex: '#000000' },
            { name: '<PERSON>', hex: '#FFFFFF' },
            { name: 'Red', hex: '#FF0000' },
            { name: '<PERSON>', hex: '#008000' },
            { name: 'Blue', hex: '#0000FF' },
            { name: 'Yellow', hex: '#FFFF00' },
            { name: 'Purple', hex: '#800080' },
            { name: 'Orange', hex: '#FFA500' },
            { name: 'Pink', hex: '#FFC0CB' },
            { name: '<PERSON><PERSON>', hex: '#00FFFF' },
            { name: '<PERSON><PERSON>', hex: '#00FF00' },
            { name: '<PERSON>', hex: '#A52A2A' }
        ];

        // Wait for DOM to be fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializePalettes());
        } else {
            // DOM already loaded, initialize immediately
            this.initializePalettes();
        }
    }

    initializePalettes() {
        console.log('Initializing color palettes');
        // Create color palette containers
        this.createColorPalette('fontColorPicker', 'Font Color');
        this.createColorPalette('bgColorPicker', 'Fill Color');

        // Add event listeners to show/hide palettes
        this.addEventListeners();
    }

    createColorPalette(pickerId, title) {
        console.log(`Creating color palette for ${pickerId}`);
        const picker = document.getElementById(pickerId);
        if (!picker) {
            console.error(`Color picker with ID ${pickerId} not found`);
            return;
        }

        // Get the label for this picker
        const label = document.querySelector(`label[for="${pickerId}"]`);
        if (!label) {
            console.error(`Label for color picker ${pickerId} not found`);
            return;
        }

        // Wrap the color picker in a container
        const wrapper = document.createElement('div');
        wrapper.className = 'color-picker-wrapper';
        picker.parentNode.insertBefore(wrapper, picker);
        wrapper.appendChild(picker);

        // Remove any existing color indicators
        const existingIndicator = label.querySelector('.color-indicator');
        if (existingIndicator) {
            label.removeChild(existingIndicator);
        }

        // Check if a palette container already exists and remove it
        const existingPalette = document.getElementById(`${pickerId}-palette`);
        if (existingPalette) {
            existingPalette.parentNode.removeChild(existingPalette);
        }

        // Create the palette container and append it to the document body
        // This ensures it can overlay on top of all elements
        const paletteContainer = document.createElement('div');
        paletteContainer.className = 'color-palette-container';
        paletteContainer.id = `${pickerId}-palette`;
        document.body.appendChild(paletteContainer);

        // Add title
        const paletteTitle = document.createElement('div');
        paletteTitle.className = 'color-palette-title';
        paletteTitle.textContent = title;
        paletteContainer.appendChild(paletteTitle);

        // Create color grid
        const colorGrid = document.createElement('div');
        colorGrid.className = 'color-palette-grid';
        paletteContainer.appendChild(colorGrid);

        // Add color swatches
        this.famousColors.forEach(color => {
            const swatch = document.createElement('div');
            swatch.className = 'color-swatch';
            swatch.style.backgroundColor = color.hex;
            swatch.title = color.name;
            swatch.dataset.color = color.hex;
            colorGrid.appendChild(swatch);

            // Add click event to select color
            swatch.addEventListener('click', () => {
                picker.value = color.hex;
                // Trigger the input event to apply the color
                const event = new Event('input', { bubbles: true });
                picker.dispatchEvent(event);
                paletteContainer.style.display = 'none';
            });
        });

        // Add footer with buttons
        const footer = document.createElement('div');
        footer.className = 'color-palette-footer';
        paletteContainer.appendChild(footer);

        // Add custom color button
        const customBtn = document.createElement('button');
        customBtn.textContent = 'Custom Color';
        customBtn.addEventListener('click', () => {
            picker.click();
            paletteContainer.style.display = 'none';
        });
        footer.appendChild(customBtn);

        // Add close button
        const closeBtn = document.createElement('button');
        closeBtn.textContent = 'Close';
        closeBtn.addEventListener('click', () => {
            paletteContainer.style.display = 'none';
        });
        footer.appendChild(closeBtn);
    }

    addEventListeners() {
        console.log('Adding event listeners for color palettes');
        // Show palette when clicking on the color picker label
        document.querySelectorAll('.toolbar-group[aria-label="Colors"] label').forEach(label => {
            const pickerId = label.getAttribute('for');
            const palette = document.getElementById(`${pickerId}-palette`);

            if (palette) {
                console.log(`Adding click listener for ${pickerId} palette`);
                label.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation(); // Prevent event from bubbling up

                    // Toggle this palette
                    if (palette.style.display === 'block') {
                        palette.style.display = 'none';
                    } else {
                        // Hide all other palettes
                        document.querySelectorAll('.color-palette-container').forEach(p => {
                            p.style.display = 'none';
                        });

                        // Position the palette below the label
                        const labelRect = label.getBoundingClientRect();
                        palette.style.top = (labelRect.bottom + 5) + 'px';
                        palette.style.left = labelRect.left + 'px';

                        // Show this palette
                        palette.style.display = 'block';
                    }
                });
            }
        });

        // Close palettes when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.color-picker-wrapper') &&
                !e.target.closest('.color-palette-container')) {
                document.querySelectorAll('.color-palette-container').forEach(palette => {
                    palette.style.display = 'none';
                });
            }
        });
    }
}

// Function to initialize the color palette
function initColorPalette() {
    // Remove any existing color indicators
    document.querySelectorAll('.color-indicator').forEach(indicator => {
        if (indicator && indicator.parentNode) {
            indicator.parentNode.removeChild(indicator);
        }
    });

    // Remove any existing palette containers
    document.querySelectorAll('.color-palette-container').forEach(palette => {
        if (palette && palette.parentNode) {
            palette.parentNode.removeChild(palette);
        }
    });

    // Create a new color palette instance
    console.log('Initializing color palette');
    window.colorPalette = new ColorPalette();

    // Final cleanup - ensure all color indicators are removed
    setTimeout(() => {
        document.querySelectorAll('.color-indicator').forEach(indicator => {
            if (indicator && indicator.parentNode) {
                indicator.parentNode.removeChild(indicator);
            }
        });
    }, 100);
}

// Initialize the color palette when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing color palette');
    initColorPalette();
});

// Also initialize immediately if the DOM is already loaded
if (document.readyState === 'complete' || document.readyState === 'interactive') {
    console.log('DOM already loaded, initializing color palette immediately');
    initColorPalette();
}

// Immediate cleanup function to remove any existing color indicators
(function cleanupColorIndicators() {
    console.log('Immediate cleanup of color indicators');
    document.querySelectorAll('.color-indicator').forEach(indicator => {
        if (indicator && indicator.parentNode) {
            indicator.parentNode.removeChild(indicator);
        }
    });
})();

// Export the ColorPalette class
export default ColorPalette;
