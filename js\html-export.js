/**
 * HTML Export Utility
 * Exports spreadsheet data as an HTML web page
 */

class HtmlExport {
    /**
     * Export spreadsheet data as an HTML web page
     * @param {Object} workbook - The workbook to export
     * @param {Object} sheet - The sheet to export
     * @param {string} filename - The filename for the HTML file
     * @returns {Promise<boolean>} - True if successful
     */
    static async exportToHtml(workbook, sheet, filename = 'export.html') {
        try {
            console.log('Starting HTML export...');

            // Show loading indicator
            const loadingIndicator = document.createElement('div');
            loadingIndicator.style.position = 'fixed';
            loadingIndicator.style.top = '50%';
            loadingIndicator.style.left = '50%';
            loadingIndicator.style.transform = 'translate(-50%, -50%)';
            loadingIndicator.style.padding = '20px';
            loadingIndicator.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
            loadingIndicator.style.color = 'white';
            loadingIndicator.style.borderRadius = '5px';
            loadingIndicator.style.zIndex = '9999';
            loadingIndicator.textContent = 'Creating HTML export...';
            document.body.appendChild(loadingIndicator);

            // Allow UI to update before starting processing
            await new Promise(resolve => setTimeout(resolve, 100));

            // Get workbook metadata
            const title = workbook.title || 'Spreadsheet';
            const author = workbook.author || 'Unknown';
            const subject = workbook.subject || '';
            const keywords = workbook.keywords || '';

            // Get sheet data range
            const range = sheet.usedRange();
            const startRow = range.startCell().rowNumber();
            const startCol = range.startCell().columnNumber();
            const endRow = range.endCell().rowNumber();
            const endCol = range.endCell().columnNumber();

            // Create HTML content
            let htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <meta name="author" content="${author}">
    <meta name="description" content="${subject}">
    <meta name="keywords" content="${keywords}">
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #1a73e8;
            text-align: center;
            margin-bottom: 30px;
        }
        .metadata {
            margin-bottom: 20px;
            color: #666;
            font-size: 0.9em;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            font-size: 0.8em;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>${title}</h1>
    <div class="metadata">
        <p><strong>Author:</strong> ${author}</p>
        <p><strong>Subject:</strong> ${subject}</p>
        <p><strong>Keywords:</strong> ${keywords}</p>
        <p><strong>Generated:</strong> ${new Date().toLocaleString()}</p>
    </div>
    <table>
        <thead>
            <tr>`;

            // Add column headers (A, B, C, etc.)
            for (let c = startCol; c <= endCol; c++) {
                const colLetter = String.fromCharCode(64 + c);
                htmlContent += `<th>${colLetter}</th>`;
            }

            htmlContent += `</tr>
        </thead>
        <tbody>`;

            // Add data rows
            for (let r = startRow; r <= endRow; r++) {
                htmlContent += `<tr>`;

                for (let c = startCol; c <= endCol; c++) {
                    const cell = sheet.cell(r, c);
                    const value = cell.value() || '';

                    // Start cell with styles
                    let cellStyles = '';

                    if (cell.style('bold')) {
                        cellStyles += 'font-weight: bold; ';
                    }
                    if (cell.style('italic')) {
                        cellStyles += 'font-style: italic; ';
                    }
                    if (cell.style('underline')) {
                        cellStyles += 'text-decoration: underline; ';
                    }
                    if (cell.style('fontColor')) {
                        cellStyles += `color: #${cell.style('fontColor')}; `;
                    }
                    if (cell.style('fill')) {
                        cellStyles += `background-color: #${cell.style('fill')}; `;
                    }
                    if (cell.style('horizontalAlignment')) {
                        cellStyles += `text-align: ${cell.style('horizontalAlignment')}; `;
                    }

                    htmlContent += `<td${cellStyles ? ` style="${cellStyles}"` : ''}>${value}</td>`;
                }

                htmlContent += `</tr>`;
            }

            // Complete the HTML document
            htmlContent += `
        </tbody>
    </table>
    <div class="footer">
        <p>Generated by Enven Bridge Sheet</p>
    </div>
</body>
</html>`;

            // Create a blob with the HTML content
            const blob = new Blob([htmlContent], { type: 'text/html' });

            // Create download link
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.click();

            // Clean up
            URL.revokeObjectURL(url);

            // Remove loading indicator
            document.body.removeChild(loadingIndicator);

            console.log('HTML export completed successfully');
            return true;
        } catch (error) {
            console.error('Error exporting to HTML:', error);

            // Remove loading indicator if it exists
            const loadingIndicator = document.querySelector('div[style*="Creating HTML export"]');
            if (loadingIndicator) {
                document.body.removeChild(loadingIndicator);
            }

            throw error;
        }
    }
}

// Export the class
export default HtmlExport;
