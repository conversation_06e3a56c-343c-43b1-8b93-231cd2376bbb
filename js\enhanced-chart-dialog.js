/**
 * Enhanced Chart Dialog Component
 */
import {
    CHART_TYPES,
    CHART_TYPE_GROUPS,
    CHART_TYPE_ICONS,
    CHART_TYPE_DESCRIPTIONS,
    COLOR_PALETTES,
    CHART_TEMPLATES,
    CHART_THEMES,
    DEFAULT_CHART_OPTIONS
} from './chart-constants.js';

import {
    extractDataFromRange,
    formatRangeAddress,
    createApex<PERSON><PERSON>,
    create<PERSON><PERSON>,
    createBasic<PERSON><PERSON>
} from './chart-utils.js';
import { generateChartStyle } from './chart-styler.js';
import { processChartData, suggestChartTypes } from './chart-data-processor.js';
import { createChartModal } from './standardized-modal.js';

/**
 * Enhanced Chart Dialog Component
 */
export default class EnhancedChartDialog {
    constructor(chartManager) {
        this.modal = null;
        this.selectedRange = null;
        this.extractedData = null;
        this.processedData = null;
        this.previewChartInstance = null;
        this.activeTab = 'type';
        this.callback = null;
        this.chartManager = chartManager; // Reference to ChartManager
        this._chartLibrary = chartManager ? chartManager.getChartLibrary() : 'apexcharts'; // Get library from manager

        // Initialize chart options with defaults
        this.chartOptions = { ...DEFAULT_CHART_OPTIONS };
        this.chartOptions.colors = COLOR_PALETTES.EXCEL; // Start with Excel palette
        this.chartOptions.title = 'Chart Title';
        this.chartOptions.type = 'column'; // Default type
    }

    /**
     * Show the enhanced chart dialog
     * @param {Object} selectedRange - The selected cell range
     * @param {Function} callback - Callback function to call with final chart options
     */
    show(selectedRange, callback) {
        this.selectedRange = selectedRange;
        this.callback = callback;

        // Extract data from the selected range
        if (selectedRange) {
            this.extractedData = extractDataFromRange(selectedRange);

            if (!this.extractedData || !this.extractedData.isValidData) {
                alert('Invalid or insufficient data selected for chart creation.');
                console.error("Chart data extraction failed:", this.extractedData);
                return;
            }

            // Suggest chart types based on data
            const suggestedTypes = suggestChartTypes(this.extractedData);
            if (suggestedTypes && suggestedTypes.length > 0) {
                this.chartOptions.type = suggestedTypes[0]; // Default to the first suggestion
            }

            // Process data for the selected chart type
            this.processAndSetData(this.chartOptions.type);
        } else {
             alert('No data range selected. Please select cells first.');
             return;
        }

        // Create the modal dialog if it doesn't exist
        if (!this.modal) {
            this.createModal();
        }

        // Add the modal to the document
        if (!document.body.contains(this.modal)) {
            document.body.appendChild(this.modal);
        }

        // Make modal visible
        this.modal.style.display = 'flex';

        // Add modal-open class to body to prevent scrolling
        document.body.classList.add('modal-open');

        // Initialize the dialog content and event listeners
        this.initializeDialog();
    }

    /**
     * Process data for the current chart type
     * @param {string} chartType - The chart type
     */
    processAndSetData(chartType) {
        if (this.extractedData) {
            this.processedData = processChartData(this.extractedData, chartType);
            // Update chart options based on processed data if necessary (e.g., axis titles)
            if (!this.chartOptions.xAxisTitle && this.processedData.categories && this.processedData.categories.length > 0) {
                // Basic logic, can be improved
                // this.chartOptions.xAxisTitle = "Categories";
            }
            if (!this.chartOptions.yAxisTitle && this.processedData.series && this.processedData.series.length > 0) {
                 // this.chartOptions.yAxisTitle = this.processedData.series[0].name || "Values";
            }
        }
    }

    /**
     * Create the modal dialog HTML structure
     */
    createModal() {
        // Create chart modal content
        const chartContent = `
            <div class="chart-dialog-container">
                <div class="chart-dialog-tabs">
                    <div class="chart-dialog-tab active" data-tab="type">
                        <span class="material-icons">bar_chart</span>
                        Chart Type
                    </div>
                    <div class="chart-dialog-tab" data-tab="data">
                        <span class="material-icons">table_chart</span>
                        Data
                    </div>
                    <div class="chart-dialog-tab" data-tab="style">
                        <span class="material-icons">palette</span>
                        Style
                    </div>
                    <div class="chart-dialog-tab" data-tab="customize">
                        <span class="material-icons">tune</span>
                        Customize
                    </div>
                </div>
                <div class="chart-dialog-content">
                    <div class="chart-dialog-sidebar">
                        <!-- Sidebar content loaded dynamically -->
                    </div>
                    <div class="chart-dialog-main">
                        <!-- Main content (tabs) loaded dynamically -->
                        <div id="tabContentContainer"></div>

                        <!-- Chart Preview -->
                        <div class="chart-preview-container">
                            <div class="chart-preview-header">
                                <div class="chart-preview-title">Preview</div>
                                <button id="refreshPreviewBtn" class="excel-btn excel-btn-secondary excel-btn-small" title="Refresh Preview">
                                    <span class="material-icons">refresh</span>
                                </button>
                            </div>
                            <div class="chart-preview-content" id="chartPreview">
                                <!-- Preview chart rendered here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Create buttons for the modal
        const buttons = [
            {
                text: 'Cancel',
                primary: false,
                id: 'excel-modal-cancel',
                icon: 'cancel',
                closeModal: true
            },
            {
                text: 'Create Chart',
                primary: true,
                id: 'createChartBtn',
                icon: 'bar_chart',
                closeModal: false
            }
        ];

        // Create the standardized chart modal
        const modalInstance = createChartModal({
            title: 'Create Chart',
            content: chartContent,
            size: 'full',
            buttons: buttons
        });

        // Get the modal element
        this.modal = modalInstance.getOverlay();

        // Add chart-dialog class for specific styling
        this.modal.classList.add('chart-dialog');
        this.modal.classList.add('excel-modal');

        // Add ai-style-change-2 class for enhanced styling
        this.modal.classList.add('ai-style-change-2');

        // Make sure the modal is properly centered and fullscreen
        this.modal.style.display = 'flex';
        this.modal.style.justifyContent = 'center';
        this.modal.style.alignItems = 'center';
        this.modal.style.width = '100vw';
        this.modal.style.height = '100vh';
        this.modal.style.position = 'fixed';
        this.modal.style.top = '0';
        this.modal.style.left = '0';
        this.modal.style.right = '0';
        this.modal.style.bottom = '0';
        this.modal.style.background = 'rgba(0, 0, 0, 0.5)';
        this.modal.style.zIndex = '9999';
        this.modal.style.overflow = 'hidden';
        this.modal.style.margin = '0';
        this.modal.style.padding = '0';
    }

    /**
     * Initialize the dialog elements and event listeners
     */
    initializeDialog() {
        const closeBtn = this.modal.querySelector('.excel-modal-close');
        const cancelBtn = this.modal.querySelector('.excel-modal-cancel');
        const createBtn = this.modal.querySelector('#createChartBtn');
        const tabs = this.modal.querySelectorAll('.chart-dialog-tab');
        const refreshPreviewBtn = this.modal.querySelector('#refreshPreviewBtn');

        // Close/Cancel listeners
        closeBtn.addEventListener('click', () => this.close());
        cancelBtn.addEventListener('click', () => this.close());

        // Create chart listener
        createBtn.addEventListener('click', () => this.createChart());

        // Refresh preview listener
        refreshPreviewBtn.addEventListener('click', () => this.updatePreview());

        // Tab switching listeners
        tabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                const clickedTab = e.currentTarget;
                tabs.forEach(t => t.classList.remove('active'));
                clickedTab.classList.add('active');
                this.activeTab = clickedTab.dataset.tab;
                this.loadSidebarContent(this.activeTab);
                this.loadTabContent(this.activeTab);
            });
        });

        // Load initial tab and sidebar content
        this.loadSidebarContent(this.activeTab);
        this.loadTabContent(this.activeTab);

        // Initialize preview
        this.updatePreview();
    }

    /**
     * Close the modal dialog
     */
    close() {
        if (this.modal && document.body.contains(this.modal)) {
            // Add fade-out animation
            this.modal.style.animation = 'standardModalFadeOut 0.3s ease-out forwards';

            // Remove modal-open class from body
            document.body.classList.remove('modal-open');

            // Remove after animation completes
            setTimeout(() => {
                if (this.modal && document.body.contains(this.modal)) {
                    document.body.removeChild(this.modal);
                }
                this.modal = null; // Allow modal to be recreated next time
            }, 300);
        }

        // Clean up preview chart instance
        if (this.previewChartInstance && this.previewChartInstance.destroy) {
            try {
                this.previewChartInstance.destroy();
            } catch(e) {
                console.warn("Error destroying preview chart", e);
            }
            this.previewChartInstance = null;
        }
    }

    /**
     * Finalize chart options and call the callback
     */
    createChart() {
        console.log('Final Chart Options:', this.chartOptions);
        console.log('Final Processed Data:', this.processedData);

        // Combine options and data
        const finalConfig = {
            ...this.chartOptions,
            data: this.processedData,
            sourceRange: this.selectedRange // Include source range for refresh functionality
        };

        if (this.callback) {
            this.callback(finalConfig);
        }
        this.close();
    }

    /**
     * Load sidebar content based on the active tab
     * @param {string} tabName - The active tab name
     */
    loadSidebarContent(tabName) {
        const sidebar = this.modal.querySelector('.chart-dialog-sidebar');
        sidebar.innerHTML = ''; // Clear previous content

        // Common controls (Title, Theme, Colors) - always visible or adjustable based on tab
        sidebar.innerHTML += `
            <div class="chart-settings-section">
                <div class="chart-settings-title">
                    <span class="material-icons">title</span> Chart Title
                </div>
                <input type="text" id="chartTitle" class="form-control" value="${this.chartOptions.title}">
            </div>
        `;

        // Tab-specific sidebar controls
        switch (tabName) {
            case 'style':
                sidebar.innerHTML += `
                    <div class="chart-settings-section">
                        <div class="chart-settings-title">
                            <span class="material-icons">format_paint</span> Chart Style
                        </div>
                        <div class="form-group">
                            <label for="chartTheme">Theme:</label>
                            <select id="chartTheme" class="form-control">
                                ${Object.entries(CHART_THEMES).map(([key, value]) =>
                                    `<option value="${value}" ${this.chartOptions.theme === value ? 'selected' : ''}>${key.charAt(0).toUpperCase() + key.slice(1).toLowerCase()}</option>`
                                ).join('')}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="colorPalette">Color Palette:</label>
                            <select id="colorPalette" class="form-control">
                                ${Object.keys(COLOR_PALETTES).map(key =>
                                    `<option value="${key}" ${JSON.stringify(this.chartOptions.colors) === JSON.stringify(COLOR_PALETTES[key]) ? 'selected' : ''}>${key.charAt(0).toUpperCase() + key.slice(1).toLowerCase()}</option>`
                                ).join('')}
                            </select>
                            <div class="color-palette-container" id="palettePreviewContainer">
                                <!-- Palette preview generated dynamically -->
                            </div>
                        </div>
                    </div>
                `;
                this.updatePalettePreview(); // Update palette preview
                break;
            case 'customize':
                 sidebar.innerHTML += `
                    <div class="chart-settings-section">
                        <div class="chart-settings-title">
                            <span class="material-icons">settings</span> General Options
                        </div>
                         <div class="switch-control">
                            <label class="switch">
                                <input type="checkbox" id="animationsToggle" ${this.chartOptions.animations ? 'checked' : ''}>
                                <span class="slider"></span>
                            </label>
                            <span class="switch-label">Animations</span>
                        </div>
                        <div class="switch-control">
                            <label class="switch">
                                <input type="checkbox" id="legendToggle" ${this.chartOptions.legend ? 'checked' : ''}>
                                <span class="slider"></span>
                            </label>
                            <span class="switch-label">Show Legend</span>
                        </div>
                        <div class="switch-control">
                            <label class="switch">
                                <input type="checkbox" id="dataLabelsToggle" ${this.chartOptions.dataLabels ? 'checked' : ''}>
                                <span class="slider"></span>
                            </label>
                            <span class="switch-label">Data Labels</span>
                        </div>
                         <div class="switch-control">
                            <label class="switch">
                                <input type="checkbox" id="gridToggle" ${this.chartOptions.grid ? 'checked' : ''}>
                                <span class="slider"></span>
                            </label>
                            <span class="switch-label">Show Grid</span>
                        </div>
                    </div>
                    <div class="chart-settings-section">
                         <div class="chart-settings-title">
                            <span class="material-icons">tune</span> Axis Titles
                        </div>
                        <div class="form-group">
                            <label for="xAxisTitle">X-Axis Title:</label>
                            <input type="text" id="xAxisTitle" class="form-control" value="${this.chartOptions.xAxisTitle || ''}">
                        </div>
                        <div class="form-group">
                            <label for="yAxisTitle">Y-Axis Title:</label>
                            <input type="text" id="yAxisTitle" class="form-control" value="${this.chartOptions.yAxisTitle || ''}">
                        </div>
                    </div>
                 `;
                break;
            // Add cases for 'type' and 'data' if they need sidebar controls
            default:
                 sidebar.innerHTML += `
                    <div class="chart-settings-section">
                         <p>Select options from the main panel.</p>
                    </div>
                 `;
                break;
        }

        // Re-attach common listeners
        this.attachSidebarListeners();
    }

     /**
     * Load main content based on the active tab
     * @param {string} tabName - The active tab name
     */
    loadTabContent(tabName) {
        const tabContentContainer = this.modal.querySelector('#tabContentContainer');
        tabContentContainer.innerHTML = ''; // Clear previous content

        switch (tabName) {
            case 'type':
                this.loadChartTypeTab(tabContentContainer);
                break;
            case 'data':
                this.loadDataTab(tabContentContainer);
                break;
            case 'style':
                this.loadStyleTab(tabContentContainer);
                break;
             case 'customize':
                this.loadCustomizeTab(tabContentContainer);
                break;
        }
         // Re-attach listeners specific to the loaded tab content if necessary
         this.attachTabContentListeners(tabName);
    }

    // --- Tab Content Loaders ---

    loadChartTypeTab(container) {
        container.innerHTML = ''; // Clear previous content

        // Introduction text for chart selection
        const introSection = document.createElement('div');
        introSection.className = 'chart-intro-section';
        introSection.innerHTML = `
            <h3>Select the Best Chart Type for Your Data</h3>
            <p>Choose a chart type that best represents your data and communication goals. Different chart types are better suited for different types of data and analysis.</p>
        `;
        container.appendChild(introSection);

        // Group chart types
        Object.entries(CHART_TYPE_GROUPS).forEach(([groupName, types]) => {
            const groupContainer = document.createElement('div');
            groupContainer.className = 'chart-type-group';
            groupContainer.innerHTML = `
                <h3 class="chart-type-group-title">
                    <span class="material-icons">${this.getGroupIcon(groupName)}</span>
                    ${this.formatGroupName(groupName)}
                </h3>
                <p class="chart-type-group-description">${this.getGroupDescription(groupName)}</p>
            `;

            const grid = document.createElement('div');
            grid.className = 'chart-type-grid';

            types.forEach(type => {
                // Use the key from CHART_TYPES for consistency
                const typeKey = Object.keys(CHART_TYPES).find(key => CHART_TYPES[key] === type);
                if (!typeKey) return; // Skip if type constant doesn't exist

                const item = document.createElement('div');
                item.className = 'chart-type-item';
                item.dataset.type = type;
                if (type === this.chartOptions.type) {
                    item.classList.add('selected');
                }
                item.innerHTML = `
                    <span class="material-icons chart-type-icon">${CHART_TYPE_ICONS[type] || 'insert_chart'}</span>
                    <span class="chart-type-label">${type.charAt(0).toUpperCase() + type.slice(1)}</span>
                    <p class="chart-type-description">${CHART_TYPE_DESCRIPTIONS[type] || ''}</p>
                    ${type === this.chartOptions.type ? '<span class="material-icons chart-type-selected-icon">check_circle</span>' : ''}
                `;
                item.addEventListener('click', () => this.selectChartType(type));
                grid.appendChild(item);
            });

            groupContainer.appendChild(grid);
            container.appendChild(groupContainer);
        });
    }

    loadDataTab(container) {
         container.innerHTML = `
            <div class="chart-data-section">
                <div class="chart-data-header">
                    <div class="chart-data-title">Data Range</div>
                    <button id="selectNewRangeBtn" class="excel-btn excel-btn-secondary excel-btn-small">
                        <span class="material-icons">edit</span> Select Range
                    </button>
                </div>
                <div class="data-range-selector">
                    <input type="text" id="currentRangeInput" class="form-control" value="${formatRangeAddress(this.selectedRange)}" readonly>
                </div>
            </div>
             <div class="chart-data-section">
                 <div class="chart-data-title">Data Preview</div>
                 <div id="dataPreviewTableContainer" style="max-height: 300px; overflow-y: auto;">
                     <!-- Data table generated dynamically -->
                 </div>
             </div>
        `;
        this.renderDataPreviewTable();
    }

    loadStyleTab(container) {
        container.innerHTML = `
            <div class="chart-style-intro">
                <h3>Customize Chart Style and Appearance</h3>
                <p>Choose from professionally designed templates or customize individual elements to create the perfect visual for your data.</p>
            </div>

            <div class="chart-settings-section">
                <div class="chart-settings-title">
                    <span class="material-icons">style</span>
                    Chart Templates
                </div>
                <p>Select a pre-designed template to instantly apply professionally designed styles to your chart.</p>
                <div class="chart-template-grid" id="templateGrid">
                    <!-- Templates loaded dynamically -->
                </div>
            </div>

            <div class="chart-style-options-container">
                <div class="chart-settings-section">
                    <div class="chart-settings-title">
                        <span class="material-icons">color_lens</span>
                        Color Palette
                    </div>
                    <p>Choose a color scheme that best matches your branding or visual preferences.</p>
                    <div class="color-palette-display" id="colorPaletteDisplay">
                        <!-- Color palettes will be displayed here -->
                    </div>
                </div>

                <div class="chart-settings-section">
                    <div class="chart-settings-title">
                        <span class="material-icons">palette</span>
                        Visual Theme
                    </div>
                    <p>Apply an overall visual theme to your chart's appearance.</p>
                    <div class="chart-theme-options" id="themeOptionsDisplay">
                        <!-- Theme options will be displayed here -->
                    </div>
                </div>
            </div>
        `;

        // Load chart templates
        this.loadChartTemplates();

        // Create color palette display
        const colorPaletteDisplay = container.querySelector('#colorPaletteDisplay');
        if (colorPaletteDisplay) {
            Object.entries(COLOR_PALETTES).forEach(([key, palette]) => {
                const paletteItem = document.createElement('div');
                paletteItem.className = 'color-palette-item';
                if (JSON.stringify(this.chartOptions.colors) === JSON.stringify(palette)) {
                    paletteItem.classList.add('selected');
                }

                // Create preview of colors
                const previewDiv = document.createElement('div');
                previewDiv.className = 'color-palette-preview';

                palette.slice(0, 5).forEach(color => {
                    const colorDiv = document.createElement('div');
                    colorDiv.style.backgroundColor = color;
                    previewDiv.appendChild(colorDiv);
                });

                // Create name label
                const nameDiv = document.createElement('div');
                nameDiv.className = 'color-palette-name';
                nameDiv.textContent = key.charAt(0).toUpperCase() + key.slice(1).toLowerCase();

                paletteItem.appendChild(previewDiv);
                paletteItem.appendChild(nameDiv);

                paletteItem.addEventListener('click', () => {
                    // Update selected state in UI
                    colorPaletteDisplay.querySelectorAll('.color-palette-item').forEach(item => {
                        item.classList.remove('selected');
                    });
                    paletteItem.classList.add('selected');

                    // Update chart options
                    this.chartOptions.colors = palette;
                    this.updatePreview();
                });

                colorPaletteDisplay.appendChild(paletteItem);
            });
        }

        // Create theme options display
        const themeOptionsDisplay = container.querySelector('#themeOptionsDisplay');
        if (themeOptionsDisplay) {
            Object.entries(CHART_THEMES).forEach(([key, theme]) => {
                const themeItem = document.createElement('div');
                themeItem.className = 'chart-theme-option';
                if (this.chartOptions.theme === theme) {
                    themeItem.classList.add('selected');
                }

                // Create preview
                const previewDiv = document.createElement('div');
                previewDiv.className = 'chart-theme-preview';

                // Create a small preview icon based on theme
                const iconColor = key === 'DARK' ? '#fff' : key === 'COLORFUL' ? '#FF4500' : '#1a73e8';
                const bgColor = key === 'DARK' ? '#333' : key === 'MONOCHROME' ? '#f0f0f0' : '#fff';
                previewDiv.style.backgroundColor = bgColor;
                previewDiv.innerHTML = `<span class="material-icons" style="color: ${iconColor}; font-size: 36px;">insert_chart</span>`;

                // Create name label
                const nameDiv = document.createElement('div');
                nameDiv.className = 'chart-theme-name';
                nameDiv.textContent = key.charAt(0).toUpperCase() + key.slice(1).toLowerCase();

                themeItem.appendChild(previewDiv);
                themeItem.appendChild(nameDiv);

                themeItem.addEventListener('click', () => {
                    // Update selected state in UI
                    themeOptionsDisplay.querySelectorAll('.chart-theme-option').forEach(item => {
                        item.classList.remove('selected');
                    });
                    themeItem.classList.add('selected');

                    // Update chart options
                    this.chartOptions.theme = theme;
                    this.updatePreview();
                });

                themeOptionsDisplay.appendChild(themeItem);
            });
        }
    }

    loadCustomizeTab(container) {
        // Content for this tab is primarily in the sidebar.
        // We might add advanced options here later.
        container.innerHTML = `
             <div class="chart-settings-section">
                 <div class="chart-settings-title">Advanced Customization</div>
                 <p>More detailed customization options (e.g., axis formatting, specific element styling) can be added here.</p>
                 <!-- Example: Line Style for Line/Area charts -->
                 ${(this.chartOptions.type === 'line' || this.chartOptions.type === 'area') ? `
                    <div class="form-group">
                         <label for="lineStyleSelect">Line Style:</label>
                         <select id="lineStyleSelect" class="form-control">
                             <option value="straight" ${this.chartOptions.lineStyle === 'straight' ? 'selected' : ''}>Straight</option>
                             <option value="smooth" ${this.chartOptions.lineStyle === 'smooth' ? 'selected' : ''}>Smooth</option>
                         </select>
                     </div>
                 ` : ''}
                  ${(this.chartOptions.type === 'area') ? `
                     <div class="form-group">
                         <label for="fillOpacitySlider">Fill Opacity:</label>
                         <input type="range" id="fillOpacitySlider" min="0" max="1" step="0.1" value="${this.chartOptions.fillOpacity || 0.7}">
                         <span id="fillOpacityValue">${this.chartOptions.fillOpacity || 0.7}</span>
                     </div>
                 ` : ''}
                  ${(this.chartOptions.type === 'column' || this.chartOptions.type === 'bar') ? `
                     <div class="form-group">
                         <label for="borderRadiusSlider">Bar Corner Radius:</label>
                         <input type="range" id="borderRadiusSlider" min="0" max="15" step="1" value="${this.chartOptions.borderRadius || 4}">
                         <span id="borderRadiusValue">${this.chartOptions.borderRadius || 4}px</span>
                     </div>
                 ` : ''}
            </div>
        `;
    }

    // --- Helper Functions ---

    selectChartType(type) {
        this.chartOptions.type = type;
        // Re-process data for the new type
        this.processAndSetData(type);
        // Update selected state in UI
        this.modal.querySelectorAll('.chart-type-item').forEach(item => {
            item.classList.toggle('selected', item.dataset.type === type);
        });
        // Update preview
        this.updatePreview();
        // Update customize tab if visible
        if (this.activeTab === 'customize') {
             this.loadCustomizeTab(this.modal.querySelector('#tabContentContainer'));
             this.attachTabContentListeners('customize');
        }
    }

    updatePalettePreview() {
        const container = this.modal.querySelector('#palettePreviewContainer');
        if (!container) return;
        container.innerHTML = '';
        const selectedPalette = this.chartOptions.colors || [];
        selectedPalette.slice(0, 8).forEach(color => {
            const swatch = document.createElement('div');
            swatch.style.backgroundColor = color;
            swatch.style.width = '20px';
            swatch.style.height = '20px';
            swatch.style.borderRadius = '3px';
            container.appendChild(swatch);
        });
    }

    loadChartTemplates() {
        const grid = this.modal.querySelector('#templateGrid');
        grid.innerHTML = ''; // Clear previous
        Object.entries(CHART_TEMPLATES).forEach(([key, template]) => {
            const item = document.createElement('div');
            item.className = 'chart-template-item';
            item.dataset.templateKey = key;
            item.innerHTML = `
                <div class="chart-template-preview">
                    <span class="material-icons" style="font-size: 48px; color: ${template.colors[0]};">${CHART_TYPE_ICONS[template.type]}</span>
                </div>
                <div class="chart-template-name">${template.title}</div>
            `;
            item.addEventListener('click', () => this.applyTemplate(key));
            grid.appendChild(item);
        });
    }

    applyTemplate(templateKey) {
        const template = CHART_TEMPLATES[templateKey];
        if (!template) return;

        // Apply template options
        this.chartOptions = {
            ...DEFAULT_CHART_OPTIONS, // Start with defaults
            ...template.options,      // Apply template specific options
            type: template.type,      // Set type from template
            title: template.title,    // Set title from template
            colors: template.colors   // Set colors from template
        };

        // Highlight selected template (optional)
         this.modal.querySelectorAll('.chart-template-item').forEach(item => {
             item.classList.toggle('selected', item.dataset.templateKey === templateKey);
         });

        // Refresh sidebar to reflect new options
        this.loadSidebarContent(this.activeTab);
        // Reprocess data for the new type
        this.processAndSetData(this.chartOptions.type);
        // Update preview
        this.updatePreview();
    }

    renderDataPreviewTable() {
        const container = this.modal.querySelector('#dataPreviewTableContainer');
        if (!container || !this.extractedData) return;

        const table = document.createElement('table');
        table.className = 'chart-data-table';
        const thead = table.createTHead();
        const tbody = table.createTBody();

        // Headers
        const headerRow = thead.insertRow();
        // Add empty corner cell
        headerRow.appendChild(document.createElement('th'));
        (this.extractedData.series || []).forEach((series, index) => {
             const th = document.createElement('th');
             th.textContent = series.name || `Series ${index + 1}`;
             headerRow.appendChild(th);
        });
         // Handle case with no series but values
         if (!this.extractedData.series || this.extractedData.series.length === 0 && this.extractedData.values) {
            const th = document.createElement('th');
            th.textContent = 'Value';
            headerRow.appendChild(th);
         }

        // Rows
        (this.extractedData.categories || []).forEach((category, catIndex) => {
            const row = tbody.insertRow();
            const catCell = row.insertCell();
            catCell.textContent = category;
            catCell.style.fontWeight = '500';

             (this.extractedData.series || []).forEach(series => {
                 const valCell = row.insertCell();
                 valCell.textContent = series.data[catIndex] !== undefined ? series.data[catIndex] : '-';
             });
              // Handle case with no series but values
            if (!this.extractedData.series || this.extractedData.series.length === 0 && this.extractedData.values) {
                 const valCell = row.insertCell();
                 valCell.textContent = this.extractedData.values[catIndex] !== undefined ? this.extractedData.values[catIndex] : '-';
            }
        });

        container.innerHTML = '';
        container.appendChild(table);
    }

    // --- Event Listener Attachers ---

    attachSidebarListeners() {
        const chartTitleInput = this.modal.querySelector('#chartTitle');
        if (chartTitleInput) {
            chartTitleInput.addEventListener('input', () => {
                this.chartOptions.title = chartTitleInput.value;
                this.updatePreview();
            });
        }

        const chartThemeSelect = this.modal.querySelector('#chartTheme');
        if (chartThemeSelect) {
             chartThemeSelect.addEventListener('change', () => {
                 this.chartOptions.theme = chartThemeSelect.value;
                 this.updatePreview();
             });
        }

        const colorPaletteSelect = this.modal.querySelector('#colorPalette');
        if (colorPaletteSelect) {
             colorPaletteSelect.addEventListener('change', () => {
                 this.chartOptions.colors = COLOR_PALETTES[colorPaletteSelect.value];
                 this.updatePalettePreview(); // Update visual preview
                 this.updatePreview();
             });
        }

        const animationsToggle = this.modal.querySelector('#animationsToggle');
        if (animationsToggle) {
            animationsToggle.addEventListener('change', () => {
                this.chartOptions.animations = animationsToggle.checked;
                this.updatePreview();
            });
        }

        const legendToggle = this.modal.querySelector('#legendToggle');
        if (legendToggle) {
            legendToggle.addEventListener('change', () => {
                this.chartOptions.legend = legendToggle.checked;
                this.updatePreview();
            });
        }

        const dataLabelsToggle = this.modal.querySelector('#dataLabelsToggle');
        if (dataLabelsToggle) {
             dataLabelsToggle.addEventListener('change', () => {
                 this.chartOptions.dataLabels = dataLabelsToggle.checked;
                 this.updatePreview();
             });
        }

        const gridToggle = this.modal.querySelector('#gridToggle');
        if (gridToggle) {
            gridToggle.addEventListener('change', () => {
                this.chartOptions.grid = gridToggle.checked;
                this.updatePreview();
            });
        }

        const xAxisTitleInput = this.modal.querySelector('#xAxisTitle');
        if (xAxisTitleInput) {
            xAxisTitleInput.addEventListener('input', () => {
                this.chartOptions.xAxisTitle = xAxisTitleInput.value;
                this.updatePreview();
            });
        }

        const yAxisTitleInput = this.modal.querySelector('#yAxisTitle');
        if (yAxisTitleInput) {
            yAxisTitleInput.addEventListener('input', () => {
                this.chartOptions.yAxisTitle = yAxisTitleInput.value;
                this.updatePreview();
            });
        }
    }

    attachTabContentListeners(tabName) {
         if (tabName === 'data') {
            const selectRangeBtn = this.modal.querySelector('#selectNewRangeBtn');
            if (selectRangeBtn) {
                selectRangeBtn.addEventListener('click', () => this.selectNewDataRange());
            }
         }
         if (tabName === 'customize') {
            const lineStyleSelect = this.modal.querySelector('#lineStyleSelect');
            if(lineStyleSelect) {
                lineStyleSelect.addEventListener('change', () => {
                    this.chartOptions.lineStyle = lineStyleSelect.value;
                    this.updatePreview();
                });
            }
            const fillOpacitySlider = this.modal.querySelector('#fillOpacitySlider');
            const fillOpacityValue = this.modal.querySelector('#fillOpacityValue');
            if(fillOpacitySlider && fillOpacityValue) {
                 fillOpacitySlider.addEventListener('input', () => {
                     this.chartOptions.fillOpacity = parseFloat(fillOpacitySlider.value);
                     fillOpacityValue.textContent = this.chartOptions.fillOpacity.toFixed(1);
                     this.updatePreview();
                 });
            }
             const borderRadiusSlider = this.modal.querySelector('#borderRadiusSlider');
             const borderRadiusValue = this.modal.querySelector('#borderRadiusValue');
             if(borderRadiusSlider && borderRadiusValue) {
                  borderRadiusSlider.addEventListener('input', () => {
                      this.chartOptions.borderRadius = parseInt(borderRadiusSlider.value);
                      borderRadiusValue.textContent = this.chartOptions.borderRadius + 'px';
                      this.updatePreview();
                  });
             }
         }
    }

    selectNewDataRange() {
        // Hide the modal temporarily
        this.modal.style.display = 'none';

        // Call a function (presumably in main_script or utils) to enable range selection mode
        if (window.enableRangeSelectionMode) {
            window.enableRangeSelectionMode((newRangeString) => {
                // This callback receives the new range string (e.g., "C5:D10") or null if cancelled
                this.modal.style.display = 'flex'; // Show the modal again

                if (newRangeString) {
                    console.log('New range selected:', newRangeString);
                    // Convert range string back to range object {start: {r,c}, end: {r,c}}
                    // (Need a utility function for this, e.g., parseRangeString)
                    const newRange = this.parseRangeString(newRangeString);
                    if(newRange) {
                        this.selectedRange = newRange;
                        this.extractedData = extractDataFromRange(this.selectedRange);
                        if (!this.extractedData || !this.extractedData.isValidData) {
                             alert('Invalid or insufficient data selected in the new range.');
                             // Revert to old range? Or keep UI showing the selection attempt?
                             // For now, just log and keep the old data
                             console.error('New data extraction failed');
                             this.extractedData = extractDataFromRange(this.selectedRange); // Re-extract old data
                        } else {
                             this.processAndSetData(this.chartOptions.type);
                             // Update the input field and data preview
                             const rangeInput = this.modal.querySelector('#currentRangeInput');
                             if (rangeInput) rangeInput.value = newRangeString;
                             this.renderDataPreviewTable();
                             this.updatePreview();
                        }
                    } else {
                         console.error('Could not parse selected range string:', newRangeString);
                    }
                } else {
                    console.log('Range selection cancelled.');
                }
            });
        } else {
            this.modal.style.display = 'flex'; // Show modal again if range selection isn't available
            alert('Range selection functionality is not available.');
        }
    }

    // Utility to parse range string (e.g., "A1:B10") into an object
    parseRangeString(rangeString) {
        if (!rangeString || typeof rangeString !== 'string') return null;
        const parts = rangeString.split(':');
        if (parts.length !== 2) return null;

        const parseCell = (cellString) => {
             const colMatch = cellString.match(/[A-Z]+/i);
             const rowMatch = cellString.match(/\d+/);
             if (!colMatch || !rowMatch) return null;
             const col = this.columnLetterToNumber(colMatch[0].toUpperCase());
             const row = parseInt(rowMatch[0]);
             return { r: row, c: col };
        };

        const start = parseCell(parts[0]);
        const end = parseCell(parts[1]);

        if (!start || !end) return null;

        return { start, end };
    }
     // Utility to convert column letter to number
    columnLetterToNumber(column) {
        let result = 0;
        for (let i = 0; i < column.length; i++) {
            result = result * 26 + (column.charCodeAt(i) - 64);
        }
        return result;
    }

    // --- Preview Update ---

    /**
     * Update the chart preview
     */
    updatePreview() {
        const previewContainer = this.modal.querySelector('#chartPreview');
        if (!previewContainer || !this.processedData) {
            previewContainer.innerHTML = '<p style="text-align:center; color:#888;">No data to preview.</p>';
            return;
        }

        // Clear previous preview
        if (this.previewChartInstance) {
            if (this.previewChartInstance.destroy && typeof this.previewChartInstance.destroy === 'function') {
                 try { this.previewChartInstance.destroy(); } catch(e) { console.warn("Error destroying previous preview chart", e); }
            }
             else if (this.previewChartInstance.dispose && typeof this.previewChartInstance.dispose === 'function') { // ECharts dispose method
                 try { this.previewChartInstance.dispose(); } catch(e) { console.warn("Error disposing previous preview chart", e); }
            }
            this.previewChartInstance = null;
        }
        previewContainer.innerHTML = ''; // Ensure container is empty

        // Show loading indicator
        previewContainer.innerHTML = '<div style="display: flex; justify-content: center; align-items: center; height: 100%; color: #888;"><span class="material-icons" style="font-size: 28px; margin-right: 10px;">hourglass_top</span>Loading Preview...</div>';

        // Use setTimeout to allow UI update before potentially blocking chart creation
        setTimeout(() => {
            try {
                // Generate style options based on current selections
                let baseStyleOptions = {
                    ...this.chartOptions, // Includes type, title, colors, theme etc.
                    colors: this.chartOptions.colors || COLOR_PALETTES.EXCEL, // Ensure colors are set
                    theme: this.chartOptions.theme || CHART_THEMES.LIGHT
                };
                const styleOptions = generateChartStyle(this.chartOptions.type, baseStyleOptions);

                // Combine data and style for the chart library
                const finalOptions = { ...styleOptions }; // Start with generated styles
                finalOptions.chart = {
                    ...styleOptions.chart,
                    height: this.modal.classList.contains('ai-style-change-2') ? '400px' : '360px', // Adjusted height based on class
                    width: '100%',
                    animations: {
                        enabled: true,
                        easing: 'easeinout',
                        speed: 800,
                        animateGradually: {
                            enabled: true,
                            delay: 150
                        },
                        dynamicAnimation: {
                            enabled: true,
                            speed: 350
                        }
                    },
                    toolbar: {
                        show: true,
                        tools: {
                            download: true,
                            selection: true,
                            zoom: true,
                            zoomin: true,
                            zoomout: true,
                            pan: true,
                            reset: true
                        }
                    }
                };
                finalOptions.title = {
                    text: this.chartOptions.title,
                    ...styleOptions.title,
                    style: {
                        fontSize: '18px',
                        fontWeight: 600,
                        fontFamily: this.chartOptions.fontFamily || 'Roboto, Arial, sans-serif'
                    }
                };

                // Prepare data based on chart library requirements
                let chartDataForLib;

                // Validate processed data
                if (!this.processedData) {
                    console.error('No processed data available for chart');
                    this.processedData = {
                        categories: ['No Data'],
                        series: [{ name: 'No Data', data: [0] }],
                        labels: ['No Data']
                    };
                }

                // Ensure series is properly structured
                if (!this.processedData.series || !Array.isArray(this.processedData.series)) {
                    console.warn('Invalid series data, creating default series');
                    this.processedData.series = [{ name: 'Default', data: [0] }];
                }

                // Ensure categories exist
                if (!this.processedData.categories || !Array.isArray(this.processedData.categories)) {
                    console.warn('Invalid categories, creating default categories');
                    this.processedData.categories = ['Category 1'];
                }

                // Ensure labels exist for pie charts
                if (!this.processedData.labels || !Array.isArray(this.processedData.labels)) {
                    console.warn('Invalid labels, using categories as labels');
                    this.processedData.labels = this.processedData.categories;
                }

                if (this._chartLibrary === 'apexcharts') {
                    try {
                        console.log('Preparing data for ApexCharts, chart type:', this.chartOptions.type);

                        // Let the createApexChart function handle all the data formatting
                        // Just pass the processed data directly
                        chartDataForLib = this.processedData;

                        // Add some basic validation to ensure we have data
                        if (!chartDataForLib.series || !Array.isArray(chartDataForLib.series) || chartDataForLib.series.length === 0) {
                            console.warn('No series data found, creating default series');
                            chartDataForLib.series = [{
                                name: 'Series 1',
                                data: [0]
                            }];
                        }

                        // For pie/donut charts, ensure we have labels
                        if ((this.chartOptions.type === 'pie' || this.chartOptions.type === 'doughnut') &&
                            (!chartDataForLib.labels || !Array.isArray(chartDataForLib.labels))) {
                            console.log('Setting up labels for pie/donut chart');

                            // Try to get labels from various sources
                            if (chartDataForLib.categories && Array.isArray(chartDataForLib.categories)) {
                                chartDataForLib.labels = chartDataForLib.categories;
                            } else if (chartDataForLib.series && chartDataForLib.series[0] &&
                                      chartDataForLib.series[0].data && Array.isArray(chartDataForLib.series[0].data) &&
                                      chartDataForLib.series[0].data[0] && typeof chartDataForLib.series[0].data[0] === 'object') {
                                chartDataForLib.labels = chartDataForLib.series[0].data.map(item =>
                                    item.x || item.name || 'Unnamed'
                                );
                            } else {
                                // Generate default labels
                                const seriesData = chartDataForLib.series[0].data;
                                chartDataForLib.labels = Array.from(
                                    {length: Array.isArray(seriesData) ? seriesData.length : 1},
                                    (_, i) => `Category ${i+1}`
                                );
                            }
                        }

                        // Copy any style options to the final options
                        if (this.chartOptions.type === 'pie' || this.chartOptions.type === 'doughnut') {
                            // For pie/donut charts
                            finalOptions.plotOptions = {
                                ...finalOptions.plotOptions,
                                pie: {
                                    donut: {
                                        size: this.chartOptions.type === 'doughnut' ? '50%' : '0%'
                                    },
                                    customScale: 0.9,
                                    offsetX: 0,
                                    offsetY: 0,
                                    expandOnClick: true
                                }
                            };

                            finalOptions.dataLabels = {
                                enabled: true,
                                formatter: function(val, opts) {
                                    return opts.w.globals.labels[opts.seriesIndex] + ': ' + val.toFixed(1) + '%';
                                },
                                style: {
                                    fontSize: '12px',
                                    fontFamily: this.chartOptions.fontFamily || 'Roboto, Arial, sans-serif',
                                    fontWeight: 'normal'
                                }
                            };
                        } else {
                            // For other chart types (bar, line, etc.)
                            finalOptions.xaxis = {
                                categories: chartDataForLib.categories,
                                ...styleOptions.xaxis,
                                labels: {
                                    style: {
                                        fontSize: '14px',
                                        fontFamily: this.chartOptions.fontFamily || 'Roboto, Arial, sans-serif'
                                    }
                                }
                            };

                            finalOptions.yaxis = {
                                ...styleOptions.yaxis,
                                labels: {
                                    style: {
                                        fontSize: '14px',
                                        fontFamily: this.chartOptions.fontFamily || 'Roboto, Arial, sans-serif'
                                    },
                                    formatter: function(val) {
                                        return val.toFixed(1);
                                    }
                                }
                            };

                            // Set up bar/column specific options
                            if (this.chartOptions.type === 'bar' || this.chartOptions.type === 'column') {
                                finalOptions.plotOptions = {
                                    ...finalOptions.plotOptions,
                                    bar: {
                                        horizontal: this.chartOptions.type === 'bar',
                                        columnWidth: '70%',
                                        barHeight: '70%',
                                        distributed: false,
                                        borderRadius: 4,
                                        dataLabels: {
                                            position: this.chartOptions.type === 'bar' ? 'center' : 'top'
                                        }
                                    }
                                };
                            }
                        }

                        // Log the data being sent to ApexCharts
                        console.log('Final data for ApexCharts:', JSON.stringify({
                            type: this.chartOptions.type,
                            seriesCount: chartDataForLib.series.length,
                            hasLabels: !!chartDataForLib.labels,
                            hasCategories: !!chartDataForLib.categories
                        }));
                    } catch (error) {
                        console.error('Error preparing ApexCharts data:', error);
                        // Create a simple fallback dataset
                        chartDataForLib = {
                            series: [{
                                name: 'Series 1',
                                data: [1]
                            }],
                            categories: ['Category 1'],
                            labels: ['Category 1']
                        };
                    }
                } else { // Default/ECharts/Basic
                    chartDataForLib = this.processedData;
                }

                // Create chart based on detected/selected library
                previewContainer.innerHTML = ''; // Clear loading indicator

                if (this._chartLibrary === 'apexcharts' && typeof window.ApexCharts !== 'undefined') {
                    // ApexCharts now returns a Promise
                    // Validate chart data before creating the chart
                    this._validateChartData(chartDataForLib);

                    // Additional validation for series names
                    if (chartDataForLib.series && Array.isArray(chartDataForLib.series)) {
                        chartDataForLib.series.forEach((series, index) => {
                            if (series && typeof series === 'object') {
                                // Log the series name for debugging
                                console.log(`Series ${index} name:`,
                                    typeof series.name === 'object' ?
                                        JSON.stringify(series.name) : series.name);

                                // Ensure name is a string
                                if (typeof series.name === 'object' && series.name !== null) {
                                    if (series.name.text) {
                                        series.name = series.name.text;
                                    } else {
                                        series.name = `Series ${index + 1}`;
                                    }
                                }
                            }
                        });
                    }

                    console.log('Creating ApexChart with validated data:', JSON.stringify({
                        type: this.chartOptions.type,
                        seriesCount: Array.isArray(chartDataForLib.series) ? chartDataForLib.series.length : 0,
                        seriesNames: Array.isArray(chartDataForLib.series) ?
                            chartDataForLib.series.map(s => typeof s === 'object' ? s.name : 'unnamed') : [],
                        hasLabels: !!chartDataForLib.labels,
                        hasCategories: !!chartDataForLib.categories
                    }));

                    try {
                        const chartPromise = createApexChart(previewContainer, this.chartOptions.type, chartDataForLib, finalOptions);

                        // Handle the Promise
                        if (chartPromise instanceof Promise) {
                            chartPromise.then(chart => {
                                this.previewChartInstance = chart;
                                console.log('ApexChart preview created successfully via Promise');
                            }).catch(error => {
                                console.error('Error creating ApexChart preview:', error);
                                previewContainer.innerHTML = '<p style="text-align:center; color:#cc0000;">Error generating preview. Please try a different chart type.</p>';

                                // Fallback to basic chart
                                this.previewChartInstance = createBasicChart(previewContainer, this.chartOptions.type, {
                                    categories: this.processedData.categories,
                                    values: (this.processedData.series && this.processedData.series[0]) ? this.processedData.series[0].data : [],
                                }, finalOptions);
                            });
                        } else {
                            // Handle non-Promise return (backward compatibility)
                            this.previewChartInstance = chartPromise;
                        }
                    } catch (error) {
                        console.error('Exception creating ApexChart:', error);
                        previewContainer.innerHTML = '<p style="text-align:center; color:#cc0000;">Error generating preview. Please try a different chart type.</p>';
                    }
                } else if (this._chartLibrary === 'echarts' && typeof window.echarts !== 'undefined') {
                    this.previewChartInstance = createEChart(previewContainer, this.chartOptions.type, chartDataForLib, finalOptions);
                } else {
                    // Basic chart needs a simpler structure
                    this.previewChartInstance = createBasicChart(previewContainer, this.chartOptions.type, {
                        categories: this.processedData.categories,
                        values: (this.processedData.series && this.processedData.series[0]) ? this.processedData.series[0].data : [],
                    }, finalOptions);
                }

                if (!this.previewChartInstance) {
                    previewContainer.innerHTML = '<p style="text-align:center; color:#cc0000;">Failed to generate preview.</p>';
                }

            } catch (error) {
                console.error('Error updating chart preview:', error);
                previewContainer.innerHTML = '<p style="text-align:center; color:#cc0000;">Error generating preview.</p>';
            }
        }, 50); // Short delay
    }

    // Helper function to get appropriate icon for each group
    getGroupIcon(groupName) {
        const icons = {
            'BASIC': 'bar_chart',
            'CIRCULAR': 'pie_chart',
            'SCATTER': 'scatter_plot',
            'STATISTICAL': 'analytics',
            'FINANCIAL': 'monetization_on',
            'SPECIALIZED': 'auto_graph'
        };
        return icons[groupName] || 'insert_chart';
    }

    // Helper function to format group name for display
    formatGroupName(groupName) {
        return groupName.charAt(0).toUpperCase() + groupName.slice(1).toLowerCase() + ' Charts';
    }

    // Helper function to get description for each group
    getGroupDescription(groupName) {
        const descriptions = {
            'BASIC': 'Standard charts for comparing values across categories or showing trends over time.',
            'CIRCULAR': 'Charts showing proportions, composition, and cyclical data in a circular layout.',
            'SCATTER': 'Charts for showing relationships between variables and identifying correlations.',
            'STATISTICAL': 'Advanced charts for statistical analysis and data distribution visualization.',
            'FINANCIAL': 'Specialized charts for financial data, market analysis, and business metrics.',
            'SPECIALIZED': 'Unique chart types for specific use cases such as project management and hierarchical data.'
        };
        return descriptions[groupName] || '';
    }

    // Helper method to validate chart data before creating charts
    _validateChartData(chartData) {
        console.log('Validating chart data:', typeof chartData);

        if (!chartData) {
            console.warn('Chart data is null or undefined, using default');
            return false;
        }

        // For pie/donut charts
        if (this.chartOptions.type === 'pie' || this.chartOptions.type === 'doughnut') {
            // Check if we have series data
            if (!chartData.series) {
                console.warn('No series data for pie/donut chart');
                chartData.series = [1];
                chartData.labels = ['No Data'];
                return false;
            }

            // If series is an array of objects with data property, we need to extract the data
            if (Array.isArray(chartData.series) && chartData.series.length > 0 &&
                typeof chartData.series[0] === 'object' && chartData.series[0].data) {
                console.log('Series contains objects with data property, extracting data');

                // Extract data from the first series
                const firstSeriesData = chartData.series[0].data;

                // If data is an array of objects, extract y values
                if (Array.isArray(firstSeriesData) && firstSeriesData.length > 0 &&
                    typeof firstSeriesData[0] === 'object') {
                    console.log('Data contains objects, extracting y values');
                    chartData.series = firstSeriesData.map(item => {
                        return item.y !== undefined ? item.y : (item.value !== undefined ? item.value : 0);
                    });
                } else if (Array.isArray(firstSeriesData)) {
                    // If data is already an array of values, use it directly
                    chartData.series = firstSeriesData;
                }
            }

            // Ensure we have labels
            if (!chartData.labels || !Array.isArray(chartData.labels) || chartData.labels.length === 0) {
                console.warn('No labels for pie/donut chart, generating default labels');
                chartData.labels = Array.from({length: Array.isArray(chartData.series) ? chartData.series.length : 1},
                    (_, i) => `Category ${i+1}`);
            }

            // Ensure series and labels have the same length
            if (Array.isArray(chartData.series) && Array.isArray(chartData.labels) &&
                chartData.series.length !== chartData.labels.length) {
                console.warn('Series and labels length mismatch, adjusting');

                if (chartData.series.length > chartData.labels.length) {
                    // Add default labels
                    while (chartData.labels.length < chartData.series.length) {
                        chartData.labels.push(`Category ${chartData.labels.length + 1}`);
                    }
                } else {
                    // Truncate series
                    chartData.series = chartData.series.slice(0, chartData.labels.length);
                }
            }
        } else {
            // For other chart types (bar, line, etc.)
            if (!chartData.series || !Array.isArray(chartData.series) || chartData.series.length === 0) {
                console.warn('No series data for chart');
                chartData.series = [{
                    name: 'Series 1',
                    data: [0]
                }];
                return false;
            }

            // Validate each series
            chartData.series.forEach((series, index) => {
                if (!series || typeof series !== 'object') {
                    console.warn(`Invalid series at index ${index}, replacing with default`);
                    chartData.series[index] = {
                        name: `Series ${index + 1}`,
                        data: [0]
                    };
                    return;
                }

                // Validate series name - ensure it's a string
                if (series.name) {
                    if (typeof series.name === 'string') {
                        // Name is already a string, no change needed
                    } else if (typeof series.name === 'object' && series.name !== null) {
                        console.warn(`Series name at index ${index} is an object, converting to string`);
                        // Handle case where name is an object with text property
                        if (series.name.text) {
                            series.name = series.name.text;
                        } else if (series.name.toString) {
                            // Try to convert to string
                            series.name = series.name.toString();
                        } else {
                            // Fallback to default
                            series.name = `Series ${index + 1}`;
                        }
                    } else {
                        // Convert other types to string
                        series.name = String(series.name);
                    }
                } else {
                    series.name = `Series ${index + 1}`;
                }

                if (!series.data || !Array.isArray(series.data)) {
                    console.warn(`Missing or invalid data in series at index ${index}, replacing with default`);
                    series.data = [0];
                }

                // Ensure all data points are numbers
                series.data = series.data.map(val => {
                    if (val === null || val === undefined || isNaN(val)) {
                        return 0;
                    }
                    return typeof val === 'number' ? val : parseFloat(val) || 0;
                });
            });
        }

        return true;
    }
}
