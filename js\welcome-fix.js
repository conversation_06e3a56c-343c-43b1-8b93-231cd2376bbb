/**
 * Welcome Modal Direct Fix
 * This script provides a direct, non-module approach to handle the welcome modal
 */

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Welcome-fix.js loaded - Direct fix for welcome modal');

    // Initialize immediately
    initWelcomeModal();
});

// Initialize the welcome modal
function initWelcomeModal() {
    console.log('Initializing welcome modal with direct fix');

    // Get modal elements
    const modal = document.getElementById('welcomeModal');
    const newBtn = document.getElementById('welcomeNewBtn');
    const openBtn = document.getElementById('welcomeOpenBtn');

    if (!modal) {
        console.error('Welcome modal not found in the DOM');
        return;
    }

    console.log('Welcome modal found:', modal);

    // Make sure the modal is visible
    modal.style.display = 'flex';

    // Add event listener to new workbook button
    if (newBtn) {
        console.log('New button found, adding event listener');
        newBtn.addEventListener('click', function() {
            console.log('New workbook button clicked');
            hideModal();
            createNewWorkbook();
        });
    } else {
        console.error('New workbook button not found in the DOM');
    }

    // Add event listener to open file button
    if (openBtn) {
        console.log('Open button found, adding event listener');
        openBtn.addEventListener('click', function() {
            console.log('Open file button clicked');
            hideModal();
            openExistingFile();
        });
    } else {
        console.error('Open file button not found in the DOM');
    }

    // Close button has been removed from the UI

    // Load and display recent files
    loadRecentFiles();

    // Function to load recent files from IndexedDB
    async function loadRecentFiles() {
        try {
            // Import the storage manager
            const storageModule = await import('./storage_manager.js');
            const storageManager = storageModule.default;

            // Get recent files (limited to 3)
            const recentFiles = await storageManager.getRecentFiles(3);

            console.log('Recent files loaded:', recentFiles);

            // Get the container
            const recentFilesContainer = document.querySelector('.recent-files');
            if (!recentFilesContainer) {
                console.error('Recent files container not found');
                return;
            }

            // Clear existing items except the heading
            const heading = recentFilesContainer.querySelector('h3');
            recentFilesContainer.innerHTML = '';
            if (heading) {
                recentFilesContainer.appendChild(heading);
            } else {
                const newHeading = document.createElement('h3');
                newHeading.textContent = 'Recent Files';
                recentFilesContainer.appendChild(newHeading);
            }

            // If no recent files, show a message
            if (!recentFiles || recentFiles.length === 0) {
                const noFilesMsg = document.createElement('div');
                noFilesMsg.className = 'no-recent-files';
                noFilesMsg.textContent = 'No recent files';
                recentFilesContainer.appendChild(noFilesMsg);
                return;
            }

            // Add recent files to the container (limited to 3)
            recentFiles.forEach(file => {
                const item = document.createElement('div');
                item.className = 'recent-file-item';
                item.dataset.name = file.name;
                item.dataset.id = file.id;

                // Format the date
                const date = new Date(file.lastOpened);
                const now = new Date();
                let dateText;

                if (date.toDateString() === now.toDateString()) {
                    dateText = 'Today';
                } else if (date.toDateString() === new Date(now - 86400000).toDateString()) {
                    dateText = 'Yesterday';
                } else if (now - date < 7 * 86400000) {
                    dateText = 'Last week';
                } else {
                    dateText = date.toLocaleDateString();
                }

                // Create elements instead of using innerHTML
                const iconSpan = document.createElement('span');
                iconSpan.className = 'material-icons recent-file-icon';
                iconSpan.textContent = 'description';

                const nameSpan = document.createElement('span');
                nameSpan.className = 'recent-file-name';
                nameSpan.textContent = file.name;

                const dateSpan = document.createElement('span');
                dateSpan.className = 'recent-file-date';
                dateSpan.textContent = dateText;

                // Append all elements to the item
                item.appendChild(iconSpan);
                item.appendChild(nameSpan);
                item.appendChild(dateSpan);

                // Add click event to open file explorer
                item.addEventListener('click', function() {
                    const fileName = this.querySelector('.recent-file-name').textContent;
                    console.log('Recent file clicked:', fileName);
                    hideModal();
                    openFileExplorer(fileName);
                });

                recentFilesContainer.appendChild(item);
            });
        } catch (error) {
            console.error('Error loading recent files:', error);

            // Fallback to static items
            const recentItems = document.querySelectorAll('.recent-file-item');
            recentItems.forEach(function(item) {
                item.addEventListener('click', function() {
                    const fileName = item.querySelector('.recent-file-name').textContent;
                    console.log('Recent file clicked:', fileName);
                    hideModal();
                    openFileExplorer(fileName);
                });
            });
        }
    }

    // Function to open file explorer with a suggested filename
    function openFileExplorer(suggestedFileName) {
        console.log(`Opening file explorer for: ${suggestedFileName}`);

        // Check if there's an existing file input with id 'fileInput'
        let existingFileInput = document.getElementById('fileInput');
        if (existingFileInput) {
            console.log('Using existing fileInput element');
            existingFileInput.click();
            return;
        }

        // Check the global file dialog flag
        if (window._fileDialogOpen) {
            console.log('File dialog already open, ignoring request');
            return;
        }

        // Set the flag to indicate a file dialog is open
        window._fileDialogOpen = true;

        // Create a file input element
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = '.xlsx,.xls,.envent';
        fileInput.style.display = 'none';
        document.body.appendChild(fileInput);

        // Set up the change event handler
        fileInput.addEventListener('change', function(event) {
            // Reset the file dialog flag
            window._fileDialogOpen = false;

            const file = event.target.files[0];
            if (!file) {
                console.error('No file selected');
                document.body.removeChild(fileInput);
                return;
            }

            console.log('File selected:', file.name);

            // Try to use the global handleFileSelect function
            if (typeof window.handleFileSelect === 'function') {
                console.log('Using global handleFileSelect function');
                window.handleFileSelect(event);
            } else {
                // Show file info as fallback
                showFileInfo(file);
            }

            // Remove the file input after use
            document.body.removeChild(fileInput);
        });

        // Add cancel event listener to reset the flag when the dialog is canceled
        fileInput.addEventListener('cancel', () => {
            window._fileDialogOpen = false;
            document.body.removeChild(fileInput);
        });

        // Trigger the file dialog
        fileInput.click();

        // Show a message
        const statusElement = document.getElementById('status');
        if (statusElement) {
            statusElement.textContent = `Select a file to open (suggested: ${suggestedFileName})`;
        }
    }

    // Function to hide the modal
    function hideModal() {
        console.log('Hiding welcome modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    // Function to create a new workbook
    function createNewWorkbook() {
        console.log('Creating new workbook from welcome modal');

        // Try to click the new workbook button
        const newWorkbookBtn = document.getElementById('newWorkbookBtn');
        if (newWorkbookBtn) {
            console.log('Clicking new workbook button');

            // Set a flag to prevent multiple triggers
            if (window._creatingWorkbook) {
                console.log('Already creating workbook, ignoring click');
                return;
            }

            window._creatingWorkbook = true;
            newWorkbookBtn.click();

            // Reset the flag after a short delay
            setTimeout(() => {
                window._creatingWorkbook = false;
            }, 1000);

            return;
        }

        // If button not found, create a simple spreadsheet
        console.log('New workbook button not found, creating simple spreadsheet');
        const spreadsheetContainer = document.getElementById('spreadsheetContainer');
        if (spreadsheetContainer) {
            // Clear any existing content
            spreadsheetContainer.innerHTML = '';

            // Create a simple table
            const table = document.createElement('table');
            table.id = 'excelTable';
            table.className = 'excel-table';

            // Create header row
            const thead = document.createElement('thead');
            const headerRow = document.createElement('tr');

            // Add corner cell
            const cornerCell = document.createElement('th');
            cornerCell.className = 'corner-cell';
            headerRow.appendChild(cornerCell);

            // Add column headers (A-Z)
            for (let i = 0; i < 10; i++) {
                const th = document.createElement('th');
                th.className = 'column-header';
                th.textContent = String.fromCharCode(65 + i); // A, B, C, ...
                headerRow.appendChild(th);
            }

            thead.appendChild(headerRow);
            table.appendChild(thead);

            // Create table body
            const tbody = document.createElement('tbody');

            // Add rows
            for (let row = 1; row <= 20; row++) {
                const tr = document.createElement('tr');

                // Add row header
                const rowHeader = document.createElement('th');
                rowHeader.className = 'row-header';
                rowHeader.textContent = row;
                tr.appendChild(rowHeader);

                // Add cells
                for (let col = 0; col < 10; col++) {
                    const td = document.createElement('td');
                    td.dataset.row = row;
                    td.dataset.col = col + 1;

                    // Add input for cell editing
                    const input = document.createElement('input');
                    input.type = 'text';
                    input.className = 'cell-input';
                    input.readOnly = true;
                    input.dataset.row = row;
                    input.dataset.col = col + 1;

                    td.appendChild(input);
                    tr.appendChild(td);
                }

                tbody.appendChild(tr);
            }

            table.appendChild(tbody);
            spreadsheetContainer.appendChild(table);

            // Show toolbar
            const toolbar = document.getElementById('toolbar');
            if (toolbar) toolbar.style.display = 'flex';
        }
    }

    // Function to open an existing file
    function openExistingFile() {
        console.log('Opening file from welcome modal');

        // First try to use the existing openFileBtn directly
        const openFileBtn = document.getElementById('openFileBtn');
        if (openFileBtn) {
            console.log('Using existing openFileBtn directly');
            // Check the global file dialog flag
            if (window._fileDialogOpen) {
                console.log('File dialog already open, ignoring click');
                return;
            }

            // Use the global flag instead of a local one
            window._fileDialogOpen = true;
            openFileBtn.click();

            return;
        }

        // If no openFileBtn exists, create our own file input
        console.log('No openFileBtn found, creating our own file input');

        // Check if there's an existing file input with id 'fileInput'
        let existingFileInput = document.getElementById('fileInput');
        if (existingFileInput) {
            console.log('Using existing fileInput element');
            existingFileInput.click();
            return;
        }

        // Check the global file dialog flag
        if (window._fileDialogOpen) {
            console.log('File dialog already open, ignoring request');
            return;
        }

        // Set the flag to indicate a file dialog is open
        window._fileDialogOpen = true;

        // Create a file input element
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = '.xlsx,.xls,.envent';
        fileInput.style.display = 'none';
        document.body.appendChild(fileInput);

        // Set up the change event handler
        fileInput.addEventListener('change', function(event) {
            // Reset the file dialog flag
            window._fileDialogOpen = false;

            const file = event.target.files[0];
            if (!file) {
                console.error('No file selected');
                document.body.removeChild(fileInput);
                return;
            }

            console.log('File selected:', file.name);

            // Try to use the global handleFileSelect function
            if (typeof window.handleFileSelect === 'function') {
                console.log('Using global handleFileSelect function');
                window.handleFileSelect(event);
            } else {
                // Show file info as fallback
                showFileInfo(file);
            }

            // Remove the file input after use
            document.body.removeChild(fileInput);
        });

        // Add cancel event listener to reset the flag when the dialog is canceled
        fileInput.addEventListener('cancel', () => {
            window._fileDialogOpen = false;
            document.body.removeChild(fileInput);
        });

        // Trigger the file dialog
        fileInput.click();
    }

    // Function to show file information
    function showFileInfo(file) {
        console.log('Showing file info');
        const spreadsheetContainer = document.getElementById('spreadsheetContainer');
        if (spreadsheetContainer) {
            // Clear the container
            spreadsheetContainer.innerHTML = '';

            // Create container div
            const container = document.createElement('div');
            container.style.padding = '20px';
            container.style.textAlign = 'center';

            // Create heading
            const heading = document.createElement('h2');
            heading.textContent = 'File Selected';
            container.appendChild(heading);

            // Create file info paragraphs
            const fileNameP = document.createElement('p');
            fileNameP.textContent = `File: ${file.name}`;
            container.appendChild(fileNameP);

            const fileSizeP = document.createElement('p');
            fileSizeP.textContent = `Size: ${(file.size / 1024).toFixed(2)} KB`;
            container.appendChild(fileSizeP);

            const fileTypeP = document.createElement('p');
            fileTypeP.textContent = `Type: ${file.type || 'Unknown'}`;
            container.appendChild(fileTypeP);

            const fileModifiedP = document.createElement('p');
            fileModifiedP.textContent = `Last Modified: ${new Date(file.lastModified).toLocaleString()}`;
            container.appendChild(fileModifiedP);

            // Create note paragraph
            const noteP = document.createElement('p');
            noteP.style.color = '#888';
            noteP.style.marginTop = '20px';
            noteP.textContent = 'Note: File processing functionality is not available.';
            container.appendChild(noteP);

            // Add the container to the spreadsheet container
            spreadsheetContainer.appendChild(container);

            // Show toolbar
            const toolbar = document.getElementById('toolbar');
            if (toolbar) toolbar.style.display = 'flex';
        }
    }
}
