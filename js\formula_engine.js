// js/formula_engine.js

// Formula Engine for Excel-like spreadsheet system

class FormulaEngine {
    constructor() {
        this.functions = new Map();
        this.registerBuiltInFunctions();
    }

    // Register built-in Excel-like functions
    registerBuiltInFunctions() {
        // Math Functions
        this.registerFunction('SUM', (args) => args.reduce((a, b) => a + (Number(b) || 0), 0));
        this.registerFunction('AVERAGE', (args) => this.functions.get('SUM')(args) / args.length);
        this.registerFunction('ROUND', (args) => Math.round(Number(args[0])));
        this.registerFunction('MOD', (args) => Number(args[0]) % Number(args[1]));

        // Logical Functions
        this.registerFunction('IF', (args) => args[0] ? args[1] : args[2]);
        this.registerFunction('AND', (args) => args.every(Boolean));
        this.registerFunction('OR', (args) => args.some(Boolean));
        this.registerFunction('NOT', (args) => !args[0]);

        // Text Functions
        this.registerFunction('LEFT', (args) => String(args[0]).substring(0, Number(args[1])));
        this.registerFunction('RIGHT', (args) => {
            const str = String(args[0]);
            return str.substring(str.length - Number(args[1]));
        });
        this.registerFunction('TRIM', (args) => String(args[0]).trim());
        this.registerFunction('CONCAT', (args) => args.join(''));

        // Date Functions
        this.registerFunction('NOW', () => new Date());
        this.registerFunction('TODAY', () => {
            const date = new Date();
            date.setHours(0, 0, 0, 0);
            return date;
        });
        this.registerFunction('DATEDIF', (args) => {
            const date1 = new Date(args[0]);
            const date2 = new Date(args[1]);
            return Math.floor((date2 - date1) / (1000 * 60 * 60 * 24));
        });

        // Lookup Functions
        this.registerFunction('VLOOKUP', (args) => {
            const [searchValue, range, colIndex, exactMatch = true] = args;
            // Implementation depends on data structure
            // Placeholder for actual implementation
            return null;
        });

        // Financial Functions
        this.registerFunction('NPV', (args) => {
            const rate = Number(args[0]);
            const values = args.slice(1);
            return values.reduce((npv, value, index) => {
                return npv + Number(value) / Math.pow(1 + rate, index + 1);
            }, 0);
        });
    }

    // Register a custom function
    registerFunction(name, func) {
        this.functions.set(name.toUpperCase(), func);
    }

    // Parse and evaluate a formula
    evaluate(formula, context = {}) {
        try {
            if (!formula.startsWith('=')) {
                return formula; // Not a formula, return as is
            }

            const expression = formula.substring(1); // Remove '=' prefix
            return this.evaluateExpression(expression, context);
        } catch (error) {
            console.error('Formula evaluation error:', error);
            return '#ERROR!';
        }
    }

    // Evaluate a parsed expression
    evaluateExpression(expression, context) {
        // Basic implementation - needs to be expanded for full Excel compatibility
        const functionMatch = expression.match(/^([A-Z]+)\((.*?)\)$/);
        
        if (functionMatch) {
            const [, funcName, argsStr] = functionMatch;
            const func = this.functions.get(funcName.toUpperCase());
            
            if (!func) {
                return '#NAME?';
            }

            const args = this.parseArguments(argsStr);
            return func(args.map(arg => this.evaluateExpression(arg, context)));
        }

        // Handle cell references
        if (expression.match(/^[A-Z]+[0-9]+$/)) {
            return context[expression] || '#REF!';
        }

        // Handle numbers
        if (!isNaN(expression)) {
            return Number(expression);
        }

        // Handle strings
        if (expression.startsWith('"') && expression.endsWith('"')) {
            return expression.slice(1, -1);
        }

        return expression;
    }

    // Parse function arguments
    parseArguments(argsStr) {
        const args = [];
        let currentArg = '';
        let parenthesesCount = 0;
        let inQuotes = false;

        for (let i = 0; i < argsStr.length; i++) {
            const char = argsStr[i];

            if (char === '"') {
                inQuotes = !inQuotes;
                currentArg += char;
            } else if (!inQuotes && char === '(') {
                parenthesesCount++;
                currentArg += char;
            } else if (!inQuotes && char === ')') {
                parenthesesCount--;
                currentArg += char;
            } else if (!inQuotes && char === ',' && parenthesesCount === 0) {
                args.push(currentArg.trim());
                currentArg = '';
            } else {
                currentArg += char;
            }
        }

        if (currentArg) {
            args.push(currentArg.trim());
        }

        return args;
    }
}

// Export a singleton instance
const formulaEngine = new FormulaEngine();
export default formulaEngine;