/**
 * Welcome Modal Handler
 * Manages the welcome screen functionality
 */

class WelcomeModal {
    constructor() {
        this.modal = document.getElementById('welcomeModal');
        this.newBtn = document.getElementById('welcomeNewBtn');
        this.openBtn = document.getElementById('welcomeOpenBtn');
        this.recentItems = document.querySelectorAll('.recent-file-item');

        this.initialize();
    }

    /**
     * Initialize the welcome modal
     */
    initialize() {
        console.log('Initializing welcome modal buttons...');

        // Add event listeners to buttons
        if (this.newBtn) {
            console.log('New button found, adding event listener');
            this.newBtn.addEventListener('click', () => {
                console.log('New button clicked');
                this.hideModal();
                this.createNewWorkbook();
            });
        } else {
            console.error('New button not found in the DOM');
        }

        if (this.openBtn) {
            console.log('Open button found, adding event listener');
            this.openBtn.addEventListener('click', () => {
                console.log('Open button clicked');
                this.hideModal();
                this.openExistingFile();
            });
        } else {
            console.error('Open button not found in the DOM');
        }

        // Close button has been removed from the UI

        // Add event listeners to recent file items
        this.recentItems.forEach(item => {
            item.addEventListener('click', () => {
                const fileName = item.querySelector('.recent-file-name').textContent;
                this.hideModal();
                this.openRecentFile(fileName);
            });
        });

        // Show the modal
        this.showModal();

        console.log('Welcome modal initialized');
    }

    /**
     * Show the welcome modal
     */
    showModal() {
        if (this.modal) {
            console.log('Showing welcome modal');
            this.modal.style.display = 'flex';
        } else {
            console.error('Welcome modal element not found');
        }
    }

    /**
     * Hide the welcome modal
     */
    hideModal() {
        if (this.modal) {
            console.log('Hiding welcome modal');
            this.modal.style.display = 'none';
        } else {
            console.error('Welcome modal element not found when trying to hide');
        }
    }

    /**
     * Create a new workbook
     */
    createNewWorkbook() {
        console.log('Creating new workbook from welcome modal...');

        try {
            // Direct click on the new workbook button - most reliable method
            const newWorkbookBtn = document.getElementById('newWorkbookBtn');
            if (newWorkbookBtn) {
                console.log('Clicking new workbook button directly...');
                newWorkbookBtn.click();
                return;
            }

            console.error('New workbook button not found, trying alternative methods');

            // Try to use the main module directly
            if (window.mainModule && typeof window.mainModule.createNewWorkbook === 'function') {
                console.log('Using mainModule.createNewWorkbook()');
                window.mainModule.createNewWorkbook();
                return;
            }

            // If main module is not available, try to use the function directly
            if (typeof window.createNewWorkbook === 'function') {
                console.log('Using window.createNewWorkbook()');
                window.createNewWorkbook();
                return;
            }

            // Try to import the main_script.js module directly
            console.log('Trying to import main_script.js directly');
            import('./main_script.js')
                .then(module => {
                    if (typeof module.createNewWorkbook === 'function') {
                        console.log('Using imported createNewWorkbook function');
                        module.createNewWorkbook();
                    } else {
                        console.error('createNewWorkbook function not found in main_script.js');
                        throw new Error('createNewWorkbook function not found');
                    }
                })
                .catch(error => {
                    console.error('Error importing main_script.js:', error);
                    // Continue with fallback method
                    this._createSimpleSpreadsheet();
                });

            return; // Return here as the import is async

            // If all else fails, create a simple spreadsheet UI
            console.log('All methods failed, creating a simple spreadsheet UI');
            const spreadsheetContainer = document.getElementById('spreadsheetContainer');
            if (spreadsheetContainer) {
                // Clear the welcome modal
                spreadsheetContainer.innerHTML = '';

                // Create a simple table
                const table = document.createElement('table');
                table.id = 'excelTable';
                table.className = 'excel-table';

                // Create header row
                const thead = document.createElement('thead');
                const headerRow = document.createElement('tr');

                // Add corner cell
                const cornerCell = document.createElement('th');
                cornerCell.className = 'corner-cell';
                headerRow.appendChild(cornerCell);

                // Add column headers (A-Z)
                for (let i = 0; i < 10; i++) {
                    const th = document.createElement('th');
                    th.className = 'column-header';
                    th.textContent = String.fromCharCode(65 + i); // A, B, C, ...
                    headerRow.appendChild(th);
                }

                thead.appendChild(headerRow);
                table.appendChild(thead);

                // Create table body
                const tbody = document.createElement('tbody');

                // Add rows
                for (let row = 1; row <= 20; row++) {
                    const tr = document.createElement('tr');

                    // Add row header
                    const rowHeader = document.createElement('th');
                    rowHeader.className = 'row-header';
                    rowHeader.textContent = row;
                    tr.appendChild(rowHeader);

                    // Add cells
                    for (let col = 0; col < 10; col++) {
                        const td = document.createElement('td');
                        td.dataset.row = row;
                        td.dataset.col = col + 1;

                        // Add input for cell editing
                        const input = document.createElement('input');
                        input.type = 'text';
                        input.className = 'cell-input';
                        input.readOnly = true;
                        input.dataset.row = row;
                        input.dataset.col = col + 1;

                        td.appendChild(input);
                        tr.appendChild(td);
                    }

                    tbody.appendChild(tr);
                }

                table.appendChild(tbody);
                spreadsheetContainer.appendChild(table);

                // Show toolbar
                const toolbar = document.getElementById('toolbar');
                if (toolbar) toolbar.style.display = 'flex';

                // Show formula bar
                const formulaBar = document.getElementById('formulaBar');
                if (formulaBar) formulaBar.style.display = 'flex';
            }
        } catch (error) {
            console.error('Error creating new workbook:', error);
            alert('Could not create a new workbook. Please try again later.');
        }
    }

    /**
     * Open an existing file
     */
    openExistingFile() {
        console.log('Opening file dialog from welcome modal...');

        try {
            // Direct click on the open file button - most reliable method
            const openFileBtn = document.getElementById('openFileBtn');
            if (openFileBtn) {
                console.log('Clicking open file button directly...');
                openFileBtn.click();
                return;
            }

            console.error('Open file button not found, trying alternative methods');

            // Create a file input element as fallback
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = '.xlsx,.xls';
            fileInput.style.display = 'none';
            document.body.appendChild(fileInput);

            // Set up the change event handler
            fileInput.addEventListener('change', (event) => {
                try {
                    const file = event.target.files[0];
                    if (!file) {
                        console.error('No file selected');
                        return;
                    }

                    console.log('File selected:', file.name);

                    // First try to use the main module
                    if (window.mainModule && typeof window.mainModule.openFile === 'function') {
                        console.log('Using mainModule.openFile()');
                        window.mainModule.openFile(event);
                        return;
                    }

                    // If main module is not available, try to use the function directly
                    if (typeof window.handleFileSelect === 'function') {
                        console.log('Using window.handleFileSelect()');
                        window.handleFileSelect(event);
                        return;
                    }

                    // Try to import the main_script.js module directly
                    console.log('Trying to import main_script.js for file handling');
                    import('./main_script.js')
                        .then(module => {
                            if (typeof module.openFile === 'function') {
                                console.log('Using imported openFile function');
                                module.openFile(event);
                            } else {
                                console.error('openFile function not found in main_script.js');
                                throw new Error('openFile function not found');
                            }
                        })
                        .catch(error => {
                            console.error('Error importing main_script.js for file handling:', error);
                            // Show file info as fallback
                            this._showFileInfo(file);
                        });

                    return; // Return here as the import is async

                    // If all else fails, create a simple message
                    console.log('All methods failed, showing file name');
                    const spreadsheetContainer = document.getElementById('spreadsheetContainer');
                    if (spreadsheetContainer) {
                        spreadsheetContainer.innerHTML = `
                            <div style="padding: 20px; text-align: center;">
                                <h2>File Selected</h2>
                                <p>File: ${file.name}</p>
                                <p>Size: ${(file.size / 1024).toFixed(2)} KB</p>
                                <p>Type: ${file.type || 'Unknown'}</p>
                                <p>Last Modified: ${new Date(file.lastModified).toLocaleString()}</p>
                                <p style="color: #888; margin-top: 20px;">Note: File processing functionality is not available.</p>
                            </div>
                        `;

                        // Show toolbar
                        const toolbar = document.getElementById('toolbar');
                        if (toolbar) toolbar.style.display = 'flex';
                    }
                } catch (error) {
                    console.error('Error handling file selection:', error);
                    alert('Error processing the selected file: ' + error.message);
                } finally {
                    // Remove the file input after use
                    document.body.removeChild(fileInput);
                }
            });

            // Trigger the file dialog
            fileInput.click();
        } catch (error) {
            console.error('Error opening file dialog:', error);
            alert('Could not open the file dialog. Please try again later.');
        }
    }

    /**
     * Open a recent file
     * @param {string} fileName - Name of the file to open
     */
    openRecentFile(fileName) {
        console.log(`Opening recent file: ${fileName}`);

        // In a real application, you would look up the file path from storage
        // and then open the file. For now, we'll just show a message.
        if (typeof updateStatus === 'function') {
            updateStatus(`Opening ${fileName}...`, 'info');
        }

        // For demo purposes, create a new workbook
        this.createNewWorkbook();
    }

    /**
     * Create a simple spreadsheet as a fallback
     * @private
     */
    _createSimpleSpreadsheet() {
        console.log('Creating a simple spreadsheet UI as fallback');
        const spreadsheetContainer = document.getElementById('spreadsheetContainer');
        if (spreadsheetContainer) {
            // Clear the welcome modal
            spreadsheetContainer.innerHTML = '';

            // Create a simple table
            const table = document.createElement('table');
            table.id = 'excelTable';
            table.className = 'excel-table';

            // Create header row
            const thead = document.createElement('thead');
            const headerRow = document.createElement('tr');

            // Add corner cell
            const cornerCell = document.createElement('th');
            cornerCell.className = 'corner-cell';
            headerRow.appendChild(cornerCell);

            // Add column headers (A-Z)
            for (let i = 0; i < 10; i++) {
                const th = document.createElement('th');
                th.className = 'column-header';
                th.textContent = String.fromCharCode(65 + i); // A, B, C, ...
                headerRow.appendChild(th);
            }

            thead.appendChild(headerRow);
            table.appendChild(thead);

            // Create table body
            const tbody = document.createElement('tbody');

            // Add rows
            for (let row = 1; row <= 20; row++) {
                const tr = document.createElement('tr');

                // Add row header
                const rowHeader = document.createElement('th');
                rowHeader.className = 'row-header';
                rowHeader.textContent = row;
                tr.appendChild(rowHeader);

                // Add cells
                for (let col = 0; col < 10; col++) {
                    const td = document.createElement('td');
                    td.dataset.row = row;
                    td.dataset.col = col + 1;

                    // Add input for cell editing
                    const input = document.createElement('input');
                    input.type = 'text';
                    input.className = 'cell-input';
                    input.readOnly = true;
                    input.dataset.row = row;
                    input.dataset.col = col + 1;

                    td.appendChild(input);
                    tr.appendChild(td);
                }

                tbody.appendChild(tr);
            }

            table.appendChild(tbody);
            spreadsheetContainer.appendChild(table);

            // Show toolbar
            const toolbar = document.getElementById('toolbar');
            if (toolbar) toolbar.style.display = 'flex';

            // Show formula bar
            const formulaBar = document.getElementById('formulaBar');
            if (formulaBar) formulaBar.style.display = 'flex';
        }
    }

    /**
     * Show file information as a fallback
     * @param {File} file - The file object
     * @private
     */
    _showFileInfo(file) {
        console.log('Showing file info as fallback');
        const spreadsheetContainer = document.getElementById('spreadsheetContainer');
        if (spreadsheetContainer) {
            spreadsheetContainer.innerHTML = `
                <div style="padding: 20px; text-align: center;">
                    <h2>File Selected</h2>
                    <p>File: ${file.name}</p>
                    <p>Size: ${(file.size / 1024).toFixed(2)} KB</p>
                    <p>Type: ${file.type || 'Unknown'}</p>
                    <p>Last Modified: ${new Date(file.lastModified).toLocaleString()}</p>
                    <p style="color: #888; margin-top: 20px;">Note: File processing functionality is not available.</p>
                </div>
            `;

            // Show toolbar
            const toolbar = document.getElementById('toolbar');
            if (toolbar) toolbar.style.display = 'flex';
        }
    }
}

// Initialize the welcome modal when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Check if the welcome modal already exists
    if (!window.welcomeModal) {
        console.log('Initializing welcome modal from DOMContentLoaded event');
        window.welcomeModal = new WelcomeModal();
    }
});

// Also initialize immediately if the document is already loaded
if (document.readyState === 'complete' || document.readyState === 'interactive') {
    // Delay slightly to ensure DOM is fully processed
    setTimeout(() => {
        if (!window.welcomeModal) {
            console.log('Initializing welcome modal immediately (document already loaded)');
            window.welcomeModal = new WelcomeModal();
        }
    }, 100);
}

// Export the WelcomeModal class
export default WelcomeModal;
