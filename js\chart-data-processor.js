/**
 * Advanced chart data processing utilities
 */
import { extractDataFromRange } from './chart-utils.js';

/**
 * Process data for different chart types
 * @param {Object} data - The raw data object
 * @param {string} chartType - The chart type
 * @returns {Object} Processed data for the chart
 */
export function processChartData(data, chartType) {
    if (!data) return null;
    
    switch (chartType) {
        case 'pie':
        case 'doughnut':
            return processPieData(data);
        case 'scatter':
        case 'bubble':
            return processScatterData(data);
        case 'heatmap':
            return processHeatmapData(data);
        case 'radar':
        case 'polar':
            return processRadarData(data);
        case 'boxplot':
            return processBoxPlotData(data);
        case 'candlestick':
            return processCandlestickData(data);
        case 'waterfall':
            return processWaterfallData(data);
        case 'treemap':
            return processTreemapData(data);
        case 'funnel':
            return processFunnelData(data);
        default:
            return processStandardData(data);
    }
}

/**
 * Process standard chart data (column, bar, line, area)
 * @param {Object} data - The raw data object
 * @returns {Object} Processed data for standard charts
 */
function processStandardData(data) {
    // Standard charts can use the data as is, but we'll ensure it's in the right format
    const processed = {
        categories: data.categories || [],
        series: data.series || [{
            name: 'Series 1',
            data: data.values || []
        }]
    };
    
    // Ensure all series have names
    processed.series = processed.series.map((series, index) => ({
        name: series.name || `Series ${index + 1}`,
        data: series.data || []
    }));
    
    return processed;
}

/**
 * Process pie/doughnut chart data
 * @param {Object} data - The raw data object
 * @returns {Object} Processed data for pie/doughnut charts
 */
function processPieData(data) {
    // For pie charts, we need to pair categories with values
    const processed = {
        labels: data.categories || [],
        series: data.values || []
    };
    
    // If we have multiple series, use only the first one for pie charts
    if (data.series && data.series.length > 0) {
        processed.series = data.series[0].data || [];
    }
    
    return processed;
}

/**
 * Process scatter/bubble chart data
 * @param {Object} data - The raw data object
 * @returns {Object} Processed data for scatter/bubble charts
 */
function processScatterData(data) {
    // For scatter charts, we need [x,y] pairs
    let processed = {
        series: []
    };
    
    // If we have series data in the expected format, use it
    if (data.series && data.series.length > 0) {
        processed.series = data.series.map(series => ({
            name: series.name,
            data: series.data.map((y, i) => {
                // For bubble charts, we need [x, y, z] where z is the bubble size
                if (series.sizes && series.sizes[i] !== undefined) {
                    return [i, y, series.sizes[i]];
                }
                // For scatter charts, we need [x, y]
                return [i, y];
            })
        }));
    } 
    // Otherwise, try to create from categories and values
    else if (data.categories && data.values) {
        processed.series = [{
            name: 'Series 1',
            data: data.categories.map((cat, i) => {
                // Try to convert category to number for x-axis
                const x = !isNaN(parseFloat(cat)) ? parseFloat(cat) : i;
                return [x, data.values[i] || 0];
            })
        }];
    }
    
    return processed;
}

/**
 * Process heatmap chart data
 * @param {Object} data - The raw data object
 * @returns {Object} Processed data for heatmap charts
 */
function processHeatmapData(data) {
    // For heatmaps, we need series of {x, y, value}
    let processed = {
        categories: data.categories || [],
        series: []
    };
    
    // If we have series data in the right format, use it
    if (data.series && data.series.length > 0 && data.categories) {
        processed.series = data.series.map(series => ({
            name: series.name,
            data: series.data.map((value, i) => ({
                x: data.categories[i] || `Item ${i+1}`,
                y: value
            }))
        }));
    }
    // Otherwise, try to create a heatmap from the raw data
    else if (data.rawData && Array.isArray(data.rawData)) {
        // Assume rawData is a 2D array of values
        processed.series = data.rawData.map((row, rowIndex) => ({
            name: `Row ${rowIndex + 1}`,
            data: row.map((value, colIndex) => ({
                x: data.categories[colIndex] || `Column ${colIndex + 1}`,
                y: value
            }))
        }));
    }
    
    return processed;
}

/**
 * Process radar/polar chart data
 * @param {Object} data - The raw data object
 * @returns {Object} Processed data for radar/polar charts
 */
function processRadarData(data) {
    // For radar charts, we need categories and series
    const processed = {
        categories: data.categories || [],
        series: data.series || [{
            name: 'Series 1',
            data: data.values || []
        }]
    };
    
    return processed;
}

/**
 * Process boxplot chart data
 * @param {Object} data - The raw data object
 * @returns {Object} Processed data for boxplot charts
 */
function processBoxPlotData(data) {
    // For boxplots, we need [min, q1, median, q3, max] for each category
    let processed = {
        categories: data.categories || [],
        series: []
    };
    
    // If we have series data in the expected format, use it
    if (data.series && data.series.length > 0 && data.series[0].boxplotData) {
        processed.series = data.series.map(series => ({
            name: series.name,
            data: series.boxplotData
        }));
    }
    // Otherwise, try to calculate boxplot data from raw values
    else if (data.rawData && Array.isArray(data.rawData)) {
        processed.series = [{
            name: 'Boxplot',
            data: data.rawData.map(values => {
                // Sort values
                const sorted = [...values].sort((a, b) => a - b);
                const len = sorted.length;
                
                // Calculate quartiles
                const min = sorted[0];
                const max = sorted[len - 1];
                const median = len % 2 === 0 
                    ? (sorted[len/2 - 1] + sorted[len/2]) / 2 
                    : sorted[Math.floor(len/2)];
                const q1Idx = Math.floor(len / 4);
                const q3Idx = Math.floor(len * 3 / 4);
                const q1 = sorted[q1Idx];
                const q3 = sorted[q3Idx];
                
                return [min, q1, median, q3, max];
            })
        }];
    }
    
    return processed;
}

/**
 * Process candlestick chart data
 * @param {Object} data - The raw data object
 * @returns {Object} Processed data for candlestick charts
 */
function processCandlestickData(data) {
    // For candlesticks, we need [open, high, low, close] for each category
    let processed = {
        categories: data.categories || [],
        series: []
    };
    
    // If we have series data in the expected format, use it
    if (data.series && data.series.length > 0 && data.series[0].ohlcData) {
        processed.series = [{
            name: 'OHLC',
            data: data.series[0].ohlcData
        }];
    }
    // Otherwise, try to interpret the data as OHLC
    else if (data.series && data.series.length >= 4) {
        // Assume first 4 series are open, high, low, close
        const openData = data.series[0].data || [];
        const highData = data.series[1].data || [];
        const lowData = data.series[2].data || [];
        const closeData = data.series[3].data || [];
        
        processed.series = [{
            name: 'OHLC',
            data: openData.map((open, i) => [
                open,
                highData[i] || open,
                lowData[i] || open,
                closeData[i] || open
            ])
        }];
    }
    
    return processed;
}

/**
 * Process waterfall chart data
 * @param {Object} data - The raw data object
 * @returns {Object} Processed data for waterfall charts
 */
function processWaterfallData(data) {
    // For waterfall charts, we need values and their cumulative effect
    let processed = {
        categories: data.categories || [],
        series: []
    };
    
    // If we have waterfall data in the expected format, use it
    if (data.series && data.series.length > 0 && data.series[0].waterfallData) {
        processed.series = data.series;
    }
    // Otherwise, create waterfall data from regular values
    else {
        const values = data.values || 
            (data.series && data.series.length > 0 ? data.series[0].data : []);
            
        if (values && values.length > 0) {
            // Calculate running total
            let runningTotal = 0;
            const waterfallData = values.map((value, index) => {
                const isNegative = value < 0;
                const start = runningTotal;
                runningTotal += value;
                const end = runningTotal;
                
                return {
                    x: data.categories[index] || `Item ${index + 1}`,
                    y: [start, end],
                    isNegative
                };
            });
            
            processed.series = [{
                name: 'Waterfall',
                data: waterfallData
            }];
        }
    }
    
    return processed;
}

/**
 * Process treemap chart data
 * @param {Object} data - The raw data object
 * @returns {Object} Processed data for treemap charts
 */
function processTreemapData(data) {
    // For treemaps, we need [x, y] where x is the category and y is the value
    let processed = {
        series: []
    };
    
    // If we have treemap data in the expected format, use it
    if (data.series && data.series.length > 0 && data.series[0].treemapData) {
        processed.series = data.series;
    }
    // Otherwise, create treemap data from categories and values
    else if (data.categories && data.values) {
        const treemapData = data.categories.map((cat, i) => ({
            x: cat,
            y: data.values[i] || 0
        }));
        
        processed.series = [{
            name: 'Treemap',
            data: treemapData
        }];
    }
    
    return processed;
}

/**
 * Process funnel chart data
 * @param {Object} data - The raw data object
 * @returns {Object} Processed data for funnel charts
 */
function processFunnelData(data) {
    // For funnel charts, we need categories and values in descending order
    let processed = {
        categories: [...(data.categories || [])],
        series: [{
            name: 'Funnel',
            data: [...(data.values || [])]
        }]
    };
    
    // If we have series data in the expected format, use it
    if (data.series && data.series.length > 0) {
        processed.series = [{ 
            name: data.series[0].name || 'Funnel',
            data: [...data.series[0].data]
        }];
    }
    
    // Sort data in descending order for better visualization
    if (processed.series[0].data.length > 0) {
        // Create pairs of category and value for sorting
        const pairs = processed.categories.map((cat, i) => ({
            category: cat,
            value: processed.series[0].data[i]
        }));
        
        // Sort by value in descending order
        pairs.sort((a, b) => b.value - a.value);
        
        // Reconstruct categories and values
        processed.categories = pairs.map(pair => pair.category);
        processed.series[0].data = pairs.map(pair => pair.value);
    }
    
    return processed;
}

/**
 * Extract and process data from a range for a specific chart type
 * @param {Object} range - The cell range
 * @param {string} chartType - The chart type
 * @returns {Object} Processed data for the chart
 */
export function extractAndProcessData(range, chartType) {
    // Extract raw data from range
    const rawData = extractDataFromRange(range);
    
    // Process data for the specific chart type
    return processChartData(rawData, chartType);
}

/**
 * Analyze data to suggest appropriate chart types
 * @param {Object} data - The data object
 * @returns {Array} Array of recommended chart types
 */
export function suggestChartTypes(data) {
    if (!data) return ['column']; // Default suggestion
    
    const recommendations = [];
    
    // Check data characteristics
    const hasSeries = data.series && data.series.length > 0;
    const seriesCount = hasSeries ? data.series.length : 1;
    const dataPointCount = data.categories ? data.categories.length : 0;
    const hasNegativeValues = hasSeries 
        ? data.series.some(s => s.data.some(v => v < 0))
        : (data.values ? data.values.some(v => v < 0) : false);
    
    // Time series detection (categories look like dates)
    const isTimeSeries = data.categories && data.categories.some(cat => {
        if (typeof cat !== 'string') return false;
        return /^\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}$/.test(cat) || // Date format
               /^\d{4}[\/\-\.]\d{1,2}[\/\-\.]\d{1,2}$/.test(cat) ||   // ISO date format
               /^(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)/.test(cat); // Month names
    });
    
    // Percentage detection (values sum to approximately 100)
    const isPotentialPercentage = hasSeries 
        ? data.series[0].data.reduce((sum, val) => sum + val, 0) >= 95 && 
          data.series[0].data.reduce((sum, val) => sum + val, 0) <= 105
        : (data.values ? 
            data.values.reduce((sum, val) => sum + val, 0) >= 95 && 
            data.values.reduce((sum, val) => sum + val, 0) <= 105 
            : false);
    
    // Sequential data detection (for waterfall charts)
    const hasSequentialFlow = dataPointCount >= 3 && dataPointCount <= 10;
    
    // Stage data detection (for funnel charts)
    const hasStageLikeData = dataPointCount >= 3 && dataPointCount <= 8 && 
        data.categories && data.categories.some(cat => 
            typeof cat === 'string' && 
            (cat.includes('Stage') || cat.includes('Step') || cat.includes('Phase')));
    
    // Make recommendations based on data characteristics
    if (isTimeSeries) {
        recommendations.push('line', 'area');
        if (seriesCount === 1) recommendations.push('column');
    } else if (isPotentialPercentage && dataPointCount <= 10 && !hasNegativeValues) {
        recommendations.push('pie', 'doughnut');
        recommendations.push('column', 'bar');
    } else if (seriesCount === 1 && dataPointCount <= 10) {
        recommendations.push('column', 'bar', 'line');
    } else if (seriesCount > 3) {
        recommendations.push('line', 'area', 'radar');
    } else {
        recommendations.push('column', 'line', 'bar');
    }
    
    // Add scatter for numeric x-y data
    if (data.categories && data.categories.every(cat => !isNaN(parseFloat(cat)))) {
        recommendations.push('scatter');
    }
    
    // Add waterfall for sequential data with rises and falls
    if (hasSequentialFlow && hasNegativeValues) {
        recommendations.push('waterfall');
    }
    
    // Add funnel for stage-like data
    if (hasStageLikeData && !hasNegativeValues) {
        recommendations.push('funnel');
    }
    
    // Add treemap for hierarchical or categorization data
    if (dataPointCount >= 6 && dataPointCount <= 20 && !hasNegativeValues) {
        recommendations.push('treemap');
    }
    
    return recommendations;
}

/**
 * Normalize data for better visualization
 * @param {Array} values - Array of values to normalize
 * @param {string} method - Normalization method ('minmax', 'percentage', 'zscore')
 * @returns {Array} Normalized values
 */
export function normalizeData(values, method = 'minmax') {
    if (!values || values.length === 0) return [];
    
    switch (method) {
        case 'percentage':
            // Convert values to percentages of the total
            const total = values.reduce((sum, val) => sum + val, 0);
            return values.map(val => (val / total) * 100);
            
        case 'zscore':
            // Z-score normalization (standardization)
            const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
            const stdDev = Math.sqrt(
                values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
            );
            return values.map(val => (val - mean) / (stdDev || 1));
            
        case 'minmax':
        default:
            // Min-max normalization to [0, 1] range
            const min = Math.min(...values);
            const max = Math.max(...values);
            const range = max - min;
            return values.map(val => range ? (val - min) / range : 0);
    }
}

/**
 * Detect outliers in the data
 * @param {Array} values - Array of values to check for outliers
 * @param {string} method - Detection method ('iqr', 'zscore')
 * @returns {Array} Indexes of outlier values
 */
export function detectOutliers(values, method = 'iqr') {
    if (!values || values.length === 0) return [];
    
    switch (method) {
        case 'zscore':
            // Z-score method (values beyond 3 standard deviations)
            const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
            const stdDev = Math.sqrt(
                values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
            );
            
            return values.map((val, idx) => 
                Math.abs((val - mean) / stdDev) > 3 ? idx : -1
            ).filter(idx => idx !== -1);
            
        case 'iqr':
        default:
            // IQR method (values beyond 1.5 * IQR from Q1 or Q3)
            const sorted = [...values].sort((a, b) => a - b);
            const q1Idx = Math.floor(sorted.length / 4);
            const q3Idx = Math.floor(sorted.length * 3 / 4);
            const q1 = sorted[q1Idx];
            const q3 = sorted[q3Idx];
            const iqr = q3 - q1;
            const lowerBound = q1 - (1.5 * iqr);
            const upperBound = q3 + (1.5 * iqr);
            
            return values.map((val, idx) => 
                (val < lowerBound || val > upperBound) ? idx : -1
            ).filter(idx => idx !== -1);
    }
}

/**
 * Calculate trendline for data points
 * @param {Array} xValues - X values
 * @param {Array} yValues - Y values
 * @param {string} type - Trendline type ('linear', 'exponential', 'polynomial')
 * @param {number} degree - Polynomial degree (for polynomial type)
 * @returns {Object} Trendline parameters and points
 */
export function calculateTrendline(xValues, yValues, type = 'linear', degree = 2) {
    if (!xValues || !yValues || xValues.length !== yValues.length || xValues.length < 2) {
        return null;
    }
    
    switch (type) {
        case 'linear':
            // Linear regression (y = mx + b)
            const n = xValues.length;
            const sumX = xValues.reduce((sum, x) => sum + x, 0);
            const sumY = yValues.reduce((sum, y) => sum + y, 0);
            const sumXY = xValues.reduce((sum, x, i) => sum + x * yValues[i], 0);
            const sumXX = xValues.reduce((sum, x) => sum + x * x, 0);
            
            const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
            const intercept = (sumY - slope * sumX) / n;
            
            const trendPoints = xValues.map(x => slope * x + intercept);
            
            return {
                type: 'linear',
                equation: `y = ${slope.toFixed(4)}x + ${intercept.toFixed(4)}`,
                parameters: { slope, intercept },
                points: trendPoints
            };
        
        // More trendline types could be implemented here
            
        default:
            return null;
    }
}

/**
 * Generate a predicted value based on trendline
 * @param {number} x - X value for prediction
 * @param {Object} trendline - Trendline object from calculateTrendline
 * @returns {number} Predicted Y value
 */
export function predictValue(x, trendline) {
    if (!trendline || !trendline.parameters) return null;
    
    switch (trendline.type) {
        case 'linear':
            return trendline.parameters.slope * x + trendline.parameters.intercept;
            
        // Other trendline types would be handled here
            
        default:
            return null;
    }
}
