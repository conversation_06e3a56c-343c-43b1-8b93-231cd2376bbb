/**
 * Welcome Modal Fix
 * This script fixes issues with the welcome modal:
 * 1. Ensures the close button works correctly
 * 2. Loads real recent files immediately instead of showing mock files first
 */

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Welcome modal fix loaded');

    // Add a small delay to ensure the welcome modal is fully initialized
    setTimeout(fixWelcomeModal, 100);
});

// Fix the welcome modal
function fixWelcomeModal() {
    console.log('Applying welcome modal fixes');

    // Get modal elements
    const modal = document.getElementById('welcomeModal');

    if (!modal) {
        console.error('Welcome modal not found in the DOM');
        return;
    }

    // Close button has been removed from the UI

    // Fix recent files - clear any mock files and load real ones immediately
    fixRecentFiles();

    // Function to hide the modal
    function hideModal() {
        console.log('Hiding welcome modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    // Function to fix recent files
    async function fixRecentFiles() {
        try {
            // Get the container
            const recentFilesContainer = document.querySelector('.recent-files');
            if (!recentFilesContainer) {
                console.error('Recent files container not found');
                return;
            }

            console.log('Fixing recent files');

            // Clear existing items except the heading
            const heading = recentFilesContainer.querySelector('h3');
            recentFilesContainer.innerHTML = '';
            if (heading) {
                recentFilesContainer.appendChild(heading);
            } else {
                const newHeading = document.createElement('h3');
                newHeading.textContent = 'Recent Files';
                recentFilesContainer.appendChild(newHeading);
            }

            // Import the storage manager
            const storageModule = await import('./storage_manager.js');
            const storageManager = storageModule.default;

            // Get recent files (limited to 3)
            const recentFiles = await storageManager.getRecentFiles(3);
            console.log('Recent files loaded:', recentFiles);

            // If no recent files, show a message
            if (!recentFiles || recentFiles.length === 0) {
                const noFilesMsg = document.createElement('div');
                noFilesMsg.className = 'no-recent-files';
                noFilesMsg.textContent = 'No recent files';
                recentFilesContainer.appendChild(noFilesMsg);
                return;
            }

            // Add recent files to the container (limited to 3)
            recentFiles.forEach(file => {
                const item = document.createElement('div');
                item.className = 'recent-file-item';
                item.dataset.name = file.name;
                item.dataset.id = file.id;

                // Format the date
                const date = new Date(file.lastOpened);
                const now = new Date();
                let dateText;

                if (date.toDateString() === now.toDateString()) {
                    dateText = 'Today';
                } else if (date.toDateString() === new Date(now - 86400000).toDateString()) {
                    dateText = 'Yesterday';
                } else if (now - date < 7 * 86400000) {
                    dateText = 'Last week';
                } else {
                    dateText = date.toLocaleDateString();
                }

                // Create elements instead of using innerHTML
                const iconSpan = document.createElement('span');
                iconSpan.className = 'material-icons recent-file-icon';
                iconSpan.textContent = 'description';

                const nameSpan = document.createElement('span');
                nameSpan.className = 'recent-file-name';
                nameSpan.textContent = file.name;

                const dateSpan = document.createElement('span');
                dateSpan.className = 'recent-file-date';
                dateSpan.textContent = dateText;

                // Append all elements to the item
                item.appendChild(iconSpan);
                item.appendChild(nameSpan);
                item.appendChild(dateSpan);

                // Add click event to open file
                item.addEventListener('click', function() {
                    const fileName = this.querySelector('.recent-file-name').textContent;
                    console.log('Recent file clicked:', fileName);
                    hideModal();
                    openRecentFile(fileName, file.id);
                });

                recentFilesContainer.appendChild(item);
            });
        } catch (error) {
            console.error('Error fixing recent files:', error);
        }
    }

    // Function to open a recent file
    function openRecentFile(fileName, fileId) {
        console.log(`Opening recent file: ${fileName} (ID: ${fileId})`);

        // Try to use the global openRecentFile function if available
        if (window.openRecentFile && typeof window.openRecentFile === 'function') {
            window.openRecentFile(fileName, fileId);
            return;
        }

        // Fallback to using the open file button
        const openFileBtn = document.getElementById('openFileBtn');
        if (openFileBtn) {
            openFileBtn.click();
        }
    }
}
