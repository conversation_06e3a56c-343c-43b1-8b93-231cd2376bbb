// Create globalDefine for editor.main.js
window.globalDefine = function(name, deps, callback) {
    // Handle different callback signatures
    if (typeof callback === 'function') {
        window[name] = callback();
    } else if (typeof deps === 'function') {
        window[name] = deps();
    }
    return window[name];
};

// Make globalDefine available as a global variable for editor.main.js
window.globalThis = window.globalThis || window;
window.globalThis.globalDefine = window.globalDefine;

// No need for module and require polyfills with browser-compatible JSZip
// The UMD version of JSZip handles this automatically
