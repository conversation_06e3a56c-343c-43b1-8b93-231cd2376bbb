/**
 * Handsontable 0.10.2
 * Handsontable is a simple jQuery plugin for editable tables with basic copy-paste compatibility with Excel and Google Docs
 *
 * Copyright 2012, <PERSON><PERSON>
 * Licensed under the MIT license.
 * http://handsontable.com/
 *
 * Date: Thu Jan 23 2014 23:06:23 GMT+0100 (CET)
 *//*jslint white: true, browser: true, plusplus: true, indent: 4, maxerr: 50 */var Handsontable={extension:{},helper:{}};(function(e,t,n){"use strict";function o(){this.refCounter=0,this.init()}function a(){function i(){var e=this;e.registerTimeout("enableObserveChanges",function(){e.updateSettings({observeChanges:!0})},0)}function s(e){return function(t,n){return t[1]===n[1]?0:t[1]===null?1:n[1]===null?-1:t[1]<n[1]?e?-1:1:t[1]>n[1]?e?1:-1:0}}function o(e){return function(t,n){if(t[1]===n[1])return 0;if(t[1]===null)return 1;if(n[1]===null)return-1;var r=new Date(t[1]),i=new Date(n[1]);return r<i?e?-1:1:r>i?e?1:-1:0}}function u(e){return typeof e.sortColumn!="undefined"}var e=this;this.init=function(t){var s=this,o=s.getSettings().columnSorting,u,a;s.sortingEnabled=!!o;if(s.sortingEnabled){s.sortIndex=[];var f=n.call(s);typeof f!="undefined"?(u=f.sortColumn,a=f.sortOrder):(u=o.column,a=o.sortOrder),e.sortByColumn.call(s,u,a),s.sort=function(){var t=Array.prototype.slice.call(arguments);return e.sortByColumn.apply(s,t)},typeof s.getSettings().observeChanges=="undefined"&&i.call(s),t=="afterInit"&&(r.call(s),s.addHook("afterCreateRow",e.afterCreateRow),s.addHook("afterRemoveRow",e.afterRemoveRow),s.addHook("afterLoadData",e.init))}else delete s.sort,s.removeHook("afterCreateRow",e.afterCreateRow),s.removeHook("afterRemoveRow",e.afterRemoveRow),s.removeHook("afterLoadData",e.init)},this.setSortingColumn=function(e,t){var n=this;if(typeof e=="undefined"){delete n.sortColumn,delete n.sortOrder;return}n.sortColumn===e&&typeof t=="undefined"?n.sortOrder=!n.sortOrder:n.sortOrder=typeof t!="undefined"?t:!0,n.sortColumn=e},this.sortByColumn=function(n,r){var i=this;e.setSortingColumn.call(i,n,r);if(typeof i.sortColumn=="undefined")return;i.PluginHooks.run("beforeColumnSort",i.sortColumn,i.sortOrder),e.sort.call(i),i.render(),t.call(i),i.PluginHooks.run("afterColumnSort",i.sortColumn,i.sortOrder)};var t=function(){var e=this,t={};typeof e.sortColumn!="undefined"&&(t.sortColumn=e.sortColumn),typeof e.sortOrder!="undefined"&&(t.sortOrder=e.sortOrder),(t.hasOwnProperty("sortColumn")||t.hasOwnProperty("sortOrder"))&&e.PluginHooks.run("persistentStateSave","columnSorting",t)},n=function(){var e=this,t={};return e.PluginHooks.run("persistentStateLoad","columnSorting",t),t.value},r=function(){function n(){var e=t.view.TBODY.querySelector("tr").querySelectorAll("th");return e.length}function r(e){var r=t.view.wt.wtDom.closest(e,"TH");return t.view.wt.wtDom.index(r)-n()}var t=this;t.rootElement.on("click.handsontable",".columnSorting",function(n){if(t.view.wt.wtDom.hasClass(n.target,"columnSorting")){var i=r(n.target);e.sortByColumn.call(t,i)}})};this.sort=function(){var e=this;if(typeof e.sortOrder=="undefined")return;e.sortingEnabled=!1,e.sortIndex.length=0;var t=this.colOffset();for(var n=0,r=this.countRows()-e.getSettings().minSpareRows;n<r;n++)this.sortIndex.push([n,e.getDataAtCell(n,this.sortColumn+t)]);var i=e.getCellMeta(0,e.sortColumn),u;switch(i.type){case"date":u=o;break;default:u=s}this.sortIndex.sort(u(e.sortOrder));for(var n=this.sortIndex.length;n<e.countRows();n++)this.sortIndex.push([n,e.getDataAtCell(n,this.sortColumn+t)]);e.sortingEnabled=!0},this.translateRow=function(e){var t=this;return t.sortingEnabled&&t.sortIndex&&t.sortIndex.length&&t.sortIndex[e]?t.sortIndex[e][0]:e},this.onBeforeGetSet=function(t){var n=this;t.row=e.translateRow.call(n,t.row)},this.untranslateRow=function(e){var t=this;if(t.sortingEnabled&&t.sortIndex&&t.sortIndex.length)for(var n=0;n<t.sortIndex.length;n++)if(t.sortIndex[n][0]==e)return n},this.getColHeader=function(e,t){this.getSettings().columnSorting&&this.view.wt.wtDom.addClass(t.querySelector(".colHeader"),"columnSorting")},this.afterCreateRow=function(e,n){var r=this;if(!u(r))return;for(var i=0;i<r.sortIndex.length;i++)r.sortIndex[i][0]>=e&&(r.sortIndex[i][0]+=n);for(var i=0;i<n;i++)r.sortIndex.splice(e+i,0,[e+i,r.getData()[e+i][r.sortColumn+r.colOffset()]]);t.call(r)},this.afterRemoveRow=function(n,r){var i=this;if(!u(i))return;var s=e.translateRow.call(i,n);i.sortIndex.splice(n,r);for(var o=0;o<i.sortIndex.length;o++)i.sortIndex[o][0]>s&&(i.sortIndex[o][0]-=r);t.call(i)},this.afterChangeSort=function(t){var n=this,r=!1,i={};if(!t)return;for(var s=0;s<t.length;s++)if(t[s][1]==n.sortColumn){r=!0,i.row=e.translateRow.call(n,t[s][0]),i.col=t[s][1];break}r&&setTimeout(function(){e.sort.call(n),n.render(),n.selectCell(e.untranslateRow.call(n,i.row),i.col)},0)}}function l(){var t,n,r,i,s,o=document.createElement("DIV"),u=o.style;o.className="ghost",u.position="absolute",u.top="25px",u.left=0,u.width="10px",u.height="10px",u.backgroundColor="#CCC",u.opacity=.7;var a=function(){var e=this;e.PluginHooks.run("persistentStateSave","manualColumnPositions",e.manualColumnPositions)},f=function(){var e=this,t={};return e.PluginHooks.run("persistentStateLoad","manualColumnPositions",t),t.value},l=function(){var f=this;f.rootElement.on("mousemove.manualColumnMove",function(e){t&&(u.left=s+e.pageX-i+6+"px",u.display==="none"&&(u.display="block"))}),f.rootElement.on("mouseup.manualColumnMove",function(){t&&(n<r&&r--,f.getSettings().rowHeaders&&(n--,r--),f.manualColumnPositions.splice(r,0,f.manualColumnPositions.splice(n,1)[0]),e(".manualColumnMover.active").removeClass("active"),t=!1,f.forceFullRender=!0,f.view.render(),u.display="none",a.call(f),f.PluginHooks.run("afterColumnMove",n,r))}),f.rootElement.on("mousedown.manualColumnMove",".manualColumnMover",function(e){var a=e.currentTarget,l=f.view.wt.wtDom.closest(a,"TH");n=f.view.wt.wtDom.index(l)+f.colOffset(),r=n,t=!0,i=e.pageX;var c=f.$table[0];c.parentNode.appendChild(o),u.width=f.view.wt.wtDom.outerWidth(l)+"px",u.height=f.view.wt.wtDom.outerHeight(c)+"px",s=parseInt(f.view.wt.wtDom.offset(l).left-f.view.wt.wtDom.offset(c).left,10),u.left=s+6+"px"}),f.rootElement.on("mouseenter.manualColumnMove","td, th",function(){if(t){var e=f.view.THEAD.querySelector(".manualColumnMover.active");e&&f.view.wt.wtDom.removeClass(e,"active"),r=f.view.wt.wtDom.index(this)+f.colOffset();var n=f.view.THEAD.querySelectorAll("th"),i=n[r].querySelector(".manualColumnMover");f.view.wt.wtDom.addClass(i,"active")}}),f.addHook("afterDestroy",c)},c=function(){var e=this;e.rootElement.off("mouseup.manualColumnMove"),e.rootElement.off("mousemove.manualColumnMove"),e.rootElement.off("mousedown.manualColumnMove"),e.rootElement.off("mouseenter.manualColumnMove")};this.beforeInit=function(){this.manualColumnPositions=[]},this.init=function(e){var t=this,n=!!this.getSettings().manualColumnMove;if(n){var r=this.getSettings().manualColumnMove,i=f.call(t);typeof i!="undefined"?this.manualColumnPositions=i:r instanceof Array?this.manualColumnPositions=r:this.manualColumnPositions=[],t.forceFullRender=!0,e=="afterInit"&&(l.call(this),this.manualColumnPositions.length>0&&(this.forceFullRender=!0,this.render()))}else c.call(this),this.manualColumnPositions=[]},this.modifyCol=function(e){return this.getSettings().manualColumnMove?(typeof this.manualColumnPositions[e]=="undefined"&&(this.manualColumnPositions[e]=e),this.manualColumnPositions[e]):e},this.getColHeader=function(e,t){if(this.getSettings().manualColumnMove){var n=document.createElement("DIV");n.className="manualColumnMover",t.firstChild.appendChild(n)}}}function h(){function g(e){s=this,n=e;var t=this.view.wt.wtTable.getCoords(e)[1];if(t>=0){r=t;var i=this.view.wt.wtDom.offset(this.rootElement[0]).left,o=this.view.wt.wtDom.offset(e).left;f=o-i-6,l.style.left=f+parseInt(this.view.wt.wtDom.outerWidth(e),10)+"px",this.rootElement[0].appendChild(l)}}function y(){var e=this;a=parseInt(this.view.wt.wtDom.outerWidth(n),10),e.view.wt.wtDom.addClass(l,"active"),p.height=e.view.wt.wtDom.outerHeight(e.$table[0])+"px",t=e}var t,n,r,i,s,o,u,a,f,l=document.createElement("DIV"),c=document.createElement("DIV"),h=document.createElement("DIV"),p=h.style;l.className="manualColumnResizer",c.className="manualColumnResizerHandle",l.appendChild(c),h.className="manualColumnResizerLine",l.appendChild(h);var d=e(document);d.mousemove(function(e){t&&(i=a+(e.pageX-u),o=w(r,i),l.style.left=f+i+"px")}),d.mouseup(function(){t&&(s.view.wt.wtDom.removeClass(l,"active"),t=!1,o!=a&&(s.forceFullRender=!0,s.view.render(),v.call(s),s.PluginHooks.run("afterColumnResize",r,o)),g.call(s,n))});var v=function(){var e=this;e.PluginHooks.run("persistentStateSave","manualColumnWidths",e.manualColumnWidths)},m=function(){var e=this,t={};return e.PluginHooks.run("persistentStateLoad","manualColumnWidths",t),t.value},b=function(){var e=this,n=0,i=null;this.rootElement.on("mouseenter.handsontable","th",function(n){t||g.call(e,n.currentTarget)}),this.rootElement.on("mousedown.handsontable",".manualColumnResizer",function(){i==null&&(i=setTimeout(function(){n>=2&&(o=e.determineColumnWidth.call(e,r),w(r,o),e.forceFullRender=!0,e.view.render(),e.PluginHooks.run("afterColumnResize",r,o)),n=0,i=null},500)),n++}),this.rootElement.on("mousedown.handsontable",".manualColumnResizer",function(t){u=t.pageX,y.call(e),o=a})};this.beforeInit=function(){this.manualColumnWidths=[]},this.init=function(e){var t=this,n=!!this.getSettings().manualColumnResize;if(n){var r=this.getSettings().manualColumnResize,i=m.call(t);typeof i!="undefined"?this.manualColumnWidths=i:r instanceof Array?this.manualColumnWidths=r:this.manualColumnWidths=[],e=="afterInit"&&(b.call(this),t.forceFullRender=!0,t.render())}};var w=function(e,t){return t=Math.max(t,20),e=s.PluginHooks.execute("modifyCol",e),s.manualColumnWidths[e]=t,t};this.getColWidth=function(e,t){this.getSettings().manualColumnResize&&this.manualColumnWidths[e]&&(t.width=this.manualColumnWidths[e])}}function d(e){var n,r=function(){t.localStorage[e+"__"+"persistentStateKeys"]=JSON.stringify(n)},i=function(){var r=t.localStorage[e+"__"+"persistentStateKeys"],i=typeof r=="string"?JSON.parse(r):void 0;n=i?i:[]},s=function(){n=[],r()};i(),this.saveValue=function(i,s){t.localStorage[e+"_"+i]=JSON.stringify(s),n.indexOf(i)==-1&&(n.push(i),r())},this.loadValue=function(n,r){n=typeof n!="undefined"?n:r;var i=t.localStorage[e+"_"+n];return typeof i=="undefined"?void 0:JSON.parse(i)},this.reset=function(n){t.localStorage.removeItem(e+"_"+n)},this.resetAll=function(){for(var r=0;r<n.length;r++)t.localStorage.removeItem(e+"_"+n[r]);s()}}function v(){this.boundaries=null,this.callback=null}function w(){this.maxOuts=10}function E(t,n){var r;this.instance=t,this.settings=n,this.wtDom=this.instance.wtDom,this.main=document.createElement("div"),r=this.main.style,r.position="absolute",r.top=0,r.left=0;for(var i=0;i<5;i++){var s=document.createElement("DIV");s.className="wtBorder "+(n.className||""),r=s.style,r.backgroundColor=n.border.color,r.height=n.border.width+"px",r.width=n.border.width+"px",this.main.appendChild(s)}this.top=this.main.childNodes[0],this.left=this.main.childNodes[1],this.bottom=this.main.childNodes[2],this.right=this.main.childNodes[3],this.topStyle=this.top.style,this.leftStyle=this.left.style,this.bottomStyle=this.bottom.style,this.rightStyle=this.right.style,this.corner=this.main.childNodes[4],this.corner.className+=" corner",this.cornerStyle=this.corner.style,this.cornerStyle.width="5px",this.cornerStyle.height="5px",this.cornerStyle.border="2px solid #FFF",this.disappear(),t.wtTable.bordersHolder||(t.wtTable.bordersHolder=document.createElement("div"),t.wtTable.bordersHolder.className="htBorders",t.wtTable.hider.appendChild(t.wtTable.bordersHolder)),t.wtTable.bordersHolder.appendChild(this.main);var o=!1,u=e(document.body);u.on("mousedown.walkontable."+t.guid,function(){o=!0}),u.on("mouseup.walkontable."+t.guid,function(){o=!1}),e(this.main.childNodes).on("mouseenter",function(n){if(!o||!t.getSetting("hideBorderOnMouseDownOver"))return;n.preventDefault(),n.stopImmediatePropagation();var r=this.getBoundingClientRect(),i=e(this);i.hide();var s=function(e){if(e.clientY<Math.floor(r.top))return!0;if(e.clientY>Math.ceil(r.top+r.height))return!0;if(e.clientX<Math.floor(r.left))return!0;if(e.clientX>Math.ceil(r.left+r.width))return!0};u.on("mousemove.border."+t.guid,function(e){s(e)&&(u.off("mousemove.border."+t.guid),i.show())})})}function S(){this.offset=0,this.total=0,this.fixedCount=0}function x(e){this.instance=e}function T(){this.cache=[]}function N(){this.countTH=0}function C(e,t,n,r){var i,s=0;x.apply(this,arguments),this.containerSizeFn=t,this.cellSizesSum=0,this.cellSizes=[],this.cellStretch=[],this.cellCount=0,this.remainingSize=0,this.strategy=r;for(;;){i=n(s);if(i===void 0)break;if(this.cellSizesSum>=this.getContainerSize(this.cellSizesSum+i))break;this.cellSizes.push(i),this.cellSizesSum+=i,this.cellCount++,s++}var o=this.getContainerSize(this.cellSizesSum);this.remainingSize=this.cellSizesSum-o}function k(e){var t=this,n=[];this.guid="wt_"+_(),this.wtDom=new A,e.cloneSource?(this.cloneSource=e.cloneSource,this.cloneOverlay=e.cloneOverlay,this.wtSettings=e.cloneSource.wtSettings,this.wtTable=new $(this,e.table),this.wtScroll=new B(this),this.wtViewport=e.cloneSource.wtViewport):(this.wtSettings=new V(this,e),this.wtTable=new $(this,e.table),this.wtScroll=new B(this),this.wtViewport=new J(this),this.wtScrollbars=new W(this),this.wtWheel=new K(this),this.wtEvent=new O(this));if(this.wtTable.THEAD.childNodes.length&&this.wtTable.THEAD.childNodes[0].childNodes.length){for(var r=0,i=this.wtTable.THEAD.childNodes[0].childNodes.length;r<i;r++)n.push(this.wtTable.THEAD.childNodes[0].childNodes[r].innerHTML);this.getSetting("columnHeaders").length||this.update("columnHeaders",[function(e,r){t.wtDom.fastInnerText(r,n[e])}])}this.selections={};var s=this.getSetting("selections");if(s)for(var o in s)s.hasOwnProperty(o)&&(this.selections[o]=new X(this,s[o]));this.drawn=!1,this.drawInterrupted=!1}function L(t){this.instance=t,this.init(),this.clone=this.makeClone("debug"),this.clone.wtTable.holder.style.opacity=.4,this.clone.wtTable.holder.style.textShadow="0 0 2px #ff0000";var n=this,r,i=0,s=0,o=n.clone.wtTable.holder.parentNode;e(document.body).on("mousemove."+this.instance.guid,function(e){if(!n.instance.wtTable.holder.parentNode)return;if(e.clientX-i>-5&&e.clientX-i<5&&e.clientY-s>-5&&e.clientY-s<5)return;i=e.clientX,s=e.clientY,A.prototype.addClass(o,"wtDebugHidden"),A.prototype.removeClass(o,"wtDebugVisible"),clearTimeout(r),r=setTimeout(function(){A.prototype.removeClass(o,"wtDebugHidden"),A.prototype.addClass(o,"wtDebugVisible")},1e3)})}function A(){}function O(t){var n=this;this.instance=t,this.wtDom=this.instance.wtDom;var r=[null,null],i=[null,null],s=function(e){var t=n.parentCell(e.target);n.wtDom.hasClass(e.target,"corner")?n.instance.getSetting("onCellCornerMouseDown",e,e.target):t.TD&&t.TD.nodeName==="TD"&&n.instance.hasSetting("onCellMouseDown")&&n.instance.getSetting("onCellMouseDown",e,t.coords,t.TD),e.button!==2&&t.TD&&t.TD.nodeName==="TD"&&(r[0]=t.TD,clearTimeout(i[0]),i[0]=setTimeout(function(){r[0]=null},1e3))},o,u=function(e){if(n.instance.hasSetting("onCellMouseOver")){var t=n.instance.wtTable.TABLE,r=n.wtDom.closest(e.target,["TD","TH"],t);r&&r!==o&&n.wtDom.isChildOf(r,t)&&(o=r,r.nodeName==="TD"&&n.instance.getSetting("onCellMouseOver",e,n.instance.wtTable.getCoords(r),r))}},a=function(e){if(e.button!==2){var t=n.parentCell(e.target);t.TD===r[0]&&t.TD===r[1]?(n.wtDom.hasClass(e.target,"corner")?n.instance.getSetting("onCellCornerDblClick",e,t.coords,t.TD):t.TD&&n.instance.getSetting("onCellDblClick",e,t.coords,t.TD),r[0]=null,r[1]=null):t.TD===r[0]&&(r[1]=t.TD,clearTimeout(i[1]),i[1]=setTimeout(function(){r[1]=null},500))}};e(this.instance.wtTable.holder).on("mousedown",s),e(this.instance.wtTable.TABLE).on("mouseover",u),e(this.instance.wtTable.holder).on("mouseup",a)}function M(){var e=arguments[0],t=arguments[1];for(var n=1,r=arguments.length/2;n<r;n++)if(e<=arguments[2*n+1]&&t>=arguments[2*n])return!0;return!1}function _(){function e(){return Math.floor((1+Math.random())*65536).toString(16).substring(1)}return e()+e()+e()+e()}function P(){}function H(e,t,n){x.apply(this,arguments),this.containerSizeFn=t,this.sizeAtIndex=n,this.cellSizesSum=0,this.cellSizes=[],this.cellCount=0,this.remainingSize=-Infinity}function B(e){this.instance=e}function j(){}function q(e){this.instance=e,this.init(),this.clone=this.makeClone("corner")}function R(e){this.instance=e,this.type="horizontal",this.cellSize=50,this.init(),this.clone=this.makeClone("left")}function U(e){this.instance=e,this.type="vertical",this.cellSize=23,this.init(),this.clone=this.makeClone("top")}function W(e){this.instance=e,e.getSetting("nativeScrollbars")?(e.update("scrollbarWidth",e.wtDom.getScrollbarWidth()),e.update("scrollbarHeight",e.wtDom.getScrollbarWidth()),this.vertical=new U(e),this.horizontal=new R(e),this.corner=new q(e),e.getSetting("debug")&&(this.debug=new L(e)),this.registerListeners()):(this.vertical=new F(e),this.horizontal=new I(e))}function X(e,t){this.instance=e,this.settings=t,this.selected=[],t.border&&(this.border=new E(e,t))}function V(e,t){var n=this;this.instance=e,this.defaults={table:void 0,debug:!1,scrollH:"auto",scrollV:"auto",nativeScrollbars:!1,stretchH:"hybrid",currentRowClassName:null,currentColumnClassName:null,data:void 0,offsetRow:0,offsetColumn:0,fixedColumnsLeft:0,fixedRowsTop:0,rowHeaders:function(){return[]},columnHeaders:function(){return[]},totalRows:void 0,totalColumns:void 0,width:null,height:null,cellRenderer:function(e,t,r){var i=n.getSetting("data",e,t);n.instance.wtDom.fastInnerText(r,i===void 0||i===null?"":i)},columnWidth:50,selections:null,hideBorderOnMouseDownOver:!1,onCellMouseDown:null,onCellMouseOver:null,onCellDblClick:null,onCellCornerMouseDown:null,onCellCornerDblClick:null,beforeDraw:null,onDraw:null,onScrollVertically:null,onScrollHorizontally:null,scrollbarWidth:10,scrollbarHeight:10},this.settings={};for(var r in this.defaults)if(this.defaults.hasOwnProperty(r))if(t[r]!==void 0)this.settings[r]=t[r];else{if(this.defaults[r]===void 0)throw new Error('A required setting "'+r+'" was not provided');this.settings[r]=this.defaults[r]}}function $(e,t){this.instance=e,this.TABLE=t,this.wtDom=this.instance.wtDom,this.wtDom.removeTextNodes(this.TABLE);var n=this.TABLE.parentNode;if(!n||n.nodeType!==1||!this.wtDom.hasClass(n,"wtHolder")){var r=document.createElement("DIV");r.className="wtSpreader",n&&n.insertBefore(r,this.TABLE),r.appendChild(this.TABLE)}this.spreader=this.TABLE.parentNode,n=this.spreader.parentNode;if(!n||n.nodeType!==1||!this.wtDom.hasClass(n,"wtHolder")){var i=document.createElement("DIV");i.className="wtHider",n&&n.insertBefore(i,this.spreader),i.appendChild(this.spreader)}this.hider=this.spreader.parentNode,this.hiderStyle=this.hider.style,this.hiderStyle.position="relative",n=this.hider.parentNode;if(!n||n.nodeType!==1||!this.wtDom.hasClass(n,"wtHolder")){var s=document.createElement("DIV");s.style.position="relative",s.className="wtHolder",n&&n.insertBefore(s,this.hider),s.appendChild(this.hider)}this.holder=this.hider.parentNode,this.TBODY=this.TABLE.getElementsByTagName("TBODY")[0],this.TBODY||(this.TBODY=document.createElement("TBODY"),this.TABLE.appendChild(this.TBODY)),this.THEAD=this.TABLE.getElementsByTagName("THEAD")[0],this.THEAD||(this.THEAD=document.createElement("THEAD"),this.TABLE.insertBefore(this.THEAD,this.TBODY)),this.COLGROUP=this.TABLE.getElementsByTagName("COLGROUP")[0],this.COLGROUP||(this.COLGROUP=document.createElement("COLGROUP"),this.TABLE.insertBefore(this.COLGROUP,this.THEAD));if(this.instance.getSetting("columnHeaders").length&&!this.THEAD.childNodes.length){var o=document.createElement("TR");this.THEAD.appendChild(o)}this.colgroupChildrenLength=this.COLGROUP.childNodes.length,this.theadChildrenLength=this.THEAD.firstChild?this.THEAD.firstChild.childNodes.length:0,this.tbodyChildrenLength=this.TBODY.childNodes.length,this.oldCellCache=new T,this.currentCellCache=new T,this.rowFilter=new P,this.columnFilter=new N,this.verticalRenderReverse=!1}function J(n){this.instance=n,this.resetSettings();if(this.instance.getSetting("nativeScrollbars")){var r=this;e(t).on("resize",function(){r.clientHeight=r.getWorkspaceHeight()})}}function K(t){if(t.getSetting("nativeScrollbars"))return;e(t.wtTable.spreader).on("mousewheel",function(e,n,r,i){!r&&!i&&n&&(i=n);if(!r&&!i)return;if(i>0&&t.getSetting("offsetRow")===0)return;if(i<0&&t.wtTable.isLastRowFullyVisible())return;if(r<0&&t.getSetting("offsetColumn")===0)return;if(r>0&&t.wtTable.isLastColumnFullyVisible())return;clearTimeout(t.wheelTimeout),t.wheelTimeout=setTimeout(function(){i?t.wtScrollbars.vertical.visible&&t.scrollVertical(-Math.ceil(i)).draw():r&&t.wtScrollbars.horizontal.visible&&t.scrollHorizontal(Math.ceil(r)).draw()},0),e.preventDefault()})}Array.prototype.indexOf||(Array.prototype.indexOf=function(e){var t=this.length>>>0,n=Number(arguments[1])||0;n=n<0?Math.ceil(n):Math.floor(n),n<0&&(n+=t);for(;n<t;n++)if(n in this&&this[n]===e)return n;return-1}),Array.prototype.filter||(Array.prototype.filter=function(e,t){function o(e){return/NodeList/i.test(e.item)}function u(e){var t=[];for(var n=0,r=e.length;n<r;n++)t[n]=e[n];return t}if(typeof this=="undefined"||this===null)throw new TypeError;if(typeof e!="function")throw new TypeError;t=t||this,o(t)&&(t=u(t));var n=t.length,r=[],i,s;for(i=0;i<n;i+=1)t.hasOwnProperty(i)&&(s=t[i],e.call(t,s,i,t)&&r.push(s));return r}),typeof WeakMap=="undefined"&&function(){var e=Object.defineProperty;try{var n=!0;e(function(){},"foo",{})}catch(r){n=!1}var i=+(new Date)%1e9,s=function(){this.name="__st"+(Math.random()*1e9>>>0)+(i++ +"__"),n||(this._wmCache=[])};n?s.prototype={set:function(t,n){var r=t[this.name];r&&r[0]===t?r[1]=n:e(t,this.name,{value:[t,n],writable:!0})},get:function(e){var t;return(t=e[this.name])&&t[0]===e?t[1]:undefined},"delete":function(e){this.set(e,undefined)}}:s.prototype={set:function(e,t){if(typeof e=="undefined"||typeof t=="undefined")return;for(var n=0,r=this._wmCache.length;n<r;n++)if(this._wmCache[n].key==e){this._wmCache[n].value=t;return}this._wmCache.push({key:e,value:t})},get:function(e){if(typeof e=="undefined")return;for(var t=0,n=this._wmCache.length;t<n;t++)if(this._wmCache[t].key==e)return this._wmCache[t].value;return},"delete":function(e){if(typeof e=="undefined")return;for(var t=0,n=this._wmCache.length;t<n;t++)this._wmCache[t].key==e&&Array.prototype.slice.call(this._wmCache,t,1)}},t.WeakMap=s}(),n.activeGuid=null,n.Core=function(i,s){function m(){var e=!1;return{validatorsInQueue:0,addValidatorToQueue:function(){this.validatorsInQueue++,e=!1},removeValidatorFormQueue:function(){this.validatorsInQueue=this.validatorsInQueue-1<0?0:this.validatorsInQueue-1,this.checkIfQueueIsEmpty()},onQueueEmpty:function(){},checkIfQueueIsEmpty:function(){this.validatorsInQueue==0&&e==0&&(e=!0,this.onQueueEmpty())}}}function g(t,n,r){function c(){var i;t.length&&(i=h.PluginHooks.execute("beforeChange",t,n),typeof i=="function"?e.when(result).then(function(){r()}):i===!1&&t.splice(0,t.length)),typeof i!="function"&&r()}var i=new m;i.onQueueEmpty=c;for(var s=t.length-1;s>=0;s--)if(t[s]===null)t.splice(s,1);else{var o=t[s][0],a=u.propToCol(t[s][1]),f=h.runHooksAndReturn("modifyCol",a),l=h.getCellMeta(o,f);l.type==="numeric"&&typeof t[s][3]=="string"&&t[s][3].length>0&&/^-?[\d\s]*\.?\d*$/.test(t[s][3])&&(t[s][3]=numeral().unformat(t[s][3]||"0")),h.getCellValidator(l)&&(i.addValidatorToQueue(),h.validateCell(t[s][3],l,function(e,n){return function(r){if(typeof r!="boolean")throw new Error("Validation error: result is not boolean");r===!1&&n.allowInvalid===!1&&(t.splice(e,1),n.valid=!0,--e),i.removeValidatorFormQueue()}}(s,l),n))}i.checkIfQueueIsEmpty()}function y(e,t){var n=e.length-1;if(n<0)return;for(;0<=n;n--){if(e[n]===null){e.splice(n,1);continue}if(o.settings.minSpareRows)while(e[n][0]>h.countRows()-1)u.createRow();if(h.dataType==="array"&&o.settings.minSpareCols)while(u.propToCol(e[n][1])>h.countCols()-1)u.createCol();u.set(e[n][0],e[n][1],e[n][3])}h.forceFullRender=!0,a.adjustRowsAndCols(),f.refreshBorders(null,!0),h.PluginHooks.run("afterChange",e,t||"edit")}function b(t,n,r){return typeof t=="object"?t:e.isPlainObject(r)?r:[[t,n,r]]}function w(e){if(!e.hasOwnProperty("type"))return;var t,r={};if(typeof e.type=="object")t=e.type;else if(typeof e.type=="string"){t=n.cellTypes[e.type];if(t===void 0)throw new Error('You declared cell type "'+e.type+'" as a string that is not mapped to a known object. Cell type must be an object or a string mapped to an object in Handsontable.cellTypes')}for(var i in t)t.hasOwnProperty(i)&&!e.hasOwnProperty(i)&&(r[i]=t[i]);return r}var o,u,a,f,l,c,h=this,p=function(){};n.helper.extend(p.prototype,r.prototype),n.helper.extend(p.prototype,s),n.helper.extend(p.prototype,w(s)),this.rootElement=i;var d=e(document.documentElement),v=e(document.body);this.guid="ht_"+n.helper.randomString(),this.rootElement[0].id||(this.rootElement[0].id=this.guid),o={cellSettings:[],columnSettings:[],columnsSettingConflicts:["data","width"],settings:new p,settingsFromDOM:{},selStart:new n.SelectionPoint,selEnd:new n.SelectionPoint,isPopulated:null,scrollable:null,extensions:{},firstRun:!0},a={alter:function(e,t,r,i,s){var l;r=r||1;switch(e){case"insert_row":l=u.createRow(t,r),l&&(o.selStart.exists()&&o.selStart.row()>=t?(o.selStart.row(o.selStart.row()+l),f.transformEnd(l,0)):f.refreshBorders());break;case"insert_col":l=u.createCol(t,r);if(l){if(n.helper.isArray(h.getSettings().colHeaders)){var c=[t,0];c.length+=l,Array.prototype.splice.apply(h.getSettings().colHeaders,c)}o.selStart.exists()&&o.selStart.col()>=t?(o.selStart.col(o.selStart.col()+l),f.transformEnd(0,l)):f.refreshBorders()}break;case"remove_row":u.removeRow(t,r),o.cellSettings.splice(t,r),a.adjustRowsAndCols(),f.refreshBorders();break;case"remove_col":u.removeCol(t,r);for(var p=0,d=u.getAll().length;p<d;p++)p in o.cellSettings&&o.cellSettings[p].splice(t,r);n.helper.isArray(h.getSettings().colHeaders)&&(typeof t=="undefined"&&(t=-1),h.getSettings().colHeaders.splice(t,r)),o.columnSettings.splice(t,r),a.adjustRowsAndCols(),f.refreshBorders();break;default:throw new Error('There is no such action "'+e+'"')}s||a.adjustRowsAndCols()},adjustRowsAndCols:function(){var e,t,n=h.countEmptyRows(!0),r;t=h.countRows();if(t<o.settings.minRows)for(e=0;e<o.settings.minRows-t;e++)u.createRow();if(n<o.settings.minSpareRows)for(;n<o.settings.minSpareRows&&h.countRows()<o.settings.maxRows;n++)u.createRow();r=h.countEmptyCols(!0);if(!o.settings.columns&&h.countCols()<o.settings.minCols)for(;h.countCols()<o.settings.minCols;r++)u.createCol();if(!o.settings.columns&&h.dataType==="array"&&r<o.settings.minSpareCols)for(;r<o.settings.minSpareCols&&h.countCols()<o.settings.maxCols;r++)u.createCol();if(o.settings.enterBeginsEditing)for(;(o.settings.minRows||o.settings.minSpareRows)&&h.countRows()>o.settings.minRows&&o.settings.minSpareRows&&n>o.settings.minSpareRows;n--)u.removeRow();if(o.settings.enterBeginsEditing&&!o.settings.columns)for(;(o.settings.minCols||o.settings.minSpareCols)&&h.countCols()>o.settings.minCols&&o.settings.minSpareCols&&r>o.settings.minSpareCols;r--)u.removeCol();var i=h.countRows(),s=h.countCols();(i===0||s===0)&&f.deselect();if(o.selStart.exists()){var a,l=o.selStart.row(),c=o.selStart.col(),p=o.selEnd.row(),d=o.selEnd.col();l>i-1?(l=i-1,a=!0,p>l&&(p=l)):p>i-1&&(p=i-1,a=!0,l>p&&(l=p)),c>s-1?(c=s-1,a=!0,d>c&&(d=c)):d>s-1&&(d=s-1,a=!0,c>d&&(c=d)),a&&h.selectCell(l,c,p,d)}},populateFromArray:function(e,t,r,i,s){var u,a,f,l,c=[],p={};a=t.length;if(a===0)return!1;var d,v,m,g;switch(s){case"shift_down":d=r?r.col-e.col+1:0,v=r?r.row-e.row+1:0,t=n.helper.translateRowsToColumns(t);for(f=0,l=t.length,m=Math.max(l,d);f<m;f++)if(f<l){for(u=0,a=t[f].length;u<v-a;u++)t[f].push(t[f][u%a]);t[f].unshift(e.col+f,e.row,0),h.spliceCol.apply(h,t[f])}else t[f%l][0]=e.col+f,h.spliceCol.apply(h,t[f%l]);break;case"shift_right":d=r?r.col-e.col+1:0,v=r?r.row-e.row+1:0;for(u=0,a=t.length,g=Math.max(a,v);u<g;u++)if(u<a){for(f=0,l=t[u].length;f<d-l;f++)t[u].push(t[u][f%l]);t[u].unshift(e.row+u,e.col,0),h.spliceRow.apply(h,t[u])}else t[u%a][0]=e.row+u,h.spliceRow.apply(h,t[u%a]);break;case"overwrite":default:p.row=e.row,p.col=e.col;for(u=0;u<a;u++){if(r&&p.row>r.row||!o.settings.minSpareRows&&p.row>h.countRows()-1||p.row>=o.settings.maxRows)break;p.col=e.col,l=t[u]?t[u].length:0;for(f=0;f<l;f++){if(r&&p.col>r.col||!o.settings.minSpareCols&&p.col>h.countCols()-1||p.col>=o.settings.maxCols)break;h.getCellMeta(p.row,p.col).readOnly||c.push([p.row,p.col,t[u][f]]),p.col++,r&&f===l-1&&(f=-1)}p.row++,r&&u===a-1&&(u=-1)}h.setDataAtCell(c,null,null,i||"populateFromArray")}},getCornerCoords:function(t){function n(t,n,r){function i(e){return e[r]}return Array.prototype.map?t.apply(Math,n.map(i)):t.apply(Math,e.map(n,i))}return{TL:{row:n(Math.min,t,"row"),col:n(Math.min,t,"col")},BR:{row:n(Math.max,t,"row"),col:n(Math.max,t,"col")}}},getCellsAtCoords:function(e,t){var n=a.getCornerCoords([e,t]),r,i,s=[];for(r=n.TL.row;r<=n.BR.row;r++)for(i=n.TL.col;i<=n.BR.col;i++)s.push(h.view.getCellAtCoords({row:r,col:i}));return s}},this.selection=f={inProgress:!1,begin:function(){h.selection.inProgress=!0},finish:function(){var e=h.getSelected();h.PluginHooks.run("afterSelectionEnd",e[0],e[1],e[2],e[3]),h.PluginHooks.run("afterSelectionEndByProp",e[0],h.colToProp(e[1]),e[2],h.colToProp(e[3])),h.selection.inProgress=!1},isInProgress:function(){return h.selection.inProgress},setRangeStart:function(e){o.selStart.coords(e),f.setRangeEnd(e)},setRangeEnd:function(e,t){h.selection.begin(),o.selEnd.coords(e),o.settings.multiSelect||o.selStart.coords(e),h.view.wt.selections.current.clear(),h.view.wt.selections.current.add(o.selStart.arr()),h.view.wt.selections.area.clear(),f.isMultiple()&&(h.view.wt.selections.area.add(o.selStart.arr()),h.view.wt.selections.area.add(o.selEnd.arr()));if(o.settings.currentRowClassName||o.settings.currentColClassName)h.view.wt.selections.highlight.clear(),h.view.wt.selections.highlight.add(o.selStart.arr()),h.view.wt.selections.highlight.add(o.selEnd.arr());h.PluginHooks.run("afterSelection",o.selStart.row(),o.selStart.col(),o.selEnd.row(),o.selEnd.col()),h.PluginHooks.run("afterSelectionByProp",o.selStart.row(),u.colToProp(o.selStart.col()),o.selEnd.row(),u.colToProp(o.selEnd.col())),t!==!1&&h.view.scrollViewport(e),f.refreshBorders()},refreshBorders:function(e,t){t||l.destroyEditor(e),h.view.render(),f.isSelected()&&!t&&l.prepareEditor()},isMultiple:function(){return o.selEnd.col()!==o.selStart.col()||o.selEnd.row()!==o.selStart.row()},transformStart:function(e,t,n){o.selStart.row()+e>h.countRows()-1?n&&o.settings.minSpareRows>0?h.alter("insert_row",h.countRows()):o.settings.autoWrapCol&&(e=1-h.countRows(),t=o.selStart.col()+t==h.countCols()-1?1-h.countCols():1):o.settings.autoWrapCol&&o.selStart.row()+e<0&&o.selStart.col()+t>=0&&(e=h.countRows()-1,t=o.selStart.col()+t==0?h.countCols()-1:-1),o.selStart.col()+t>h.countCols()-1?n&&o.settings.minSpareCols>0?h.alter("insert_col",h.countCols()):o.settings.autoWrapRow&&(e=o.selStart.row()+e==h.countRows()-1?1-h.countRows():1,t=1-h.countCols()):o.settings.autoWrapRow&&o.selStart.col()+t<0&&o.selStart.row()+e>=0&&(e=o.selStart.row()+e==0?h.countRows()-1:-1,t=h.countCols()-1);var r=h.countRows(),i=h.countCols(),s={row:o.selStart.row()+e,col:o.selStart.col()+t};s.row<0?s.row=0:s.row>0&&s.row>=r&&(s.row=r-1),s.col<0?s.col=0:s.col>0&&s.col>=i&&(s.col=i-1),f.setRangeStart(s)},transformEnd:function(e,t){if(o.selEnd.exists()){var n=h.countRows(),r=h.countCols(),i={row:o.selEnd.row()+e,col:o.selEnd.col()+t};i.row<0?i.row=0:i.row>0&&i.row>=n&&(i.row=n-1),i.col<0?i.col=0:i.col>0&&i.col>=r&&(i.col=r-1),f.setRangeEnd(i)}},isSelected:function(){return o.selEnd.exists()},inInSelection:function(e){if(!f.isSelected())return!1;var t=a.getCornerCoords([o.selStart.coords(),o.selEnd.coords()]);return t.TL.row<=e.row&&t.BR.row>=e.row&&t.TL.col<=e.col&&t.BR.col>=e.col},deselect:function(){if(!f.isSelected())return;h.selection.inProgress=!1,o.selEnd=new n.SelectionPoint,h.view.wt.selections.current.clear(),h.view.wt.selections.area.clear(),l.destroyEditor(),f.refreshBorders(),h.PluginHooks.run("afterDeselect")},selectAll:function(){if(!o.settings.multiSelect)return;f.setRangeStart({row:0,col:0}),f.setRangeEnd({row:h.countRows()-1,col:h.countCols()-1},!1)},empty:function(){if(!f.isSelected())return;var e=a.getCornerCoords([o.selStart.coords(),o.selEnd.coords()]),t,n,r=[];for(t=e.TL.row;t<=e.BR.row;t++)for(n=e.TL.col;n<=e.BR.col;n++)h.getCellMeta(t,n).readOnly||r.push([t,n,""]);h.setDataAtCell(r)}},this.autofill=c=
{handle:null,init:function(){c.handle?c.handle.disabled=!1:c.handle={}},disable:function(){c.handle.disabled=!0},selectAdjacent:function(){var e,t,n,r,i;f.isMultiple()?e=h.view.wt.selections.area.getCorners():e=h.view.wt.selections.current.getCorners(),t=u.getAll();e:for(n=e[2]+1;n<h.countRows();n++){for(i=e[1];i<=e[3];i++)if(t[n][i])break e;if(!!t[n][e[1]-1]||!!t[n][e[3]+1])r=n}r&&(h.view.wt.selections.fill.clear(),h.view.wt.selections.fill.add([e[0],e[1]]),h.view.wt.selections.fill.add([r,e[3]]),c.apply())},apply:function(){var e,t,n,r,i;c.handle.isDragged=0,e=h.view.wt.selections.fill.getCorners();if(!e)return;h.view.wt.selections.fill.clear(),f.isMultiple()?t=h.view.wt.selections.area.getCorners():t=h.view.wt.selections.current.getCorners(),e[0]===t[0]&&e[1]<t[1]?(n={row:e[0],col:e[1]},r={row:e[2],col:t[1]-1}):e[0]===t[0]&&e[3]>t[3]?(n={row:e[0],col:t[3]+1},r={row:e[2],col:e[3]}):e[0]<t[0]&&e[1]===t[1]?(n={row:e[0],col:e[1]},r={row:t[0]-1,col:e[3]}):e[2]>t[2]&&e[1]===t[1]&&(n={row:t[2]+1,col:e[1]},r={row:e[2],col:e[3]}),n&&(i=SheetClip.parse(u.getText(o.selStart.coords(),o.selEnd.coords())),h.PluginHooks.run("beforeAutofill",n,r,i),a.populateFromArray(n,i,r,"autofill"),f.setRangeStart({row:e[0],col:e[1]}),f.setRangeEnd({row:e[2],col:e[3]}))},showBorder:function(e){e.row=e[0],e.col=e[1];var t=a.getCornerCoords([o.selStart.coords(),o.selEnd.coords()]);if(o.settings.fillHandle!=="horizontal"&&(t.BR.row<e.row||t.TL.row>e.row))e=[e.row,t.BR.col];else{if(o.settings.fillHandle==="vertical")return;e=[t.BR.row,e.col]}h.view.wt.selections.fill.clear(),h.view.wt.selections.fill.add([o.selStart.coords().row,o.selStart.coords().col]),h.view.wt.selections.fill.add([o.selEnd.coords().row,o.selEnd.coords().col]),h.view.wt.selections.fill.add(e),h.view.render()}},this.init=function(){h.PluginHooks.run("beforeInit"),this.view=new n.TableView(this),l=new n.EditorManager(h,o,f,u),this.updateSettings(o.settings,!0),this.parseSettingsFromDOM(),this.forceFullRender=!0,this.view.render(),typeof o.firstRun=="object"&&(h.PluginHooks.run("afterChange",o.firstRun[0],o.firstRun[1]),o.firstRun=!1),h.PluginHooks.run("afterInit")},this.validateCell=function(e,t,n,r){var i=h.getCellValidator(t);Object.prototype.toString.call(i)==="[object RegExp]"&&(i=function(e){return function(t,n){n(e.test(t))}}(i)),typeof i=="function"?(e=h.PluginHooks.execute("beforeValidate",e,t.row,t.prop,r),setTimeout(function(){i.call(t,e,function(i){t.valid=i,i=h.PluginHooks.execute("afterValidate",i,e,t.row,t.prop,r),n(i)})})):(t.valid=!0,n(!0))},this.setDataAtCell=function(e,t,n,r){var i=b(e,t,n),s,o,a=[],f;for(s=0,o=i.length;s<o;s++){if(typeof i[s]!="object")throw new Error("Method `setDataAtCell` accepts row number or changes array of arrays as its first parameter");if(typeof i[s][1]!="number")throw new Error("Method `setDataAtCell` accepts row and column number as its parameters. If you want to use object property name, use method `setDataAtRowProp`");f=u.colToProp(i[s][1]),a.push([i[s][0],f,u.get(i[s][0],f),i[s][2]])}!r&&typeof e=="object"&&(r=t),g(a,r,function(){y(a,r)})},this.setDataAtRowProp=function(e,t,n,r){var i=b(e,t,n),s,o,a=[];for(s=0,o=i.length;s<o;s++)a.push([i[s][0],i[s][1],u.get(i[s][0],i[s][1]),i[s][2]]);!r&&typeof e=="object"&&(r=t),g(a,r,function(){y(a,r)})},this.listen=function(){n.activeGuid=h.guid,document.activeElement&&document.activeElement!==document.body?document.activeElement.blur():document.activeElement||document.body.focus()},this.unlisten=function(){n.activeGuid=null},this.isListening=function(){return n.activeGuid===h.guid},this.destroyEditor=function(e){f.refreshBorders(e)},this.populateFromArray=function(e,t,n,r,i,s,o){if(typeof n!="object"||typeof n[0]!="object")throw new Error("populateFromArray parameter `input` must be an array of arrays");return a.populateFromArray({row:e,col:t},n,typeof r=="number"?{row:r,col:i}:null,s,o)},this.spliceCol=function(e,t,n){return u.spliceCol.apply(u,arguments)},this.spliceRow=function(e,t,n){return u.spliceRow.apply(u,arguments)},this.getCornerCoords=function(e){return a.getCornerCoords(e)},this.getSelected=function(){if(f.isSelected())return[o.selStart.row(),o.selStart.col(),o.selEnd.row(),o.selEnd.col()]},this.parseSettingsFromDOM=function(){var e=this.rootElement.css("overflow");if(e==="scroll"||e==="auto")this.rootElement[0].style.overflow="visible",o.settingsFromDOM.overflow=e;else if(o.settings.width===void 0||o.settings.height===void 0)o.settingsFromDOM.overflow="auto";o.settings.width===void 0?o.settingsFromDOM.width=this.rootElement.width():o.settingsFromDOM.width=void 0,o.settingsFromDOM.height=void 0;if(o.settings.height===void 0)if(o.settingsFromDOM.overflow==="scroll"||o.settingsFromDOM.overflow==="auto"){var n=this.rootElement[0].cloneNode(!1),r=this.rootElement[0].parentNode;if(r){n.removeAttribute("id"),r.appendChild(n);var i=parseInt(t.getComputedStyle(n,null).getPropertyValue("height"),10);isNaN(i)&&n.currentStyle&&(i=parseInt(n.currentStyle.height,10)),i>0&&(o.settingsFromDOM.height=i),r.removeChild(n)}}},this.render=function(){h.view&&(h.forceFullRender=!0,h.parseSettingsFromDOM(),f.refreshBorders(null,!0))},this.loadData=function(e){function l(){o.cellSettings.length=0}if(typeof e=="object"&&e!==null){if(!e.push||!e.splice)e=[e]}else{if(e!==null)throw new Error("loadData only accepts array of objects or array of arrays ("+typeof e+" given)");e=[];var t;for(var r=0,i=o.settings.startRows;r<i;r++){t=[];for(var s=0,f=o.settings.startCols;s<f;s++)t.push(null);e.push(t)}}o.isPopulated=!1,p.prototype.data=e,o.settings.dataSchema instanceof Array||e[0]instanceof Array?h.dataType="array":typeof o.settings.dataSchema=="function"?h.dataType="function":h.dataType="object",u=new n.DataMap(h,o,p),l(),a.adjustRowsAndCols(),h.PluginHooks.run("afterLoadData"),o.firstRun?o.firstRun=[null,"loadData"]:(h.PluginHooks.run("afterChange",null,"loadData"),h.render()),o.isPopulated=!0},this.getData=function(e,t,n,r){return typeof e=="undefined"?u.getAll():u.getRange({row:e,col:t},{row:n,col:r},u.DESTINATION_RENDERER)},this.getCopyableData=function(e,t,n,r){return u.getCopyableText({row:e,col:t},{row:n,col:r})},this.updateSettings=function(e,t){var r,i;if(typeof e.rows!="undefined")throw new Error("'rows' setting is no longer supported. do you mean startRows, minRows or maxRows?");if(typeof e.cols!="undefined")throw new Error("'cols' setting is no longer supported. do you mean startCols, minCols or maxCols?");for(r in e){if(r==="data")continue;h.PluginHooks.hooks[r]!==void 0||h.PluginHooks.legacy[r]!==void 0?(typeof e[r]=="function"||n.helper.isArray(e[r]))&&h.PluginHooks.add(r,e[r]):(!t&&e.hasOwnProperty(r)&&(p.prototype[r]=e[r]),n.extension[r]&&(o.extensions[r]=new n.extension[r](h,e[r])))}e.data===void 0&&o.settings.data===void 0?h.loadData(null):e.data!==void 0?h.loadData(e.data):e.columns!==void 0&&u.createMap(),i=h.countCols(),o.cellSettings.length=0;if(i>0){var s,l;for(r=0;r<i;r++)o.columnSettings[r]=n.helper.columnFactory(p,o.columnsSettingConflicts),s=o.columnSettings[r].prototype,p.prototype.columns&&(l=p.prototype.columns[r],n.helper.extend(s,l),n.helper.extend(s,w(l)))}typeof e.fillHandle!="undefined"&&(c.handle&&e.fillHandle===!1?c.disable():!c.handle&&e.fillHandle!==!1&&c.init()),typeof e.className!="undefined"&&(p.prototype.className&&h.rootElement.removeClass(p.prototype.className),e.className&&h.rootElement.addClass(e.className)),t||h.PluginHooks.run("afterUpdateSettings"),a.adjustRowsAndCols(),h.view&&!o.firstRun&&(h.forceFullRender=!0,f.refreshBorders(null,!0))},this.getValue=function(){var e=h.getSelected();if(p.prototype.getValue){if(typeof p.prototype.getValue=="function")return p.prototype.getValue.call(h);if(e)return h.getData()[e[0]][p.prototype.getValue]}else if(e)return h.getDataAtCell(e[0],e[1])},this.getSettings=function(){return o.settings},this.getSettingsFromDOM=function(){return o.settingsFromDOM},this.clear=function(){f.selectAll(),f.empty()},this.alter=function(e,t,n,r,i){a.alter(e,t,n,r,i)},this.getCell=function(e,t){return h.view.getCellAtCoords({row:e,col:t})},this.colToProp=function(e){return u.colToProp(e)},this.propToCol=function(e){return u.propToCol(e)},this.getDataAtCell=function(e,t){return u.get(e,u.colToProp(t))},this.getDataAtRowProp=function(e,t){return u.get(e,t)},this.getDataAtCol=function(e){return[].concat.apply([],u.getRange({row:0,col:e},{row:o.settings.data.length-1,col:e},u.DESTINATION_RENDERER))},this.getDataAtProp=function(e){return[].concat.apply([],u.getRange({row:0,col:u.propToCol(e)},{row:o.settings.data.length-1,col:u.propToCol(e)},u.DESTINATION_RENDERER))},this.getDataAtRow=function(e){return o.settings.data[e]},this.getCellMeta=function(e,t){function a(e){var t={row:e};return h.PluginHooks.execute("beforeGet",t),t.row}function f(e){return n.PluginHooks.execute(h,"modifyCol",e)}var r=u.colToProp(t),i;e=a(e),t=f(t),"undefined"==typeof o.columnSettings[t]&&(o.columnSettings[t]=n.helper.columnFactory(p,o.columnsSettingConflicts)),o.cellSettings[e]||(o.cellSettings[e]=[]),o.cellSettings[e][t]||(o.cellSettings[e][t]=new o.columnSettings[t]),i=o.cellSettings[e][t],i.row=e,i.col=t,i.prop=r,i.instance=h,h.PluginHooks.run("beforeGetCellMeta",e,t,i),n.helper.extend(i,w(i));if(i.cells){var s=i.cells.call(i,e,t,r);s&&(n.helper.extend(i,s),n.helper.extend(i,w(s)))}return h.PluginHooks.run("afterGetCellMeta",e,t,i),i};var E=n.helper.cellMethodLookupFactory("renderer");this.getCellRenderer=function(e,t){var r=E.call(this,e,t);return n.renderers.getRenderer(r)},this.getCellEditor=n.helper.cellMethodLookupFactory("editor"),this.getCellValidator=n.helper.cellMethodLookupFactory("validator"),this.validateCells=function(e){var t=new m;t.onQueueEmpty=e;var n=h.countRows()-1;while(n>=0){var r=h.countCols()-1;while(r>=0)t.addValidatorToQueue(),h.validateCell(h.getDataAtCell(n,r),h.getCellMeta(n,r),function(){t.removeValidatorFormQueue()},"validateCells"),r--;n--}t.checkIfQueueIsEmpty()},this.getRowHeader=function(e){if(e===void 0){var t=[];for(var n=0,r=h.countRows();n<r;n++)t.push(h.getRowHeader(n));return t}return Object.prototype.toString.call(o.settings.rowHeaders)==="[object Array]"&&o.settings.rowHeaders[e]!==void 0?o.settings.rowHeaders[e]:typeof o.settings.rowHeaders=="function"?o.settings.rowHeaders(e):o.settings.rowHeaders&&typeof o.settings.rowHeaders!="string"&&typeof o.settings.rowHeaders!="number"?e+1:o.settings.rowHeaders},this.hasRowHeaders=function(){return!!o.settings.rowHeaders},this.hasColHeaders=function(){if(o.settings.colHeaders!==void 0&&o.settings.colHeaders!==null)return!!o.settings.colHeaders;for(var e=0,t=h.countCols();e<t;e++)if(h.getColHeader(e))return!0;return!1},this.getColHeader=function(e){if(e===void 0){var t=[];for(var r=0,i=h.countCols();r<i;r++)t.push(h.getColHeader(r));return t}return e=n.PluginHooks.execute(h,"modifyCol",e),o.settings.columns&&o.settings.columns[e]&&o.settings.columns[e].title?o.settings.columns[e].title:Object.prototype.toString.call(o.settings.colHeaders)==="[object Array]"&&o.settings.colHeaders[e]!==void 0?o.settings.colHeaders[e]:typeof o.settings.colHeaders=="function"?o.settings.colHeaders(e):o.settings.colHeaders&&typeof o.settings.colHeaders!="string"&&typeof o.settings.colHeaders!="number"?n.helper.spreadsheetColumnLabel(e):o.settings.colHeaders},this._getColWidthFromSettings=function(e){var t=h.getCellMeta(0,e),n=t.width;if(n===void 0||n===o.settings.width)n=t.colWidths;if(n!==void 0&&n!==null){switch(typeof n){case"object":n=n[e];break;case"function":n=n(e)}typeof n=="string"&&(n=parseInt(n,10))}return n},this.getColWidth=function(e){e=n.PluginHooks.execute(h,"modifyCol",e);var t={width:h._getColWidthFromSettings(e)};return t.width||(t.width=50),h.PluginHooks.run("afterGetColWidth",e,t),t.width},this.countRows=function(){return o.settings.data.length},this.countCols=function(){if(h.dataType==="object"||h.dataType==="function")return o.settings.columns&&o.settings.columns.length?o.settings.columns.length:u.colToPropCache.length;if(h.dataType==="array")return o.settings.columns&&o.settings.columns.length?o.settings.columns.length:o.settings.data&&o.settings.data[0]&&o.settings.data[0].length?o.settings.data[0].length:0},this.rowOffset=function(){return h.view.wt.getSetting("offsetRow")},this.colOffset=function(){return h.view.wt.getSetting("offsetColumn")},this.countVisibleRows=function(){return h.view.wt.drawn?h.view.wt.wtTable.rowStrategy.countVisible():-1},this.countVisibleCols=function(){return h.view.wt.drawn?h.view.wt.wtTable.columnStrategy.countVisible():-1},this.countEmptyRows=function(e){var t=h.countRows()-1,n=0;while(t>=0){u.get(t,0);if(h.isEmptyRow(u.getVars.row))n++;else if(e)break;t--}return n},this.countEmptyCols=function(e){if(h.countRows()<1)return 0;var t=h.countCols()-1,n=0;while(t>=0){if(h.isEmptyCol(t))n++;else if(e)break;t--}return n},this.isEmptyRow=function(e){return o.settings.isEmptyRow.call(h,e)},this.isEmptyCol=function(e){return o.settings.isEmptyCol.call(h,e)},this.selectCell=function(e,t,n,r,i){if(typeof e!="number"||e<0||e>=h.countRows())return!1;if(typeof t!="number"||t<0||t>=h.countCols())return!1;if(typeof n!="undefined"){if(typeof n!="number"||n<0||n>=h.countRows())return!1;if(typeof r!="number"||r<0||r>=h.countCols())return!1}return o.selStart.coords({row:e,col:t}),document.activeElement&&document.activeElement!==document.documentElement&&document.activeElement!==document.body&&document.activeElement.blur(),h.listen(),typeof n=="undefined"?f.setRangeEnd({row:e,col:t},i):f.setRangeEnd({row:n,col:r},i),h.selection.finish(),!0},this.selectCellByProp=function(e,t,n,r,i){return arguments[1]=u.propToCol(arguments[1]),typeof arguments[3]!="undefined"&&(arguments[3]=u.propToCol(arguments[3])),h.selectCell.apply(h,arguments)},this.deselectCell=function(){f.deselect()},this.destroy=function(){h.clearTimeouts(),h.view&&h.view.wt.destroy(),h.rootElement.empty(),h.rootElement.removeData("handsontable"),h.rootElement.off(".handsontable"),e(t).off("."+h.guid),d.off("."+h.guid),v.off("."+h.guid),h.PluginHooks.run("afterDestroy")},this.getActiveEditor=function(){return l.getActiveEditor()},this.getInstance=function(){return h.rootElement.data("handsontable")},function(){h.PluginHooks=new n.PluginHookClass;var e=h.PluginHooks.run,t=h.PluginHooks.execute;h.PluginHooks.run=function(t,r,i,s,o,u){e.call(this,h,t,r,i,s,o,u),n.PluginHooks.run(h,t,r,i,s,o,u)},h.PluginHooks.execute=function(e,r,i,s,o,u){var a=n.PluginHooks.execute(h,e,r,i,s,o,u),f=t.call(this,h,e,a,i,s,o,u);return typeof f=="undefined"?a:f},h.addHook=function(){h.PluginHooks.add.apply(h.PluginHooks,arguments)},h.addHookOnce=function(){h.PluginHooks.once.apply(h.PluginHooks,arguments)},h.removeHook=function(){h.PluginHooks.remove.apply(h.PluginHooks,arguments)},h.runHooks=function(){h.PluginHooks.run.apply(h.PluginHooks,arguments)},h.runHooksAndReturn=function(){return h.PluginHooks.execute.apply(h.PluginHooks,arguments)}}(),this.timeouts={},this.registerTimeout=function(e,t,n){clearTimeout(this.timeouts[e]),this.timeouts[e]=setTimeout(t,n||0)},this.clearTimeouts=function(){for(var e in this.timeouts)this.timeouts.hasOwnProperty(e)&&clearTimeout(this.timeouts[e])},this.version="0.10.2"};var r=function(){};r.prototype={data:void 0,width:void 0,height:void 0,startRows:5,startCols:5,rowHeaders:null,colHeaders:null,minRows:0,minCols:0,maxRows:Infinity,maxCols:Infinity,minSpareRows:0,minSpareCols:0,multiSelect:!0,fillHandle:!0,fixedRowsTop:0,fixedColumnsLeft:0,outsideClickDeselects:!0,enterBeginsEditing:!0,enterMoves:{row:1,col:0},tabMoves:{row:0,col:1},autoWrapRow:!1,autoWrapCol:!1,copyRowsLimit:1e3,copyColsLimit:1e3,pasteMode:"overwrite",currentRowClassName:void 0,currentColClassName:void 0,stretchH:"hybrid",isEmptyRow:function(e){var t;for(var n=0,r=this.countCols();n<r;n++){t=this.getDataAtCell(e,n);if(t!==""&&t!==null&&typeof t!="undefined")return!1}return!0},isEmptyCol:function(e){var t;for(var n=0,r=this.countRows();n<r;n++){t=this.getDataAtCell(n,e);if(t!==""&&t!==null&&typeof t!="undefined")return!1}return!0},observeDOMVisibility:!0,allowInvalid:!0,invalidCellClassName:"htInvalid",placeholderCellClassName:"htPlaceholder",readOnlyCellClassName:"htDimmed",fragmentSelection:!1,readOnly:!1,nativeScrollbars:!1,type:"text",copyable:!0,debug:!1},n.DefaultSettings=r,e.fn.handsontable=function(e){var t,r,i,s,o,u=this.first(),a=u.data("handsontable");if(typeof e!="string")return o=e||{},a?a.updateSettings(o):(a=new n.Core(u,o),u.data("handsontable",a),a.init()),u;i=[];if(arguments.length>1)for(t=1,r=arguments.length;t<r;t++)i.push(arguments[t]);if(a){if(typeof a[e]=="undefined")throw new Error("Handsontable do not provide action: "+e);s=a[e].apply(a,i)}return s},n.TableView=function(r){var i=this,s=e(t),o=e(document.documentElement);this.instance=r,this.settings=r.getSettings(),this.settingsFromDOM=r.getSettingsFromDOM(),r.rootElement.data("originalStyle",r.rootElement[0].getAttribute("style")),r.rootElement.addClass("handsontable");var u=document.createElement("TABLE");u.className="htCore",this.THEAD=document.createElement("THEAD"),u.appendChild(this.THEAD),this.TBODY=document.createElement("TBODY"),u.appendChild(this.TBODY),r.$table=e(u),r.rootElement.prepend(r.$table),r.rootElement.on("mousedown.handsontable",function(e){i.isTextSelectionAllowed(e.target)||(f(),e.preventDefault(),t.focus())}),o.on("keyup."+r.guid,function(e){r.selection.isInProgress()&&!e.shiftKey&&r.selection.finish()});var a;o.on("mouseup."+r.guid,function(e){r.selection.isInProgress()&&e.which===1&&r.selection.finish(),a=!1,r.autofill.handle&&r.autofill.handle.isDragged&&(r.autofill.handle.isDragged>1&&r.autofill.apply(),r.autofill.handle.isDragged=0),n.helper.isOutsideInput(document.activeElement)&&r.unlisten()}),o.on("mousedown."+r.guid,function(e){var t=e.target;if(t!==i.wt.wtTable.spreader)while(t!==document.documentElement){if(t===null)return;if(t===r.rootElement[0]||t.nodeName==="HANDSONTABLE-TABLE")return;t=t.parentNode}i.settings.outsideClickDeselects?r.deselectCell():r.destroyEditor()}),r.rootElement.on("mousedown.handsontable",".dragdealer",function(){r.destroyEditor()}),r.$table.on("selectstart",function(e){if(i.settings.fragmentSelection)return;e.preventDefault()});var f=function(){t.getSelection?t.getSelection().empty?t.getSelection().empty():t.getSelection().removeAllRanges&&t.getSelection().removeAllRanges():document.selection&&document.selection.empty()},l={debug:function(){return i.settings.debug},table:u,stretchH:this.settings.stretchH,data:r.getDataAtCell,totalRows:r.countRows,totalColumns:r.countCols,nativeScrollbars:this.settings.nativeScrollbars,offsetRow:0,offsetColumn:0,width:this.getWidth(),height:this.getHeight(),fixedColumnsLeft:function(){return i.settings.fixedColumnsLeft},fixedRowsTop:function(){return i.settings.fixedRowsTop},rowHeaders:function(){return r.hasRowHeaders()?[function(e,t){i.appendRowHeader(e,t)}]:[]},columnHeaders:function(){return r.hasColHeaders()?[function(e,t){i.appendColHeader(e,t)}]:[]},columnWidth:r.getColWidth,cellRenderer:function(e,t,n){var r=i.instance.colToProp(t),s=i.instance.getCellMeta(e,t),o=i.instance.getCellRenderer(s),u=i.instance.getDataAtRowProp(e,r);o(i.instance,n,e,t,r,u,s),i.instance.PluginHooks.run("afterRenderer",n,e,t,r,u,s)},selections:{current:{className:"current",border:{width:2,color:"#5292F7",style:"solid",cornerVisible:function(){return i.settings.fillHandle&&!i.isCellEdited()&&!r.selection.isMultiple()}}},area:{className:"area",border:{width:1,color:"#89AFF9",style:"solid",cornerVisible:function(){return i.settings.fillHandle&&!i.isCellEdited()&&r.selection.isMultiple()}}},highlight:{highlightRowClassName:i.settings.currentRowClassName,highlightColumnClassName:i.settings.currentColClassName},fill:{className:"fill",border:{width:1,color:"red",style:"solid"}}},hideBorderOnMouseDownOver:function(){return i.settings.fragmentSelection},onCellMouseDown:function(e,t,n){r.listen(),a=!0;var i={row:t[0],col:t[1]};if(e.button!==2||!r.selection.inInSelection(i))e.shiftKey?r.selection.setRangeEnd(i):r.selection.setRangeStart(i);r.PluginHooks.run("afterOnCellMouseDown",e,t,n)},onCellMouseOver:function(e,t,n){var i={row:t[0],col:t[1]};a?r.selection.setRangeEnd(i):r.autofill.handle&&r.autofill.handle.isDragged&&(r.autofill.handle.isDragged++,r.autofill.showBorder(t)),r.PluginHooks.run("afterOnCellMouseOver",e,t,n)},onCellCornerMouseDown:function(e){r.autofill.handle.isDragged=1,e.preventDefault(),r.PluginHooks.run("afterOnCellCornerMouseDown",e)},onCellCornerDblClick:function(){r.autofill.selectAdjacent()},beforeDraw:function(e){i.beforeRender(e)},onDraw:function(e){i.onDraw(e)},onScrollVertically:function(){r.runHooks("afterScrollVertically")},onScrollHorizontally:function(){r.runHooks("afterScrollHorizontally")}};r.PluginHooks.run("beforeInitWalkontable",l),this.wt=new k(l),s.on("resize."+r.guid,function(){r.registerTimeout("resizeTimeout",function(){r.parseSettingsFromDOM();var e=i.getWidth(),t=i.getHeight();if(l.width!==e||l.height!==t)r.forceFullRender=!0,i.render(),l.width=e,l.height=t},60)}),e(i.wt.wtTable.spreader).on("mousedown.handsontable, contextmenu.handsontable",function(e){e.target===i.wt.wtTable.spreader&&e.which===3&&e.stopPropagation()}),o.on("click."+r.guid,function(){i.settings.observeDOMVisibility&&i.wt.drawInterrupted&&(i.instance.forceFullRender=!0,i.render())})},n.TableView.prototype.isTextSelectionAllowed=function(e){return n.helper.isInput(e)?!0:this.settings.fragmentSelection&&this.wt.wtDom.isChildOf(e,this.TBODY)?!0:!1},n.TableView.prototype.isCellEdited=function(){var e=this.instance.getActiveEditor();return e&&e.isOpened()},n.TableView.prototype.getWidth=function(){var e=this.settings.width!==void 0?this.settings.width:this.settingsFromDOM.width;return typeof e=="function"?e():e},n.TableView.prototype.getHeight=function(){var e=this.settings.height!==void 0?this.settings.height:this.settingsFromDOM.height;return typeof e=="function"?e():e},n.TableView.prototype.beforeRender=function(e){e&&(this.instance.PluginHooks.run("beforeRender",this.instance.forceFullRender),this.wt.update("width",this.getWidth()),this.wt.update("height",this.getHeight()))},n.TableView.prototype.onDraw=function(e){e&&this.instance.PluginHooks.run("afterRender",this.instance.forceFullRender)},n.TableView.prototype.render=function(){this.wt.draw(!this.instance.forceFullRender),this.instance.forceFullRender=!1,this.instance.rootElement.triggerHandler("render.handsontable")},n.TableView.prototype.getCellAtCoords=function(e){var t=this.wt.wtTable.getCell([e.row,e.col]);return t<0?null:t},n.TableView.prototype.scrollViewport=function(e){this.wt.scrollViewport([e.row,e.col])},n.TableView.prototype.appendRowHeader=function(e,t){if(e>-1)this.wt.wtDom.fastInnerHTML(t,this.instance.getRowHeader(e));else{var n=document.createElement("DIV");n.className="relative",this.wt.wtDom.fastInnerText(n," "),this.wt.wtDom.empty(t),t.appendChild(n)}},n.TableView.prototype.appendColHeader=function(e,t){var n=document.createElement("DIV"),r=document.createElement("SPAN");n.className="relative",r.className="colHeader",this.wt.wtDom.fastInnerHTML(r,this.instance.getColHeader(e)),n.appendChild(r),this.wt.wtDom.empty(t),t.appendChild(n),this.instance.PluginHooks.run("afterGetColHeader",e,t)},n.TableView.prototype.maximumVisibleElementWidth=function(e){var t=this.wt.wtViewport.getWorkspaceWidth();return this.settings.nativeScrollbars?t:t-e},n.TableView.prototype.maximumVisibleElementHeight=function(e){var t=this.wt.wtViewport.getWorkspaceHeight();return this.settings.nativeScrollbars?t:t-e},function(e){function t(e){var t,n;n={},t=e,this.getInstance=function(e){return e.guid in n||(n[e.guid]=new t(e)),n[e.guid]}}var n={},r=new WeakMap;e.editors={registerEditor:function(e,i){var s=new t(i);typeof e=="string"&&(n[e]=s),r.set(i,s)},getEditor:function(e,t){var i;if(typeof e=="function")r.get(e)||this.registerEditor(null,e),i=r.get(e);else{if(typeof e!="string")throw Error('Only strings and functions can be passed as "editor" parameter ');i=n[e]}if(!i)throw Error('No editor registered under name "'+e+'"');return i.getInstance(t)}}}(n),function(t){t.EditorManager=function(n,r,i){var s=this,o=e(document),u=t.helper.keyCode,a,f=function(){function e(e){if(!n.isListening())return;r.settings.beforeOnKeyDown&&r.settings.beforeOnKeyDown.call(n,e),n.PluginHooks.run("beforeKeyDown",e);if(!e.isImmediatePropagationStopped()){r.lastKeyCode=e.keyCode;if(i.isSelected()){var o=(e.ctrlKey||e.metaKey)&&!e.altKey;if(!a.isWaiting()&&!t.helper.isMetaKey(e.keyCode)&&!o){s.openEditor(""),e.stopPropagation();return}var f=e.shiftKey?i.setRangeEnd:i.setRangeStart;switch(e.keyCode){case u.A:if(o){i.selectAll(),e.preventDefault(),e.stopPropagation();break};case u.ARROW_UP:s.isEditorOpened()&&!a.isWaiting()&&s.closeEditorAndSaveChanges(o),c(e.shiftKey),e.preventDefault(),e.stopPropagation();break;case u.ARROW_DOWN:s.isEditorOpened()&&!a.isWaiting()&&s.closeEditorAndSaveChanges(o),h(e.shiftKey),e.preventDefault(),e.stopPropagation();break;case u.ARROW_RIGHT:s.isEditorOpened()&&!a.isWaiting()&&s.closeEditorAndSaveChanges(o),p(e.shiftKey),e.preventDefault(),e.stopPropagation();break;case u.ARROW_LEFT:s.isEditorOpened()&&!a.isWaiting()&&s.closeEditorAndSaveChanges(o),d(e.shiftKey),e.preventDefault(),e.stopPropagation();break;case u.TAB:var v=typeof r.settings.tabMoves=="function"?r.settings.tabMoves(e):r.settings.tabMoves;e.shiftKey?i.transformStart(-v.row,-v.col):i.transformStart(v.row,v.col,!0),e.preventDefault(),e.stopPropagation();break;case u.BACKSPACE:case u.DELETE:i.empty(e),s.prepareEditor(),e.preventDefault();break;case u.F2:s.openEditor(),e.preventDefault();break;case u.ENTER:s.isEditorOpened()?(a.state!==t.EditorState.WAITING&&s.closeEditorAndSaveChanges(o),l(e.shiftKey)):n.getSettings().enterBeginsEditing?s.openEditor():l(e.shiftKey),e.preventDefault(),e.stopImmediatePropagation();break;case u.ESCAPE:s.isEditorOpened()&&s.closeEditorAndRestoreOriginalValue(o),e.preventDefault();break;case u.HOME:e.ctrlKey||e.metaKey?f({row:0,col:r.selStart.col()}):f({row:r.selStart.row(),col:0}),e.preventDefault(),e.stopPropagation();break;case u.END:e.ctrlKey||e.metaKey?f({row:n.countRows()-1,col:r.selStart.col()}):f({row:r.selStart.row(),col:n.countCols()-1}),e.preventDefault(),e.stopPropagation();break;case u.PAGE_UP:i.transformStart(-n.countVisibleRows(),0),n.view.wt.scrollVertical(-n.countVisibleRows()),n.view.render(),e.preventDefault(),e.stopPropagation();break;case u.PAGE_DOWN:i.transformStart(n.countVisibleRows(),0),n.view.wt.scrollVertical(n.countVisibleRows()),n.view.render(),e.preventDefault(),e.stopPropagation();break;default:}}}}function f(){s.openEditor()}function l(e){var t=typeof r.settings.enterMoves=="function"?r.settings.enterMoves(event):r.settings.enterMoves;e?i.transformStart(-t.row,-t.col):i.transformStart(t.row,t.col,!0)}function c(e){e?i.transformEnd(-1,0):i.transformStart(-1,0)}function h(e){e?i.transformEnd(1,0):i.transformStart(1,0)}function p(e){e?i.transformEnd(0,1):i.transformStart(0,1)}function d(e){e?i.transformEnd(0,-1):i.transformStart(0,-1)}o.on("keydown.handsontable."+n.guid,e),n.view.wt.update("onCellDblClick",f),n.addHook("afterDestroy",function(){o.off("keydown.handsontable."+n.guid)})};this.destroyEditor=function(e){this.closeEditor(e)},this.getActiveEditor=function(){return a},this.prepareEditor=function(){if(a&&a.isWaiting()){this.closeEditor(!1,!1,function(e){e&&s.prepareEditor()});return}var e=r.selStart.row(),i=r.selStart.col(),o=n.colToProp(i),u=n.getCell(e,i),f=n.getDataAtCell(e,i),l=n.getCellMeta(e,i),c=n.getCellEditor(l);a=t.editors.getEditor(c,n),a.prepare(e,i,o,u,f,l)},this.isEditorOpened=function(){return a.isOpened()},this.openEditor=function(e){a.beginEditing(e)},this.closeEditor=function(e,t,n){a?a.finishEditing(e,t,n):n&&n(!1)},this.closeEditorAndSaveChanges=function(e){return this.closeEditor(!1,e)},this.closeEditorAndRestoreOriginalValue=function(e){return this.closeEditor(!0,e)},f()}}(n),function(e){var t={};e.renderers={registerRenderer:function(e,n){t[e]=n},getRenderer:function(e){if(typeof e=="function")return e;if(typeof e!="string")throw Error('Only strings and functions can be passed as "renderer" parameter ');if(e in t)return t[e];throw Error('No editor registered under name "'+e+'"')}}}(n),n.Dom=new A,n.helper.isPrintableChar=function(e){return e==32||e>=48&&e<=57||e>=96&&e<=111||e>=186&&e<=192||e>=219&&e<=222||e>=226||e>=65&&e<=90},n.helper.isMetaKey=function(e){var t=n.helper.keyCode,r=[t.ARROW_DOWN,t.ARROW_UP,t.ARROW_LEFT,t.ARROW_RIGHT,t.HOME,t.END,t.DELETE,t.BACKSPACE,t.F1,t.F2,t.F3,t.F4,t.F5,t.F6,t.F7,t.F8,t.F9,t.F10,t.F11,t.F12,t.TAB,t.PAGE_DOWN,t.PAGE_UP,t.ENTER,t.ESCAPE,t.SHIFT,t.CAPS_LOCK,t.ALT];return r.indexOf(e)!=-1},n.helper.isCtrlKey=function(e){var t=n.helper.keyCode;return[t.CONTROL_LEFT,224,t.COMMAND_LEFT,t.COMMAND_RIGHT].indexOf(e)!=-1},n.helper.stringify=function(e){switch(typeof e){case"string":case"number":return e+"";case"object":return e===null?"":e.toString();case"undefined":return"";default:return e.toString()}},n.helper.spreadsheetColumnLabel=function(e){var t=e+1,n="",r;while(t>0)r=(t-1)%26,n=String.fromCharCode(65+r)+n,t=parseInt((t-r)/26,10);return n},n.helper.isNumeric=function(e){var t=typeof e;return t=="number"?!isNaN(e)&&isFinite(e):t=="string"?e.length?e.length==1?/\d/.test(e):/^\s*[+-]?\s*(?:(?:\d+(?:\.\d+)?(?:e[+-]?\d+)?)|(?:0x[a-f\d]+))\s*$/i.test(e):!1:t=="object"?!!e&&typeof e.valueOf()=="number"&&!(e instanceof Date):!1},n.helper.isArray=function(e){return Object.prototype.toString.call(e).match(/array/i)!==null},n.helper.isDescendant=function(e,t){var n=t.parentNode;while(n!=null){if(n==e)return!0;n=n.parentNode}return!1},n.helper.randomString=function(){return _()},n.helper.inherit=function(e,t){return t.prototype.constructor=t,e.prototype=new t,e.prototype.constructor=e,e},n.helper.extend=function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},n.helper.getPrototypeOf=function(e){var t;if(typeof e.__proto__=="object")t=e.__proto__;else{var n,r=e.constructor;typeof e.constructor=="function"&&(n=r,delete e.constructor&&(r=e.constructor,e.constructor=n)),t=r?r.prototype:null}return t},n.helper.columnFactory=function(e,t){function r(){}n.helper.inherit(r,e);for(var i=0,s=t.length;i<s;i++)r.prototype[t[i]]=void 0;return r},n.helper.translateRowsToColumns=function(e){var t,n,r,i,s=[],o=0;for(t=0,n=e.length;t<n;t++)for(r=0,i=e[t].length;r<i;r++)r==o&&(s.push([]),o++),s[r].push(e[t][r]);return s},n.helper.to2dArray=function(e){var t=0,n=e.length;while(t<n)e[t]=[e[t]],t++},n.helper.extendArray=function(e,t){var n=0,r=t.length;while(n<r)e.push(t[n]),n++},n.helper.isInput=function(e){var t=["INPUT","SELECT","TEXTAREA"];return t.indexOf(e.nodeName)>-1},n.helper.isOutsideInput=function(e){return n.helper.isInput(e)&&e.className.indexOf("handsontableInput")==-1},n.helper.keyCode={MOUSE_LEFT:1,MOUSE_RIGHT:3,MOUSE_MIDDLE:2,BACKSPACE:8,COMMA:188,DELETE:46,END:35,ENTER:13,ESCAPE:27,CONTROL_LEFT:91,COMMAND_LEFT:17,COMMAND_RIGHT:93,ALT:18,HOME:36,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,SPACE:32,SHIFT:16,CAPS_LOCK:20,TAB:9,ARROW_RIGHT:39,ARROW_LEFT:37,ARROW_UP:38,ARROW_DOWN:40,F1:112,F2:113,F3:114,F4:115,F5:116,F6:117,F7:118,F8:119,F9:120,F10:121,F11:122,F12:123,A:65,X:88,C:67,V:86},n.helper.isObject=function(e){return Object.prototype.toString.call(e)=="[object Object]"},n.helper.isArray=function(e){return Array.isArray?Array.isArray(e):Object.prototype.toString.call(e)=="[object Array]"},n.helper.pivot=function(e){var t=[];if(!e||e.length==0||!e[0]||e[0].length==0)return t;var n=e.length,r=e[0].length;for(var i=0;i<n;i++)for(var s=0;s<r;s++)t[s]||(t[s]=[]),t[s][i]=e[i][s];return t},n.helper.proxy=function(e,t){return function(){return e.apply(t,arguments)}},n.helper.cellMethodLookupFactory=function(e){function t(e){var t=n.cellTypes[e];if(typeof t=="undefined")throw new Error('You declared cell type "'+e+'" as a string that is not mapped to a known object. Cell type must be an object or a string mapped to an object in Handsontable.cellTypes');return t}return function(i,s){return function o(r){if(!r)return;if(r.hasOwnProperty(e)&&r[e])return r[e];if(r.hasOwnProperty("type")&&r.type){var i;if(typeof r.type!="string")throw new Error("Cell type must be a string ");return i=t(r.type),i[e]}return o(n.helper.getPrototypeOf(r))}(typeof i=="number"?this.getCellMeta(i,s):i)}},n.SelectionPoint=function(){this._row=null,this._col=null},n.SelectionPoint.prototype.exists=function(){return this._row!==null},n.SelectionPoint.prototype.row=function(e){return e!==void 0&&(this._row=e),this._row},n.SelectionPoint.prototype.col=function(e){return e!==void 0&&(this._col=e),this._col},n.SelectionPoint.prototype.coords=function(e){return e!==void 0&&(this._row=e.row,this._col=e.col),{row:this._row,col:
this._col}},n.SelectionPoint.prototype.arr=function(e){return e!==void 0&&(this._row=e[0],this._col=e[1]),[this._row,this._col]},function(t){t.DataMap=function(e,t,n){this.instance=e,this.priv=t,this.GridSettings=n,this.dataSource=this.instance.getSettings().data,this.dataSource[0]?this.duckSchema=this.recursiveDuckSchema(this.dataSource[0]):this.duckSchema={},this.createMap(),this.getVars={},this.setVars={}},t.DataMap.prototype.DESTINATION_RENDERER=1,t.DataMap.prototype.DESTINATION_CLIPBOARD_GENERATOR=2,t.DataMap.prototype.recursiveDuckSchema=function(t){var n;if(e.isPlainObject(t)){n={};for(var r in t)t.hasOwnProperty(r)&&(e.isPlainObject(t[r])?n[r]=this.recursiveDuckSchema(t[r]):n[r]=null)}else n=[];return n},t.DataMap.prototype.recursiveDuckColumns=function(t,n,r){var i,s;typeof n=="undefined"&&(n=0,r="");if(e.isPlainObject(t))for(s in t)t.hasOwnProperty(s)&&(t[s]===null?(i=r+s,this.colToPropCache.push(i),this.propToColCache[i]=n,n++):n=this.recursiveDuckColumns(t[s],n,s+"."));return n},t.DataMap.prototype.createMap=function(){if(typeof this.getSchema()=="undefined")throw new Error("trying to create `columns` definition but you didnt' provide `schema` nor `data`");var e,t,n=this.getSchema();this.colToPropCache=[],this.propToColCache={};var r=this.instance.getSettings().columns;if(r)for(e=0,t=r.length;e<t;e++)this.colToPropCache[e]=r[e].data,this.propToColCache[r[e].data]=e;else this.recursiveDuckColumns(n)},t.DataMap.prototype.colToProp=function(e){return e=t.PluginHooks.execute(this.instance,"modifyCol",e),this.colToPropCache&&typeof this.colToPropCache[e]!="undefined"?this.colToPropCache[e]:e},t.DataMap.prototype.propToCol=function(e){var n;return typeof this.propToColCache[e]!="undefined"?n=this.propToColCache[e]:n=e,n=t.PluginHooks.execute(this.instance,"modifyCol",n),n},t.DataMap.prototype.getSchema=function(){var e=this.instance.getSettings().dataSchema;return e?typeof e=="function"?e():e:this.duckSchema},t.DataMap.prototype.createRow=function(t,n){var r,i=this.instance.countCols(),s=0,o;n||(n=1);if(typeof t!="number"||t>=this.instance.countRows())t=this.instance.countRows();o=t;var u=this.instance.getSettings().maxRows;while(s<n&&this.instance.countRows()<u){if(this.instance.dataType==="array"){r=[];for(var a=0;a<i;a++)r.push(null)}else this.instance.dataType==="function"?r=this.instance.getSettings().dataSchema(t):r=e.extend(!0,{},this.getSchema());t===this.instance.countRows()?this.dataSource.push(r):this.dataSource.splice(t,0,r),s++,o++}return this.instance.PluginHooks.run("afterCreateRow",t,s),this.instance.forceFullRender=!0,s},t.DataMap.prototype.createCol=function(e,n){if(this.instance.dataType==="object"||this.instance.getSettings().columns)throw new Error("Cannot create new column. When data source in an object, you can only have as much columns as defined in first data row, data schema or in the 'columns' setting.If you want to be able to add new columns, you have to use array datasource.");var r=this.instance.countRows(),i=this.dataSource,s,o=0,u;n||(n=1),u=e;var a=this.instance.getSettings().maxCols;while(o<n&&this.instance.countCols()<a){s=t.helper.columnFactory(this.GridSettings,this.priv.columnsSettingConflicts);if(typeof e!="number"||e>=this.instance.countCols()){for(var f=0;f<r;f++)typeof i[f]=="undefined"&&(i[f]=[]),i[f].push(null);this.priv.columnSettings.push(s)}else{for(var f=0;f<r;f++)i[f].splice(u,0,null);this.priv.columnSettings.splice(u,0,s)}o++,u++}return this.instance.PluginHooks.run("afterCreateCol",e,o),this.instance.forceFullRender=!0,o},t.DataMap.prototype.removeRow=function(e,t){t||(t=1),typeof e!="number"&&(e=-t),e=(this.instance.countRows()+e)%this.instance.countRows();var n=this.physicalRowsToLogical(e,t),r=this.instance.PluginHooks.execute("beforeRemoveRow",e,t);if(r===!1)return;var i=this.dataSource,s=i.filter(function(e,t){return n.indexOf(t)==-1});i.length=0,Array.prototype.push.apply(i,s),this.instance.PluginHooks.run("afterRemoveRow",e,t),this.instance.forceFullRender=!0},t.DataMap.prototype.removeCol=function(e,t){if(this.instance.dataType==="object"||this.instance.getSettings().columns)throw new Error("cannot remove column with object data source or columns option specified");t||(t=1),typeof e!="number"&&(e=-t),e=(this.instance.countCols()+e)%this.instance.countCols();var n=this.instance.PluginHooks.execute("beforeRemoveCol",e,t);if(n===!1)return;var r=this.dataSource;for(var i=0,s=this.instance.countRows();i<s;i++)r[i].splice(e,t);this.priv.columnSettings.splice(e,t),this.instance.PluginHooks.run("afterRemoveCol",e,t),this.instance.forceFullRender=!0},t.DataMap.prototype.spliceCol=function(e,n,r){var i=4<=arguments.length?[].slice.call(arguments,3):[],s=this.instance.getDataAtCol(e),o=s.slice(n,n+r),u=s.slice(n+r);t.helper.extendArray(i,u);var a=0;while(a<r)i.push(null),a++;return t.helper.to2dArray(i),this.instance.populateFromArray(n,e,i,null,null,"spliceCol"),o},t.DataMap.prototype.spliceRow=function(e,n,r){var i=4<=arguments.length?[].slice.call(arguments,3):[],s=this.instance.getDataAtRow(e),o=s.slice(n,n+r),u=s.slice(n+r);t.helper.extendArray(i,u);var a=0;while(a<r)i.push(null),a++;return this.instance.populateFromArray(e,n,[i],null,null,"spliceRow"),o},t.DataMap.prototype.get=function(e,t){this.getVars.row=e,this.getVars.prop=t,this.instance.PluginHooks.run("beforeGet",this.getVars);if(typeof this.getVars.prop=="string"&&this.getVars.prop.indexOf(".")>-1){var n=this.getVars.prop.split("."),r=this.dataSource[this.getVars.row];if(!r)return null;for(var i=0,s=n.length;i<s;i++){r=r[n[i]];if(typeof r=="undefined")return null}return r}return typeof this.getVars.prop=="function"?this.getVars.prop(this.dataSource.slice(this.getVars.row,this.getVars.row+1)[0]):this.dataSource[this.getVars.row]?this.dataSource[this.getVars.row][this.getVars.prop]:null};var n=t.helper.cellMethodLookupFactory("copyable");t.DataMap.prototype.getCopyable=function(e,t){return n.call(this.instance,e,this.propToCol(t))?this.get(e,t):""},t.DataMap.prototype.set=function(e,t,n,r){this.setVars.row=e,this.setVars.prop=t,this.setVars.value=n,this.instance.PluginHooks.run("beforeSet",this.setVars,r||"datamapGet");if(typeof this.setVars.prop=="string"&&this.setVars.prop.indexOf(".")>-1){var i=this.setVars.prop.split("."),s=this.dataSource[this.setVars.row];for(var o=0,u=i.length-1;o<u;o++)s=s[i[o]];s[i[o]]=this.setVars.value}else typeof this.setVars.prop=="function"?this.setVars.prop(this.dataSource.slice(this.setVars.row,this.setVars.row+1)[0],this.setVars.value):this.dataSource[this.setVars.row][this.setVars.prop]=this.setVars.value},t.DataMap.prototype.physicalRowsToLogical=function(e,t){var n=this.instance.countRows(),r=(n+e)%n,i=[],s=t;while(r<n&&s)this.get(r,0),i.push(this.getVars.row),s--,r++;return i},t.DataMap.prototype.clear=function(){for(var e=0;e<this.instance.countRows();e++)for(var t=0;t<this.instance.countCols();t++)this.set(e,this.colToProp(t),"")},t.DataMap.prototype.getAll=function(){return this.dataSource},t.DataMap.prototype.getRange=function(e,t,n){var r,i,s,o,u=[],a,f=n===this.DESTINATION_CLIPBOARD_GENERATOR?this.getCopyable:this.get;i=Math.max(e.row,t.row),o=Math.max(e.col,t.col);for(r=Math.min(e.row,t.row);r<=i;r++){a=[];for(s=Math.min(e.col,t.col);s<=o;s++)a.push(f.call(this,r,this.colToProp(s)));u.push(a)}return u},t.DataMap.prototype.getText=function(e,t){return SheetClip.stringify(this.getRange(e,t,this.DESTINATION_RENDERER))},t.DataMap.prototype.getCopyableText=function(e,t){return SheetClip.stringify(this.getRange(e,t,this.DESTINATION_CLIPBOARD_GENERATOR))}}(n),function(e){e.renderers.cellDecorator=function(e,t,n,r,i,s,o){o.readOnly&&e.view.wt.wtDom.addClass(t,o.readOnlyCellClassName),o.valid===!1&&o.invalidCellClassName&&e.view.wt.wtDom.addClass(t,o.invalidCellClassName),!s&&o.placeholder&&e.view.wt.wtDom.addClass(t,o.placeholderCellClassName)}}(n),function(e){var t=function(t,n,r,i,s,o,u){e.renderers.cellDecorator.apply(this,arguments),!o&&u.placeholder&&(o=u.placeholder);var a=e.helper.stringify(o);if(u.rendererTemplate){t.view.wt.wtDom.empty(n);var f=document.createElement("TEMPLATE");f.setAttribute("bind","{{}}"),f.innerHTML=u.rendererTemplate,HTMLTemplateElement.decorate(f),f.model=t.getDataAtRow(r),n.appendChild(f)}else t.view.wt.wtDom.fastInnerText(n,a)};e.renderers.TextRenderer=t,e.renderers.registerRenderer("text",t)}(n),function(e){var t=document.createElement("DIV");t.className="htAutocompleteWrapper";var n=document.createElement("DIV");n.className="htAutocompleteArrow",n.appendChild(document.createTextNode("▼"));var r=function(t,n){n.innerHTML=t.innerHTML,e.Dom.empty(t),t.appendChild(n)},i=function(r,i,s,o,u,a,f){var l=t.cloneNode(!0),c=n.cloneNode(!0);e.renderers.TextRenderer(r,i,s,o,u,a,f),i.appendChild(c),e.Dom.addClass(i,"htAutocomplete"),i.firstChild||i.appendChild(document.createTextNode(" ")),r.acArrowListener||(r.acArrowListener=function(){r.view.wt.getSetting("onCellDblClick")},r.rootElement.on("mousedown",".htAutocompleteArrow",r.acArrowListener))};e.AutocompleteRenderer=i,e.renderers.AutocompleteRenderer=i,e.renderers.registerRenderer("autocomplete",i)}(n),function(t){var n=document.createElement("INPUT");n.className="htCheckboxRendererInput",n.type="checkbox",n.setAttribute("autocomplete","off");var r=function(r,i,s,o,u,a,f){typeof f.checkedTemplate=="undefined"&&(f.checkedTemplate=!0),typeof f.uncheckedTemplate=="undefined"&&(f.uncheckedTemplate=!1),r.view.wt.wtDom.empty(i);var l=n.cloneNode(!1);a===f.checkedTemplate||a===t.helper.stringify(f.checkedTemplate)?(l.checked=!0,i.appendChild(l)):a===f.uncheckedTemplate||a===t.helper.stringify(f.uncheckedTemplate)?i.appendChild(l):a===null?(l.className+=" noValue",i.appendChild(l)):r.view.wt.wtDom.fastInnerText(i,"#bad value#");var c=e(l);f.readOnly?c.on("click",function(e){e.preventDefault()}):(c.on("mousedown",function(e){e.stopPropagation()}),c.on("mouseup",function(e){e.stopPropagation()}),c.on("change",function(){this.checked?r.setDataAtRowProp(s,u,f.checkedTemplate):r.setDataAtRowProp(s,u,f.uncheckedTemplate)}));if(!r.CheckboxRenderer||!r.CheckboxRenderer.beforeKeyDownHookBound)r.CheckboxRenderer={beforeKeyDownHookBound:!0},r.addHook("beforeKeyDown",function(n){if(n.keyCode==t.helper.keyCode.SPACE){var i=r.getSelected(),s,o,u,a={row:Math.min(i[0],i[2]),col:Math.min(i[1],i[3])},f={row:Math.max(i[0],i[2]),col:Math.max(i[1],i[3])};for(var l=a.row;l<=f.row;l++)for(var c=f.col;c<=f.col;c++){s=r.getCell(l,c),u=r.getCellMeta(l,c),o=s.querySelectorAll("input[type=checkbox]");if(o.length>0&&!u.readOnly){n.isImmediatePropagationStopped()||(n.stopImmediatePropagation(),n.preventDefault());for(var h=0,p=o.length;h<p;h++)o[h].checked=!o[h].checked,e(o[h]).trigger("change")}}}})};t.CheckboxRenderer=r,t.renderers.CheckboxRenderer=r,t.renderers.registerRenderer("checkbox",r)}(n),function(e){var t=function(t,n,r,i,s,o,u){e.helper.isNumeric(o)&&(typeof u.language!="undefined"&&numeral.language(u.language),o=numeral(o).format(u.format||"0"),t.view.wt.wtDom.addClass(n,"htNumeric")),e.renderers.TextRenderer(t,n,r,i,s,o,u)};e.NumericRenderer=t,e.renderers.NumericRenderer=t,e.renderers.registerRenderer("numeric",t)}(n),function(e){var t=function(e,t,r,i,s,o,u){n.renderers.TextRenderer.apply(this,arguments),o=t.innerHTML;var a,f=u.hashLength||o.length,l=u.hashSymbol||"*";for(a="";a.split(l).length-1<f;a+=l);e.view.wt.wtDom.fastInnerHTML(t,a)};e.PasswordRenderer=t,e.renderers.PasswordRenderer=t,e.renderers.registerRenderer("password",t)}(n),function(e){function t(t,n,r,i,s,o,u){e.renderers.cellDecorator.apply(this,arguments),e.Dom.fastInnerHTML(n,o)}e.renderers.registerRenderer("html",t),e.renderers.HtmlRenderer=t}(n),function(e){function t(t){this.instance=t,this.state=e.EditorState.VIRGIN,this._opened=!1,this._closeCallback=null,this.init()}e.EditorState={VIRGIN:"STATE_VIRGIN",EDITING:"STATE_EDITING",WAITING:"STATE_WAITING",FINISHED:"STATE_FINISHED"},t.prototype._fireCallbacks=function(e){this._closeCallback&&(this._closeCallback(e),this._closeCallback=null)},t.prototype.init=function(){},t.prototype.getValue=function(){throw Error("Editor getValue() method unimplemented")},t.prototype.setValue=function(e){throw Error("Editor setValue() method unimplemented")},t.prototype.open=function(){throw Error("Editor open() method unimplemented")},t.prototype.close=function(){throw Error("Editor close() method unimplemented")},t.prototype.prepare=function(t,n,r,i,s,o){this.TD=i,this.row=t,this.col=n,this.prop=r,this.originalValue=s,this.cellProperties=o,this.state=e.EditorState.VIRGIN},t.prototype.extend=function(){function t(){e.apply(this,arguments)}function n(e,t){function n(){}return n.prototype=t.prototype,e.prototype=new n,e.prototype.constructor=e,e}var e=this.constructor;return n(t,e)},t.prototype.saveValue=function(e,t){if(t){var n=this.instance.getSelected();this.instance.populateFromArray(n[0],n[1],e,n[2],n[3],"edit")}else this.instance.populateFromArray(this.row,this.col,e,null,null,"edit")},t.prototype.beginEditing=function(t){if(this.state!=e.EditorState.VIRGIN)return;if(this.cellProperties.readOnly)return;this.instance.view.scrollViewport({row:this.row,col:this.col}),this.instance.view.render(),this.state=e.EditorState.EDITING,t=typeof t=="string"?t:this.originalValue,this.setValue(e.helper.stringify(t)),this.open(),this._opened=!0,this.focus(),this.instance.view.render()},t.prototype.finishEditing=function(t,n,r){if(r){var i=this._closeCallback;this._closeCallback=function(e){i&&i(e),r(e)}}if(this.isWaiting())return;if(this.state==e.EditorState.VIRGIN){var s=this;setTimeout(function(){s._fireCallbacks(!0)});return}if(this.state==e.EditorState.EDITING){if(t){this.cancelChanges();return}var o=[[String.prototype.trim.call(this.getValue())]];this.state=e.EditorState.WAITING,this.saveValue(o,n);if(this.instance.getCellValidator(this.cellProperties)){var s=this;this.instance.addHookOnce("afterValidate",function(t){s.state=e.EditorState.FINISHED,s.discardEditor(t)})}else this.state=e.EditorState.FINISHED,this.discardEditor(!0)}},t.prototype.cancelChanges=function(){this.state=e.EditorState.FINISHED,this.discardEditor()},t.prototype.discardEditor=function(t){if(this.state!==e.EditorState.FINISHED)return;t===!1&&this.cellProperties.allowInvalid!==!0?(this.instance.selectCell(this.row,this.col),this.focus(),this.state=e.EditorState.EDITING,this._fireCallbacks(!1)):(this.close(),this._opened=!1,this.state=e.EditorState.VIRGIN,this._fireCallbacks(!0))},t.prototype.isOpened=function(){return this._opened},t.prototype.isWaiting=function(){return this.state===e.EditorState.WAITING},e.editors.BaseEditor=t}(n),function(t){var n=t.editors.BaseEditor.prototype.extend();n.prototype.init=function(){this.createElements(),this.bindEvents()},n.prototype.getValue=function(){return this.TEXTAREA.value},n.prototype.setValue=function(e){this.TEXTAREA.value=e};var r=function(n){var r=this,i=r.getActiveEditor(),s=t.helper.keyCode,o=(n.ctrlKey||n.metaKey)&&!n.altKey;if(n.target!==i.TEXTAREA||n.isImmediatePropagationStopped())return;if(n.keyCode===17||n.keyCode===224||n.keyCode===91||n.keyCode===93){n.stopImmediatePropagation();return}switch(n.keyCode){case s.ARROW_RIGHT:i.wtDom.getCaretPosition(i.TEXTAREA)!==i.TEXTAREA.value.length&&n.stopImmediatePropagation();break;case s.ARROW_LEFT:i.wtDom.getCaretPosition(i.TEXTAREA)!==0&&n.stopImmediatePropagation();break;case s.ENTER:var u=i.instance.getSelected(),a=u[0]!==u[2]||u[1]!==u[3];if(o&&!a||n.altKey)i.isOpened()?(i.setValue(i.getValue()+"\n"),i.focus()):i.beginEditing(i.originalValue+"\n"),n.stopImmediatePropagation();n.preventDefault();break;case s.A:case s.X:case s.C:case s.V:if(o){n.stopImmediatePropagation();break};case s.BACKSPACE:case s.DELETE:case s.HOME:case s.END:n.stopImmediatePropagation()}};n.prototype.open=function(){this.refreshDimensions(),this.instance.addHook("beforeKeyDown",r)},n.prototype.close=function(){this.textareaParentStyle.display="none",document.activeElement===this.TEXTAREA&&this.instance.listen(),this.instance.removeHook("beforeKeyDown",r)},n.prototype.focus=function(){this.TEXTAREA.focus(),this.wtDom.setCaretPosition(this.TEXTAREA,this.TEXTAREA.value.length)},n.prototype.createElements=function(){this.$body=e(document.body),this.wtDom=new A,this.TEXTAREA=document.createElement("TEXTAREA"),this.$textarea=e(this.TEXTAREA),this.wtDom.addClass(this.TEXTAREA,"handsontableInput"),this.textareaStyle=this.TEXTAREA.style,this.textareaStyle.width=0,this.textareaStyle.height=0,this.TEXTAREA_PARENT=document.createElement("DIV"),this.wtDom.addClass(this.TEXTAREA_PARENT,"handsontableInputHolder"),this.textareaParentStyle=this.TEXTAREA_PARENT.style,this.textareaParentStyle.top=0,this.textareaParentStyle.left=0,this.textareaParentStyle.display="none",this.TEXTAREA_PARENT.appendChild(this.TEXTAREA),this.instance.rootElement[0].appendChild(this.TEXTAREA_PARENT);var n=this;t.PluginHooks.add("afterRender",function(){n.instance.registerTimeout("refresh_editor_dimensions",function(){n.refreshDimensions()},0)})},n.prototype.refreshDimensions=function(){if(this.state!==t.EditorState.EDITING)return;this.TD=this.instance.getCell(this.row,this.col);if(!this.TD)return;var n=e(this.TD),r=this.wtDom.offset(this.TD),i=this.wtDom.offset(this.instance.rootElement[0]),s=this.instance.rootElement.scrollTop(),o=this.instance.rootElement.scrollLeft(),u=r.top-i.top+s-1,a=r.left-i.left+o-1,f=this.instance.getSettings(),l=f.rowHeaders===!1?0:1,c=f.colHeaders===!1?0:1;u<0&&(u=0),a<0&&(a=0),l>0&&parseInt(n.css("border-top-width"),10)>0&&(u+=1),c>0&&parseInt(n.css("border-left-width"),10)>0&&(a+=1),this.textareaParentStyle.top=u+"px",this.textareaParentStyle.left=a+"px";var h=n.width(),p=this.instance.view.maximumVisibleElementWidth(a)-10,d=n.outerHeight()-4,v=this.instance.view.maximumVisibleElementHeight(u)-5;parseInt(n.css("border-top-width"),10)>0&&(d-=1),parseInt(n.css("border-left-width"),10)>0&&l>0&&(h-=1),this.$textarea.autoResize({minHeight:Math.min(d,v),maxHeight:v,minWidth:Math.min(h,p),maxWidth:p,animate:!1,extraSpace:0}),this.textareaParentStyle.display="block"},n.prototype.bindEvents=function(){this.$textarea.on("cut.editor",function(e){e.stopPropagation()}),this.$textarea.on("paste.editor",function(e){e.stopPropagation()})},t.editors.TextEditor=n,t.editors.registerEditor("text",t.editors.TextEditor)}(n),function(e){var t=e.editors.BaseEditor.prototype.extend();t.prototype.beginEditing=function(){this.saveValue([[!this.originalValue]])},t.prototype.finishEditing=function(){},t.prototype.init=function(){},t.prototype.open=function(){},t.prototype.close=function(){},t.prototype.getValue=function(){},t.prototype.setValue=function(){},t.prototype.focus=function(){},e.editors.CheckboxEditor=t,e.editors.registerEditor("checkbox",t)}(n),function(t){var n=t.editors.TextEditor.prototype.extend();n.prototype.init=function(){if(!e.datepicker)throw new Error("jQuery UI Datepicker dependency not found. Did you forget to include jquery-ui.custom.js or its substitute?");t.editors.TextEditor.prototype.init.apply(this,arguments),this.isCellEdited=!1;var n=this;this.instance.addHook("afterDestroy",function(){n.destroyElements()})},n.prototype.createElements=function(){t.editors.TextEditor.prototype.createElements.apply(this,arguments),this.datePicker=document.createElement("DIV"),this.instance.view.wt.wtDom.addClass(this.datePicker,"htDatepickerHolder"),this.datePickerStyle=this.datePicker.style,this.datePickerStyle.position="absolute",this.datePickerStyle.top=0,this.datePickerStyle.left=0,this.datePickerStyle.zIndex=99,document.body.appendChild(this.datePicker),this.$datePicker=e(this.datePicker);var n=this,r={dateFormat:"yy-mm-dd",showButtonPanel:!0,changeMonth:!0,changeYear:!0,altField:this.$textarea,onSelect:function(){n.finishEditing(!1)}};this.$datePicker.datepicker(r),this.$datePicker.on("mousedown",function(e){e.stopPropagation()}),this.hideDatepicker()},n.prototype.destroyElements=function(){this.$datePicker.datepicker("destroy"),this.$datePicker.remove()},n.prototype.beginEditing=function(e,n,r,i,s){t.editors.TextEditor.prototype.beginEditing.apply(this,arguments),this.showDatepicker()},n.prototype.finishEditing=function(e,n){this.hideDatepicker(),t.editors.TextEditor.prototype.finishEditing.apply(this,arguments)},n.prototype.showDatepicker=function(){var t=e(this.TD),n=t.offset();this.datePickerStyle.top=n.top+t.height()+"px",this.datePickerStyle.left=n.left+"px";var r={defaultDate:this.originalValue||void 0};e.extend(r,this.cellProperties),this.$datePicker.datepicker("option",r),this.originalValue&&this.$datePicker.datepicker("setDate",this.originalValue),this.datePickerStyle.display="block"},n.prototype.hideDatepicker=function(){this.datePickerStyle.display="none"},t.editors.DateEditor=n,t.editors.registerEditor("date",n)}(n),function(t){var n=t.editors.TextEditor.prototype.extend();n.prototype.createElements=function(){t.editors.TextEditor.prototype.createElements.apply(this,arguments);var n=document.createElement("DIV");n.className="handsontableEditor",this.TEXTAREA_PARENT.appendChild(n),this.$htContainer=e(n),this.$htContainer.handsontable()},n.prototype.prepare=function(n,r,i,s,o,u){t.editors.TextEditor.prototype.prepare.apply(this,arguments);var a=this,f={startRows:0,startCols:0,minRows:0,minCols:0,className:"listbox",copyPaste:!1,cells:function(){return{readOnly:!0}},fillHandle:!1,afterOnCellMouseDown:function(){var e=this.getValue();e!==void 0&&a.setValue(e),a.instance.destroyEditor()},beforeOnKeyDown:function(e){var n=this;switch(e.keyCode){case t.helper.keyCode.ESCAPE:a.instance.destroyEditor(!0),e.stopImmediatePropagation(),e.preventDefault();break;case t.helper.keyCode.ENTER:var r=n.getSelected(),i=this.getDataAtCell(r[0],r[1]);i!==void 0&&a.setValue(i),a.instance.destroyEditor();break;case t.helper.keyCode.ARROW_UP:n.getSelected()&&n.getSelected()[0]==0&&!a.cellProperties.strict&&(n.deselectCell(),a.instance.listen(),a.focus(),e.preventDefault(),e.stopImmediatePropagation())}}};this.cellProperties.handsontable&&(f=e.extend(f,u.handsontable)),this.$htContainer.handsontable("destroy"),this.$htContainer.handsontable(f)};var r=function(e){if(e.isImmediatePropagationStopped())return;var n=this.getActiveEditor(),r=n.$htContainer.handsontable("getInstance");if(e.keyCode==t.helper.keyCode.ARROW_DOWN){if(!r.getSelected())r.selectCell(0,0);else{var i=r.getSelected()[0],s=i<r.countRows()-1?i+1:i;r.selectCell(s,0)}e.preventDefault(),e.stopImmediatePropagation()}};n.prototype.open=function(){this.instance.addHook("beforeKeyDown",r),t.editors.TextEditor.prototype.open.apply(this,arguments),this.$htContainer.handsontable("render"),this.cellProperties.strict?(this.$htContainer.handsontable("selectCell",0,0),this.$textarea[0].style.visibility="hidden"):(this.$htContainer.handsontable("deselectCell"),this.$textarea[0].style.visibility="visible"),this.wtDom.setCaretPosition(this.$textarea[0],0,this.$textarea[0].value.length)},n.prototype.close=function(){this.instance.removeHook("beforeKeyDown",r),this.instance.listen(),t.editors.TextEditor.prototype.close.apply(this,arguments)},n.prototype.focus=function(){this.instance.listen(),t.editors.TextEditor.prototype.focus.apply(this,arguments)},n.prototype.beginEditing=function(e){var n=this.instance.getSettings().onBeginEditing;if(n&&n()===!1)return;t.editors.TextEditor.prototype.beginEditing.apply(this,arguments)},n.prototype.finishEditing=function(e,n){this.$htContainer.handsontable("isListening")&&this.instance.listen();if(this.$htContainer.handsontable("getSelected")){var r=this.$htContainer.handsontable("getInstance").getValue();r!==void 0&&this.setValue(r)}return t.editors.TextEditor.prototype.finishEditing.apply(this,arguments)},t.editors.HandsontableEditor=n,t.editors.registerEditor("handsontable",n)}(n),function(e){function r(e,t){var n={},r=t.length,i,s,o;for(var u=0,a=e.length;u<a;u++){i=e[u],r>0?s=i.indexOf(t):s=i===t?0:-1;if(s==-1)continue;o=i.length-s-r;if(typeof n.indexOfValue=="undefined"||n.indexOfValue>s||n.indexOfValue==s&&n.charsLeft>o)n.indexOfValue=s,n.charsLeft=o,n.index=u}return n.index}var t=e.editors.HandsontableEditor.prototype.extend();t.prototype.init=function(){e.editors.HandsontableEditor.prototype.init.apply(this,arguments),this.query=null},t.prototype.createElements=function(){e.editors.HandsontableEditor.prototype.createElements.apply(this,arguments),this.$htContainer.addClass("autocompleteEditor")},t.prototype.bindEvents=function(){var t=this;this.$textarea.on("keydown.autocompleteEditor",function(n){!e.helper.isMetaKey(n.keyCode)||[e.helper.keyCode.BACKSPACE,e.helper.keyCode.DELETE].indexOf(n.keyCode)!=-1?setTimeout(function(){t.queryChoices(t.$textarea.val())}):n.keyCode==e.helper.keyCode.ENTER&&t.cellProperties.strict!==!0&&t.$htContainer.handsontable("deselectCell")}),this.$htContainer.on("mouseenter",function(){t.$htContainer.handsontable("deselectCell")}),this.$htContainer.on("mouseleave",function(){t.queryChoices(t.query)}),e.editors.HandsontableEditor.prototype.bindEvents.apply(this,arguments)},t.prototype.beginEditing=function(){e.editors.HandsontableEditor.prototype.beginEditing.apply(this,arguments);var t=this;setTimeout(function(){t.queryChoices(t.TEXTAREA.value)});var n=this.$htContainer.handsontable("getInstance");n.updateSettings({colWidths:[this.wtDom.outerWidth(this.TEXTAREA)-2],afterRenderer:function(e,n,r,i,s,o){var u=e.innerHTML.match(new RegExp(t.query,"i"));u&&(e.innerHTML=s.replace(u[0],"<strong>"+u[0]+"</strong>"))}})};var n;t.prototype.open=function(){var t=this;n=function(n){var r=this;n.keyCode==e.helper.keyCode.ARROW_UP&&r.getSelected()&&r.getSelected()[0]==0&&(t.cellProperties.strict||r.deselectCell(),t.instance.listen(),t.focus(),n.preventDefault(),n.stopImmediatePropagation())},this.$htContainer.handsontable("getInstance").addHook("beforeKeyDown",n),e.editors.HandsontableEditor.prototype.open.apply(this,arguments),this.$textarea[0].style.visibility="visible",t.focus()},t.prototype.close=function(){this.$htContainer.handsontable("getInstance").removeHook("beforeKeyDown",n),e.editors.HandsontableEditor.prototype.close.apply(this,arguments)},t.prototype.queryChoices=function(t){this.query=t;if(typeof this.cellProperties.source=="function"){var n=this;this.cellProperties.source(t,function(e){n.updateChoicesList(e)})}else if(e.helper.isArray(this.cellProperties.source)){var r;if(!t||this.cellProperties.filter===!1)r=this.cellProperties.source;else{var i=new RegExp(t,this.cellProperties.filteringCaseSensitive===!0?"":"i");r=this.cellProperties.source.filter(function(e){return i.test(e)})}this.updateChoicesList(r)}else this.updateChoicesList([])},t.prototype.updateChoicesList=function(t){this.$htContainer.handsontable("loadData",e.helper.pivot([t]));var n=this.getValue(),i;this.cellProperties.strict===!0&&(i=r(t,n),typeof i=="undefined"&&(i=0)),typeof i=="undefined"?this.$htContainer.handsontable("deselectCell"):this.$htContainer.handsontable("selectCell",i,0),this.focus()},e.editors.AutocompleteEditor=t,e.editors.registerEditor("autocomplete",t)}(n),function(t){var n=t.editors.TextEditor.prototype.extend(),r=new A;n.prototype.createElements=function(){t.editors.TextEditor.prototype.createElements.apply(this,arguments),this.TEXTAREA=document.createElement("input"),this.TEXTAREA.setAttribute("type","password"),this.TEXTAREA.className="handsontableInput",this.textareaStyle=this.TEXTAREA.style,this.textareaStyle.width=0,this.textareaStyle.height=0,this.$textarea=e(this.TEXTAREA),r.empty(this.TEXTAREA_PARENT),this.TEXTAREA_PARENT.appendChild(this.TEXTAREA)},t.editors.PasswordEditor=n,t.editors.registerEditor("password",n)}(n),function(t){var n=t.editors.BaseEditor.prototype.extend();n.prototype.init=function(){this.select=e("<select />").addClass("htSelectEditor").hide(),this.instance.rootElement.append(this.select)},n.prototype.prepare=function(){t.editors.BaseEditor.prototype.prepare.apply(this,arguments);var n=this.cellProperties.selectOptions,r;typeof n=="function"?r=this.prepareOptions(n(this.row,this.col,this.prop)):r=this.prepareOptions(n);var i=[];for(var s in r)if(r.hasOwnProperty(s)){var o=e("<option />");o.val(s),o.html(r[s]),i.push(o)}this.select.empty(),this.select.append(i)},n.prototype.prepareOptions=function(e){var n={};if(t.helper.isArray(e))for(var r=0,i=e.length;r<i;r++)n[e[r]]=e[r];else typeof e=="object"&&(n=e);return n},n.prototype.getValue=function(){return this.select.val()},n.prototype.setValue=function(e){this.select.val(e)};var r=function(e){var n=this,r=n.getActiveEditor();switch(e.keyCode){case t.helper.keyCode.ARROW_UP:var i=r.select.find("option:selected").prev();i.length==1&&i.prop("selected",!0),e.stopImmediatePropagation(),e.preventDefault();break;case t.helper.keyCode.ARROW_DOWN:var s=r.select.find("option:selected").next();s.length==1&&s.prop("selected",!0),e.stopImmediatePropagation(),e.preventDefault()}};n.prototype.open=function(){this.select.css({height:e(this.TD).height(),"min-width":e(this.TD).outerWidth()}),this.select.show(),this.select.offset(e(this.TD).offset()),this.instance.addHook("beforeKeyDown",r)},n.prototype.close=function(){this.select.hide(),this.instance.removeHook("beforeKeyDown",r)},n.prototype.focus=function(){this.select.focus()},t.editors.SelectEditor=n,t.editors.registerEditor("select",n)}(n),function(e){var t=e.editors.AutocompleteEditor.prototype.extend();t.prototype.prepare=function(){e.editors.AutocompleteEditor.prototype.prepare.apply(this,arguments),this.cellProperties.filter=!1,this.cellProperties.strict=!0},e.editors.DropdownEditor=t,e.editors.registerEditor("dropdown",t)}(n),n.NumericValidator=function(e,t){e===null&&(e=""),t(/^-?\d*\.?\d*$/.test(e))};var i=function(e,t){var n=e,r=typeof n=="string"?n.toLowerCase():null;return function(e){var i=!1;for(var s=0,o=e.length;s<o;s++){if(n===e[s]){i=!0;break}if(r===e[s].toLowerCase()){i=!0;break}}t(i)}};n.AutocompleteValidator=function(e,t){this.strict&&this.source?typeof this.source=="function"?this.source(e,i(e,t)):i(e,t)(this.source):t(!0)},n.AutocompleteCell={editor:n.editors.AutocompleteEditor,renderer:n.renderers.AutocompleteRenderer,validator:n.AutocompleteValidator},n.CheckboxCell={editor:n.editors.CheckboxEditor,renderer:n.renderers.CheckboxRenderer},n.TextCell={editor:n.editors.TextEditor,renderer:n.renderers.TextRenderer},n.NumericCell={editor:n.editors.TextEditor,renderer:n.renderers.NumericRenderer,validator:n.NumericValidator,dataType:"number"},n.DateCell={editor:n.editors.DateEditor,renderer:n.renderers.AutocompleteRenderer},n.HandsontableCell={editor:n.editors.HandsontableEditor,renderer:n.renderers.AutocompleteRenderer},n.PasswordCell={editor:n.editors.PasswordEditor,renderer:n.renderers.PasswordRenderer,copyable:!1},n.DropdownCell={editor:n.editors.DropdownEditor,renderer:n.renderers.AutocompleteRenderer,validator:n.AutocompleteValidator},n.cellTypes={text:n.TextCell,date:n.DateCell,numeric:n.NumericCell,checkbox:n.CheckboxCell,autocomplete:n.AutocompleteCell,handsontable:n.HandsontableCell,password:n.PasswordCell,dropdown:n.DropdownCell},n.cellLookup={validator:{numeric:n.NumericValidator,autocomplete:n.AutocompleteValidator}},function(e){function t(r){return this.filter(t.resizableFilterSelector).each(function(){new n(e(this),r)}),this}function n(n,r){if(this.clones)return;this.config=e.extend({},t.defaults,r),this.el=n,this.nodeName=n[0].nodeName.toLowerCase(),this.previousScrollTop=null,r.maxWidth==="original"&&(r.maxWidth=n.width()),r.minWidth==="original"&&(r.minWidth=n.width()),r.maxHeight==="original"&&(r.maxHeight=n.height()),r.minHeight==="original"&&(r.minHeight=n.height()),this.nodeName==="textarea"&&n.css({resize:"none",overflowY:"none"}),n.data("AutoResizer",this),this.createClone(),this.injectClone(),this.bind()}t.defaults={onResize:function(){},animate:{duration:200,complete:function(){}},extraSpace:50,minHeight:"original",maxHeight:500,minWidth:"original",maxWidth:500},t.cloneCSSProperties=["lineHeight","textDecoration","letterSpacing","fontSize","fontFamily","fontStyle","fontWeight","textTransform","textAlign","direction","wordSpacing","fontSizeAdjust","padding"],t.cloneCSSValues={position:"absolute",top:-9999,left:-9999,opacity:0,overflow:"hidden",overflowX:"hidden",overflowY:"hidden",border:"1px solid black",padding:"0.49em"},t.resizableFilterSelector="textarea,input:not(input[type]),input[type=text],input[type=password]",t.AutoResizer=n,e.fn.autoResize=t,n.prototype={bind:function(){var t=e.proxy(function(){return this.check(),!0},this);this.unbind(),this.el.bind("keyup.autoResize",t).bind("change.autoResize",t),this.check(null,!0)},unbind:function(){this.el.unbind(".autoResize")},createClone:function(){var n=this.el,r=this,i=this.config;this.clones=e();if(i.minHeight!=="original"||i.maxHeight!=="original")this.hClone=n.clone().height("auto"),this.clones=this.clones
.add(this.hClone);if(i.minWidth!=="original"||i.maxWidth!=="original")this.wClone=e("<div/>").width("auto").css({whiteSpace:"nowrap","float":"left"}),this.clones=this.clones.add(this.wClone);e.each(t.cloneCSSProperties,function(e,t){r.clones.css(t,n.css(t))}),this.clones.removeAttr("name").removeAttr("id").attr("tabIndex",-1).css(t.cloneCSSValues).css("overflowY","scroll")},check:function(e,t){var n=this.config,r=this.wClone,i=this.hClone,s=this.el,o=s.val();if(r){r.text(o);var u=r.outerWidth(),a=u+n.extraSpace>=n.minWidth?u+n.extraSpace:n.minWidth,f=s.width();a=Math.min(a,n.maxWidth);if(a<f&&a>=n.minWidth||a>=n.minWidth&&a<=n.maxWidth)n.onResize.call(s),s.scrollLeft(0),n.animate&&!t?s.stop(1,1).animate({width:a},n.animate):s.width(a)}if(i){a&&i.width(a),i.height(0).val(o).scrollTop(1e4);var l=i[0].scrollTop+n.extraSpace;if(this.previousScrollTop===l)return;this.previousScrollTop=l,l>=n.maxHeight&&(l=n.maxHeight),l<n.minHeight&&(l=n.minHeight),l==n.maxHeight&&a==n.maxWidth?s.css("overflowY","scroll"):s.css("overflowY","hidden"),n.onResize.call(s),n.animate&&!t?s.stop(1,1).animate({height:l},n.animate):s.height(l)}},destroy:function(){this.unbind(),this.el.removeData("AutoResizer"),this.clones.remove(),delete this.el,delete this.hClone,delete this.wClone,delete this.clones},injectClone:function(){(t.cloneContainer||(t.cloneContainer=e("<arclones/>").appendTo("body"))).empty().append(this.clones)}}}(jQuery),function(e){function t(e){return e.split('"').length-1}e.SheetClip={parse:function(e){var n,r,i,s=[],o=0,u,a,f,l;i=e.split("\n"),i.length>1&&i[i.length-1]===""&&i.pop();for(n=0,r=i.length;n<r;n+=1){i[n]=i[n].split("	");for(u=0,a=i[n].length;u<a;u+=1)s[o]||(s[o]=[]),f&&u===0?(l=s[o].length-1,s[o][l]=s[o][l]+"\n"+i[n][0],f&&t(i[n][0])&1&&(f=!1,s[o][l]=s[o][l].substring(0,s[o][l].length-1).replace(/""/g,'"'))):u===a-1&&i[n][u].indexOf('"')===0?(s[o].push(i[n][u].substring(1).replace(/""/g,'"')),f=!0):(s[o].push(i[n][u].replace(/""/g,'"')),f=!1);f||(o+=1)}return s},stringify:function(e){var t,n,r,i,s="",o;for(t=0,n=e.length;t<n;t+=1){for(r=0,i=e[t].length;r<i;r+=1)r>0&&(s+="	"),o=e[t][r],typeof o=="string"?o.indexOf("\n")>-1?s+='"'+o.replace(/"/g,'""')+'"':s+=o:o===null||o===void 0?s+="":s+=o;s+="\n"}return s}}}(t);var s=function(){var e;return{getInstance:function(){return e?e.hasBeenDestroyed()&&e.init():e=new o,e.refCounter++,e}}}();o.prototype.init=function(){var e=this,t,n;this.copyCallbacks=[],this.cutCallbacks=[],this.pasteCallbacks=[],this.listenerElement=document.documentElement,n=document.body,document.getElementById("CopyPasteDiv")?(this.elDiv=document.getElementById("CopyPasteDiv"),this.elTextarea=this.elDiv.firstChild):(this.elDiv=document.createElement("DIV"),this.elDiv.id="CopyPasteDiv",t=this.elDiv.style,t.position="fixed",t.top=0,t.left=0,n.appendChild(this.elDiv),this.elTextarea=document.createElement("TEXTAREA"),this.elTextarea.className="copyPaste",t=this.elTextarea.style,t.width="1px",t.height="1px",this.elDiv.appendChild(this.elTextarea),typeof t.opacity!="undefined"&&(t.opacity=0)),this.keydownListener=function(t){var n=!1;t.metaKey?n=!0:t.ctrlKey&&navigator.userAgent.indexOf("Mac")===-1&&(n=!0);if(n){if(document.activeElement!==e.elTextarea&&(e.getSelectionText()!=""||["INPUT","SELECT","TEXTAREA"].indexOf(document.activeElement.nodeName)!=-1))return;e.selectNodeText(e.elTextarea),setTimeout(function(){e.selectNodeText(e.elTextarea)},0)}n&&(t.keyCode===67||t.keyCode===86||t.keyCode===88)&&(t.keyCode===88?setTimeout(function(){e.triggerCut(t)},0):t.keyCode===86&&setTimeout(function(){e.triggerPaste(t)},0))},this._bindEvent(this.listenerElement,"keydown",this.keydownListener)},o.prototype.selectNodeText=function(e){e.select()},o.prototype.getSelectionText=function(){var e="";return t.getSelection?e=t.getSelection().toString():document.selection&&document.selection.type!="Control"&&(e=document.selection.createRange().text),e},o.prototype.copyable=function(e){if(typeof e!="string"&&e.toString===void 0)throw new Error("copyable requires string parameter");this.elTextarea.value=e},o.prototype.onCut=function(e){this.cutCallbacks.push(e)},o.prototype.onPaste=function(e){this.pasteCallbacks.push(e)},o.prototype.removeCallback=function(e){var t,n;for(t=0,n=this.copyCallbacks.length;t<n;t++)if(this.copyCallbacks[t]===e)return this.copyCallbacks.splice(t,1),!0;for(t=0,n=this.cutCallbacks.length;t<n;t++)if(this.cutCallbacks[t]===e)return this.cutCallbacks.splice(t,1),!0;for(t=0,n=this.pasteCallbacks.length;t<n;t++)if(this.pasteCallbacks[t]===e)return this.pasteCallbacks.splice(t,1),!0;return!1},o.prototype.triggerCut=function(e){var t=this;t.cutCallbacks&&setTimeout(function(){for(var n=0,r=t.cutCallbacks.length;n<r;n++)t.cutCallbacks[n](e)},50)},o.prototype.triggerPaste=function(e,t){var n=this;n.pasteCallbacks&&setTimeout(function(){var r=(t||n.elTextarea.value).replace(/\n$/,"");for(var i=0,s=n.pasteCallbacks.length;i<s;i++)n.pasteCallbacks[i](r,e)},50)},o.prototype.destroy=function(){!this.hasBeenDestroyed()&&--this.refCounter==0&&(this.elDiv&&this.elDiv.parentNode&&this.elDiv.parentNode.removeChild(this.elDiv),this._unbindEvent(this.listenerElement,"keydown",this.keydownListener))},o.prototype.hasBeenDestroyed=function(){return!this.refCounter},o.prototype._bindEvent=function(){return t.jQuery?function(t,n,r){e(t).on(n+".copypaste",r)}:function(e,t,n){e.addEventListener(t,n,!1)}}(),o.prototype._unbindEvent=function(){return t.jQuery?function(t,n,r){e(t).off(n+".copypaste",r)}:function(e,t,n){e.removeEventListener(t,n,!1)}}();var u;(function(e){function s(e){return e.indexOf("/")===-1&&e.indexOf("~")===-1?e:e.replace(/~/g,"~0").replace(/\//g,"~1")}function o(e,t){var n;for(var r in e)if(e.hasOwnProperty(r)){if(e[r]===t)return s(r)+"/";if(typeof e[r]=="object"){n=o(e[r],t);if(n!="")return s(r)+"/"+n}}return""}function u(e,t){if(e===t)return"/";var n=o(e,t);if(n==="")throw new Error("Object not found in root");return"/"+n}function c(e){for(var t=0,n=a.length;t<n;t++)if(a[t].obj===e)return a[t]}function h(e,t){for(var n=0,r=e.observers.length;n<r;n++)if(e.observers[n].callback===t)return e.observers[n].observer}function p(e,t){for(var n=0,r=e.observers.length;n<r;n++)if(e.observers[n].observer===t){e.observers.splice(n,1);return}}function d(e,t){y(t),Object.observe?g(t,e):clearTimeout(t.next);var n=c(e);p(n,t)}function v(e,n){var r=[],s=e,o,p=c(e);p?o=h(p,n):(p=new f(e),a.push(p));if(o)return o;if(Object.observe)o=function(t){g(o,e),m(o,e);var a=0,f=t.length;while(a<f){if((t[a].name!=="length"||!E(t[a].object))&&t[a].name!=="__Jasmine_been_here_before__"){var l=t[a].type;switch(l){case"new":l="add";break;case"deleted":l="delete";break;case"updated":l="update"}i[l].call(t[a],r,u(s,t[a].object))}a++}r&&n&&n(r),o.patches=r,r=[]};else{o={},p.value=JSON.parse(JSON.stringify(e));if(n){o.callback=n,o.next=null;var d=this.intervals||[100,1e3,1e4,6e4],v=0,b=function(){y(o)},w=function(){clearTimeout(o.next),o.next=setTimeout(function(){b(),v=0,o.next=setTimeout(S,d[v++])},0)},S=function(){b(),v==d.length&&(v=d.length-1),o.next=setTimeout(S,d[v++])};typeof t!="undefined"&&(t.addEventListener?(t.addEventListener("mousedown",w),t.addEventListener("mouseup",w),t.addEventListener("keydown",w)):(t.attachEvent("onmousedown",w),t.attachEvent("onmouseup",w),t.attachEvent("onkeydown",w))),o.next=setTimeout(S,d[v++])}}return o.patches=r,o.object=e,p.observers.push(new l(n,o)),m(o,e)}function m(e,t){if(Object.observe){Object.observe(t,e);for(var n in t)if(t.hasOwnProperty(n)){var r=t[n];r&&typeof r=="object"&&m(e,r)}}return e}function g(e,t){if(Object.observe){Object.unobserve(t,e);for(var n in t)if(t.hasOwnProperty(n)){var r=t[n];r&&typeof r=="object"&&g(e,r)}}return e}function y(e){if(Object.observe)Object.deliverChangeRecords(e);else{var t;for(var n=0,r=a.length;n<r;n++)if(a[n].obj===e.object){t=a[n];break}w(t.value,e.object,e.patches,"")}var i=e.patches;return i.length>0&&(e.patches=[],e.callback&&e.callback(i)),i}function w(e,t,n,r){var i=b(t),o=b(e),u=!1,a=!1;for(var f=o.length-1;f>=0;f--){var l=o[f],c=e[l];if(t.hasOwnProperty(l)){var h=t[l];c instanceof Object?w(c,h,n,r+"/"+s(l)):c!=h&&(u=!0,n.push({op:"replace",path:r+"/"+s(l),value:h}),e[l]=h)}else n.push({op:"remove",path:r+"/"+s(l)}),delete e[l],a=!0}if(!a&&i.length==o.length)return;for(var f=0;f<i.length;f++){var l=i[f];e.hasOwnProperty(l)||(n.push({op:"add",path:r+"/"+s(l),value:t[l]}),e[l]=JSON.parse(JSON.stringify(t[l])))}}function S(e,t){var i=!1,s=0,o=t.length,u;while(s<o){u=t[s];var a=u.path.split("/"),f=e,l=1,c=a.length;for(;;)if(E(f)){var h=parseInt(a[l],10);l++;if(l>=c){i=r[u.op].call(u,f,h,e);break}f=f[h]}else{var p=a[l];p.indexOf("~")!=-1&&(p=p.replace(/~1/g,"/").replace(/~0/g,"~")),l++;if(l>=c){i=n[u.op].call(u,f,p,e);break}f=f[p]}s++}return i}var n={add:function(e,t){return e[t]=this.value,!0},remove:function(e,t){return delete e[t],!0},replace:function(e,t){return e[t]=this.value,!0},move:function(e,t,n){var r={op:"_get",path:this.from};return S(n,[r]),S(n,[{op:"remove",path:this.from}]),S(n,[{op:"add",path:this.path,value:r.value}]),!0},copy:function(e,t,n){var r={op:"_get",path:this.from};return S(n,[r]),S(n,[{op:"add",path:this.path,value:r.value}]),!0},test:function(e,t){return JSON.stringify(e[t])===JSON.stringify(this.value)},_get:function(e,t){this.value=e[t]}},r={add:function(e,t){return e.splice(t,0,this.value),!0},remove:function(e,t){return e.splice(t,1),!0},replace:function(e,t){return e[t]=this.value,!0},move:n.move,copy:n.copy,test:n.test,_get:n._get},i={add:function(e,t){var n={op:"add",path:t+s(this.name),value:this.object[this.name]};e.push(n)},"delete":function(e,t){var n={op:"remove",path:t+s(this.name)};e.push(n)},update:function(e,t){var n={op:"replace",path:t+s(this.name),value:this.object[this.name]};e.push(n)}},a=[];e.intervals;var f=function(){function e(e){this.observers=[],this.obj=e}return e}(),l=function(){function e(e,t){this.callback=e,this.observer=t}return e}();e.unobserve=d,e.observe=v,e.generate=y;var b;Object.keys?b=Object.keys:b=function(e){var t=[];for(var n in e)e.hasOwnProperty(n)&&t.push(n);return t};var E;Array.isArray?E=Array.isArray:E=function(e){return e.push&&typeof e.length=="number"},e.apply=S})(u||(u={})),typeof exports!="undefined"&&(exports.apply=u.apply,exports.observe=u.observe,exports.unobserve=u.unobserve,exports.generate=u.generate),n.PluginHookClass=function(){function r(){this.hooks=e(),this.legacy=t}var e=function(){return{beforeInitWalkontable:[],beforeInit:[],beforeRender:[],beforeChange:[],beforeRemoveCol:[],beforeRemoveRow:[],beforeValidate:[],beforeGet:[],beforeSet:[],beforeGetCellMeta:[],beforeAutofill:[],beforeKeyDown:[],beforeColumnSort:[],afterInit:[],afterLoadData:[],afterUpdateSettings:[],afterRender:[],afterRenderer:[],afterChange:[],afterValidate:[],afterGetCellMeta:[],afterGetColHeader:[],afterGetColWidth:[],afterDestroy:[],afterRemoveRow:[],afterCreateRow:[],afterRemoveCol:[],afterCreateCol:[],afterColumnResize:[],afterColumnMove:[],afterColumnSort:[],afterDeselect:[],afterSelection:[],afterSelectionByProp:[],afterSelectionEnd:[],afterSelectionEndByProp:[],afterCopyLimit:[],afterOnCellMouseDown:[],afterOnCellMouseOver:[],afterOnCellCornerMouseDown:[],afterScrollVertically:[],afterScrollHorizontally:[],modifyCol:[]}},t={onBeforeChange:"beforeChange",onChange:"afterChange",onCreateRow:"afterCreateRow",onCreateCol:"afterCreateCol",onSelection:"afterSelection",onCopyLimit:"afterCopyLimit",onSelectionEnd:"afterSelectionEnd",onSelectionByProp:"afterSelectionByProp",onSelectionEndByProp:"afterSelectionEndByProp"};return r.prototype.add=function(e,r){e in t&&(e=t[e]),typeof this.hooks[e]=="undefined"&&(this.hooks[e]=[]);if(n.helper.isArray(r))for(var i=0,s=r.length;i<s;i++)this.hooks[e].push(r[i]);else{if(this.hooks[e].indexOf(r)>-1)throw new Error("Seems that you are trying to set the same plugin hook twice ("+e+", "+r+")");this.hooks[e].push(r)}return this},r.prototype.once=function(e,t){if(n.helper.isArray(t))for(var r=0,i=t.length;r<i;r++)t[r].runOnce=!0,this.add(e,t[r]);else t.runOnce=!0,this.add(e,t)},r.prototype.remove=function(e,n){var r=!1;e in t&&(e=t[e]);if(typeof this.hooks[e]!="undefined")for(var i=0,s=this.hooks[e].length;i<s;i++)if(this.hooks[e][i]==n){delete this.hooks[e][i].runOnce,this.hooks[e].splice(i,1),r=!0;break}return r},r.prototype.run=function(e,n,r,i,s,o,u){n in t&&(n=t[n]);if(typeof this.hooks[n]!="undefined"){var a=Array.prototype.slice.call(this.hooks[n]);for(var f=0,l=a.length;f<l;f++)a[f].call(e,r,i,s,o,u),a[f].runOnce&&this.remove(n,a[f])}},r.prototype.execute=function(e,n,r,i,s,o,u){var a,f;n in t&&(n=t[n]);if(typeof this.hooks[n]!="undefined"){f=Array.prototype.slice.call(this.hooks[n]);for(var l=0,c=f.length;l<c;l++){a=f[l].call(e,r,i,s,o,u),a!==void 0&&(r=a),f[l].runOnce&&this.remove(n,f[l]);if(a===!1)return!1}}return r},r}(),n.PluginHooks=new n.PluginHookClass,function(e){function t(){function i(e){var t=document,n=this;n.table=t.createElement("table"),n.theadTh=t.createElement("th"),n.table.appendChild(t.createElement("thead")).appendChild(t.createElement("tr")).appendChild(n.theadTh),n.tableStyle=n.table.style,n.tableStyle.tableLayout="auto",n.tableStyle.width="auto",n.tbody=t.createElement("tbody"),n.table.appendChild(n.tbody),n.container=t.createElement("div"),n.container.className=e.rootElement[0].className+" hidden",n.containerStyle=n.container.style,n.container.appendChild(n.table)}var t=this,r=5;this.beforeInit=function(){var e=this;e.autoColumnWidths=[],e.getSettings().autoColumnSize!==!1?e.autoColumnSizeTmp||(e.autoColumnSizeTmp={table:null,tableStyle:null,theadTh:null,tbody:null,container:null,containerStyle:null,determineBeforeNextRender:!0},e.addHook("beforeRender",n.determineIfChanged),e.addHook("afterGetColWidth",n.getColWidth),e.addHook("afterDestroy",n.afterDestroy),e.determineColumnWidth=t.determineColumnWidth):e.autoColumnSizeTmp&&(e.removeHook("beforeRender",n.determineIfChanged),e.removeHook("afterGetColWidth",n.getColWidth),e.removeHook("afterDestroy",n.afterDestroy),delete e.determineColumnWidth,t.afterDestroy.call(e))},this.determineIfChanged=function(e){e&&n.determineColumnsWidth.apply(this,arguments)},this.determineColumnWidth=function(t){var n=this,s=n.autoColumnSizeTmp;s.container||i.call(s,n),s.container.className=n.rootElement[0].className+" htAutoColumnSize",s.table.className=n.$table[0].className;var o=n.countRows(),u={},a=0;for(var f=0;f<o;f++){var l=e.helper.stringify(n.getDataAtCell(f,t)),c=l.length;c>a&&(a=c),u[c]||(u[c]={needed:r,strings:[]}),u[c].needed&&(u[c].strings.push({value:l,row:f}),u[c].needed--)}var h=n.getSettings();h.colHeaders&&n.view.appendColHeader(t,s.theadTh),n.view.wt.wtDom.empty(s.tbody);for(var p in u)if(u.hasOwnProperty(p))for(var d=0,v=u[p].strings.length;d<v;d++){var m=u[p].strings[d].row,g=n.getCellMeta(m,t);g.col=t,g.row=m;var y=n.getCellRenderer(g),b=document.createElement("tr"),w=document.createElement("td");y(n,w,m,t,n.colToProp(t),u[p].strings[d].value,g),f++,b.appendChild(w),s.tbody.appendChild(b)}var E=n.rootElement[0].parentNode;E.appendChild(s.container);var S=n.view.wt.wtDom.outerWidth(s.table);E.removeChild(s.container);if(!h.nativeScrollbars){var x=n.view.wt.wtViewport.getViewportWidth()-2;S>x&&(S=x)}return S},this.determineColumnsWidth=function(){var e=this,n=this.getSettings();if(n.autoColumnSize||!n.colWidths){var r=this.countCols();for(var i=0;i<r;i++)e._getColWidthFromSettings(i)||(this.autoColumnWidths[i]=t.determineColumnWidth.call(e,i))}},this.getColWidth=function(e,t){this.autoColumnWidths[e]&&this.autoColumnWidths[e]>t.width&&(t.width=this.autoColumnWidths[e])},this.afterDestroy=function(){var e=this;e.autoColumnSizeTmp&&e.autoColumnSizeTmp.container&&e.autoColumnSizeTmp.container.parentNode&&e.autoColumnSizeTmp.container.parentNode.removeChild(e.autoColumnSizeTmp.container),e.autoColumnSizeTmp=null}}var n=new t;e.PluginHooks.add("beforeInit",n.beforeInit),e.PluginHooks.add("afterUpdateSettings",n.beforeInit)}(n);var f=new a;n.PluginHooks.add("afterInit",function(){f.init.call(this,"afterInit")}),n.PluginHooks.add("afterUpdateSettings",function(){f.init.call(this,"afterUpdateSettings")}),n.PluginHooks.add("beforeGet",f.onBeforeGetSet),n.PluginHooks.add("beforeSet",f.onBeforeGetSet),n.PluginHooks.add("afterGetColHeader",f.getColHeader),function(t){function n(r,i){function o(){var n=e("body > .htContextMenu")[0];return n||(n=document.createElement("DIV"),t.Dom.addClass(n,"htContextMenu"),document.getElementsByTagName("body")[0].appendChild(n)),n}this.instance=r;var s=this;this.menu=o(),this.enabled=!0,this.bindMouseEvents(),this.bindTableEvents(),this.instance.addHook("afterDestroy",function(){s.destroy()}),this.defaultOptions={items:{row_above:{name:"Insert row above",callback:function(e,t){this.alter("insert_row",t.start.row())},disabled:function(){return this.countRows()>=this.getSettings().maxRows}},row_below:{name:"Insert row below",callback:function(e,t){this.alter("insert_row",t.end.row()+1)},disabled:function(){return this.countRows()>=this.getSettings().maxRows}},hsep1:n.SEPARATOR,col_left:{name:"Insert column on the left",callback:function(e,t){this.alter("insert_col",t.start.col())},disabled:function(){return this.countCols()>=this.getSettings().maxCols}},col_right:{name:"Insert column on the right",callback:function(e,t){this.alter("insert_col",t.end.col()+1)},disabled:function(){return this.countCols()>=this.getSettings().maxCols}},hsep2:n.SEPARATOR,remove_row:{name:"Remove row",callback:function(e,t){var n=t.end.row()-t.start.row()+1;this.alter("remove_row",t.start.row(),n)}},remove_col:{name:"Remove column",callback:function(e,t){var n=t.end.col()-t.start.col()+1;this.alter("remove_col",t.start.col(),n)}},hsep3:n.SEPARATOR,undo:{name:"Undo",callback:function(){this.undo()},disabled:function(){return this.undoRedo&&!this.undoRedo.isUndoAvailable()}},redo:{name:"Redo",callback:function(){this.redo()},disabled:function(){return this.undoRedo&&!this.undoRedo.isRedoAvailable()}}}},this.options={},t.helper.extend(this.options,this.defaultOptions),this.updateOptions(i)}function r(){var e=this,r=e.getSettings().contextMenu,i=t.helper.isObject(r)?r:{};r?(e.contextMenu||(e.contextMenu=new n(e,i)),e.contextMenu.enable(),t.helper.isArray(r)&&e.contextMenu.filterItems(r)):e.contextMenu&&(e.contextMenu.destroy(),delete e.contextMenu)}n.prototype.bindMouseEvents=function(){function r(r){r.preventDefault();if(r.target.nodeName!="TD"&&(!t.Dom.hasClass(r.target,"current")||!t.Dom.hasClass(r.target,"wtBorder")))return;this.show(r.pageY,r.pageX),e(document).on("mousedown.htContextMenu",t.helper.proxy(n.prototype.close,this))}this.instance.rootElement.on("contextmenu.htContextMenu",t.helper.proxy(r,this))},n.prototype.bindTableEvents=function(){var e=this;this._afterScrollCallback=function(){e.close()},this.instance.addHook("afterScrollVertically",this._afterScrollCallback),this.instance.addHook("afterScrollHorizontally",this._afterScrollCallback)},n.prototype.unbindTableEvents=function(){var e=this;this._afterScrollCallback&&(this.instance.removeHook("afterScrollVertically",this._afterScrollCallback),this.instance.removeHook("afterScrollHorizontally",this._afterScrollCallback),this._afterScrollCallback=null)},n.prototype.performAction=function(){var t=e(this.menu).handsontable("getInstance"),r=t.getSelected()[0],i=t.getData()[r];if(i.disabled===!0||typeof i.disabled=="function"&&i.disabled.call(this.instance)===!0)return;if(typeof i.callback!="function")return;var s=this.instance.getSelected(),o=n.utils.normalizeSelection(s);i.callback.call(this.instance,i.key,o)},n.prototype.unbindMouseEvents=function(){this.instance.rootElement.off("contextmenu.htContextMenu"),e(document).off("mousedown.htContextMenu")},n.prototype.show=function(r,i){this.menu.style.display="block",e(this.menu).off("mousedown.htContextMenu").on("mousedown.htContextMenu",t.helper.proxy(this.performAction,this)),e(this.menu).handsontable({data:n.utils.convertItemsToArray(this.getItems()),colHeaders:!1,colWidths:[160],readOnly:!0,copyPaste:!1,columns:[{data:"name",renderer:t.helper.proxy(this.renderer,this)}],beforeKeyDown:t.helper.proxy(this.onBeforeKeyDown,this)}),this.bindTableEvents(),this.setMenuPosition(r,i),e(this.menu).handsontable("listen")},n.prototype.close=function(){this.hide(),e(document).off("mousedown.htContextMenu"),this.unbindTableEvents(),this.instance.listen()},n.prototype.hide=function(){this.menu.style.display="none",e(this.menu).handsontable("destroy")},n.prototype.renderer=function(r,i,s,o,u,a,f){function p(e){return(new RegExp(n.SEPARATOR,"i")).test(e.name)}function d(e,t){return e.disabled===!0||typeof e.disabled=="function"&&e.disabled.call(l.instance)===!0}var l=this,c=r.getData()[s],h=document.createElement("DIV");t.Dom.empty(i),i.appendChild(h),p(c)?t.Dom.addClass(i,"htSeparator"):t.Dom.fastInnerText(h,a),d(c,l.instance)?(t.Dom.addClass(i,"htDisabled"),e(h).on("mouseenter",function(){r.deselectCell()})):(t.Dom.removeClass(i,"htDisabled"),e(h).on("mouseenter",function(){r.selectCell(s,o)}))},n.prototype.onBeforeKeyDown=function(r){function u(e){var t=e.getCell(0,0);n.utils.isSeparator(t)||n.utils.isDisabled(t)?f(0,0,e):e.selectCell(0,0)}function a(e){var t=e.countRows()-1,r=e.getCell(t,0);n.utils.isSeparator(r)||n.utils.isDisabled(r)?l(t,0,e):e.selectCell(t,0)}function f(e,t,r){var i=e+1,s=i<r.countRows()?r.getCell(i,t):null;if(!s)return;n.utils.isSeparator(s)||n.utils.isDisabled(s)?f(i,t,r):r.selectCell(i,t)}function l(e,t,r){var i=e-1,s=i>=0?r.getCell(i,t):null;if(!s)return;n.utils.isSeparator(s)||n.utils.isDisabled(s)?l(i,t,r):r.selectCell(i,t)}var i=this,s=e(i.menu).handsontable("getInstance"),o=s.getSelected();switch(r.keyCode){case t.helper.keyCode.ESCAPE:i.close(),r.preventDefault(),r.stopImmediatePropagation();break;case t.helper.keyCode.ENTER:s.getSelected()&&(i.performAction(),i.close());break;case t.helper.keyCode.ARROW_DOWN:o?f(o[0],o[1],s):u(s),r.preventDefault(),r.stopImmediatePropagation();break;case t.helper.keyCode.ARROW_UP:o?l(o[0],o[1],s):a(s),r.preventDefault(),r.stopImmediatePropagation()}},n.prototype.getItems=function(){function n(e){typeof e=="string"?this.name=e:t.helper.extend(this,e)}var e={};n.prototype=this.options;for(var r in this.options.items)this.options.items.hasOwnProperty(r)&&(!this.itemsFilter||this.itemsFilter.indexOf(r)!=-1)&&(e[r]=new n(this.options.items[r]));return e},n.prototype.updateOptions=function(e){e=e||{};if(e.items)for(var n in e.items){var r={};e.items.hasOwnProperty(n)&&this.defaultOptions.items.hasOwnProperty(n)&&t.helper.isObject(e.items[n])&&(t.helper.extend(r,this.defaultOptions.items[n]),t.helper.extend(r,e.items[n]),e.items[n]=r)}t.helper.extend(this.options,e)},n.prototype.setMenuPosition=function(e,t){var n={top:e,topRelative:e-document.documentElement.scrollTop,left:t,leftRelative:t-document.documentElement.scrollLeft};this.menuFitsBelowCursor(n)?this.positionMenuBelowCursor(n):this.positionMenuAboveCursor(n),this.menuFitsOnRightOfCursor(n)?this.positionMenuOnRightOfCursor(n):this.positionMenuOnLeftOfCursor(n)},n.prototype.menuFitsBelowCursor=function(e){return e.topRelative+this.menu.offsetHeight<=document.documentElement.scrollTop+document.documentElement.clientHeight},n.prototype.menuFitsOnRightOfCursor=function(e){return e.leftRelative+this.menu.offsetWidth<=document.documentElement.scrollLeft+document.documentElement.clientWidth},n.prototype.positionMenuBelowCursor=function(e){this.menu.style.top=e.top+"px"},n.prototype.positionMenuAboveCursor=function(e){this.menu.style.top=e.top-this.menu.offsetHeight+"px"},n.prototype.positionMenuOnRightOfCursor=function(e){this.menu.style.left=e.left+"px"},n.prototype.positionMenuOnLeftOfCursor=function(e){this.menu.style.left=e.left-this.menu.offsetWidth+"px"},n.utils={},n.utils.convertItemsToArray=function(e){var t=[],n;for(var r in e)if(e.hasOwnProperty(r)){if(typeof e[r]=="string")n={name:e[r]};else{if(e[r].visible===!1)continue;n=e[r]}n.key=r,t.push(n)}return t},n.utils.normalizeSelection=function(e){var n={start:new t.SelectionPoint,end:new t.SelectionPoint};return n.start.row(Math.min(e[0],e[2])),n.start.col(Math.min(e[1],e[3])),n.end.row(Math.max(e[0],e[2])),n.end.col(Math.max(e[1],e[3])),n},n.utils.isSeparator=function(e){return t.Dom.hasClass(e,"htSeparator")},n.utils.isDisabled=function(e){return t.Dom.hasClass(e,"htDisabled")},n.prototype.enable=function(){this.enabled||(this.enabled=!0,this.bindMouseEvents())},n.prototype.disable=function(){this.enabled&&(this.enabled=!1,this.close(),this.unbindMouseEvents(),this.unbindTableEvents())},n.prototype.destroy=function(){this.close(),this.unbindMouseEvents(),this.unbindTableEvents(),this.isMenuEnabledByOtherHotInstance()||this.removeMenu()},n.prototype.isMenuEnabledByOtherHotInstance=function(){var t=e(".handsontable"),n=!1;for(var r=0,i=t.length;r<i;r++){var s=e(t[r]).handsontable("getInstance");if(s&&s.getSettings().contextMenu){n=!0;break}}return n},n.prototype.removeMenu=function(){this.menu.parentNode&&this.menu.parentNode.removeChild(this.menu)},n.prototype.filterItems=function(e){this.itemsFilter=e},n.SEPARATOR="---------",t.PluginHooks.add("afterInit",r),t.PluginHooks.add("afterUpdateSettings",r),t.ContextMenu=n}(n),n.PluginHooks.add("beforeGetCellMeta",function(e,t,n){n.isWritable=!n.readOnly;if(n.autoComplete)throw new Error("Support for legacy autocomplete syntax was removed in Handsontable 0.10.0. Please remove the property named 'autoComplete' from your config. For replacement instructions, see wiki page https://github.com/warpech/jquery-handsontable/wiki/Migration-guide-to-0.10.x")});var c=new l;n.PluginHooks.add("beforeInit",c.beforeInit),n.PluginHooks.add("afterInit",function(){c.init.call(this,"afterInit")}),n.PluginHooks.add("afterUpdateSettings",function(){c.init.call(this,"afterUpdateSettings")}),n.PluginHooks.add("afterGetColHeader",c.getColHeader),n.PluginHooks.add("modifyCol",c.modifyCol);var p=new h;n.PluginHooks.add("beforeInit",p.beforeInit),n.PluginHooks.add("afterInit",function(){p.init.call(this,"afterInit")}),n.PluginHooks.add("afterUpdateSettings",function(){p.init.call(this,"afterUpdateSettings")}),n.PluginHooks.add("afterGetColWidth",p.getColWidth),function(){function t(){var e=this,t=e.getSettings().observeChanges;t?(e.observer&&s.call(e),r.call(e),a.call(e)):t||s.call(e)}function r(){var e=this;e.observeChangesActive=!0,e.pauseObservingChanges=function(){e.observeChangesActive=!1},e.resumeObservingChanges=function(){e.observeChangesActive=!0},e.observedData=e.getData(),e.observer=u.observe(e.observedData,function(t){e.observeChangesActive&&(i.call(e,t),e.render()),e.runHooks("afterChangesObserved")})}function i(e){function u(e){var t;return t=f(e),t=a(t),t}function a(e){var t=[];return e.filter(function(e){var n=l(e.path);if(["add","remove"].indexOf(e.op)!=-1&&!isNaN(n.col)){if(t.indexOf(n.col)!=-1)return!1;t.push(n.col)}return!0})}function f(e){return e.filter(function(e){return!/[/]length/ig.test(e.path)})}function l(e){var t=e.match(/^\/(\d+)\/?(.*)?$/);return{row:parseInt(t[1],10),col:/^\d*$/.test(t[2])?parseInt(t[2],10):t[2]}}var t=this,n=u(e);for(var r=0,i=n.length;r<i;r++){var s=n[r],o=l(s.path);switch(s.op){case"add":isNaN(o.col)?t.runHooks("afterCreateRow",o.row):t.runHooks("afterCreateCol",o.col);break;case"remove":isNaN(o.col)?t.runHooks("afterRemoveRow",o.row,1):t.runHooks("afterRemoveCol",o.col,1);break;case"replace":t.runHooks("afterChange",[o.row,o.col,null,s.value],"external")}}}function s(){var e=this;e.observer&&(o.call(e),f.call(e))}function o(){var e=this;u.unobserve(e.observedData,e.observer),delete e.observeChangesActive,delete e.pauseObservingChanges,delete e.resumeObservingChanges}function a(){var e=this;e.addHook("afterDestroy",s),e.addHook("afterCreateRow",l),e.addHook("afterRemoveRow",l),e.addHook("afterCreateCol",l),e.addHook("afterRemoveCol",l),e.addHook("afterChange",function(e,t){t!="loadData"&&l.call(this)})}function f(){var e=this;e.removeHook("afterDestroy",s),e.removeHook("afterCreateRow",l),e.removeHook("afterRemoveRow",l),e.removeHook("afterCreateCol",l),e.removeHook("afterRemoveCol",l),e.removeHook("afterChange",l)}function l(){var e=this;e.pauseObservingChanges(),e.addHookOnce("afterChangesObserved",function(){e.resumeObservingChanges()})}n.PluginHooks.add("afterLoadData",t),n.PluginHooks.add("afterUpdateSettings",t)}(),function(e){function t(){function r(){var e=this;for(var t in n)n.hasOwnProperty(t)&&!s.call(e,t)&&e.PluginHooks.add(t,n[t])}function i(){var e=this;for(var t in n)n.hasOwnProperty(t)&&s.call(e,t)&&e.PluginHooks.remove(t,n[t])}function s(e){var t=this;return t.PluginHooks.hooks.hasOwnProperty(e)}var t=this;this.init=function(){var n=this,s=n.getSettings().persistentState;t.enabled=!!s;if(!t.enabled){i.call(n);return}n.storage||(n.storage=new e(n.rootElement[0].id)),n.resetState=t.resetValue,r.call(n)},this.saveValue=function(e,t){var n=this;n.storage.saveValue(e,t)},this.loadValue=function(e,t){var n=this;t.value=n.storage.loadValue(e)},this.resetValue=function(e){var t=this;typeof e!="undefined"?t.storage.reset(e):t.storage.resetAll()};var n={persistentStateSave:t.saveValue,persistentStateLoad:t.loadValue,persistentStateReset:t.resetValue}}var r=new t;n.PluginHooks.add("beforeInit",r.init),n.PluginHooks.add("afterUpdateSettings",r.init)}(d),function(t){t.UndoRedo=function(e){var n=this;this.instance=e,this.doneActions=[],this.undoneActions=[],this.ignoreNewActions=!1,e.addHook("afterChange",function(e,r){if(e){var i=new t.UndoRedo.ChangeAction(e);n.done(i)}}),e.addHook("afterCreateRow",function(e,r){var i=new t.UndoRedo.CreateRowAction(e,r);n.done(i)}),e.addHook("beforeRemoveRow",function(e,r){var i=n.instance.getData();e=(i.length+e)%i.length;var s=i.slice(e,e+r),o=new t.UndoRedo.RemoveRowAction(e,s);n.done(o)}),e.addHook("afterCreateCol",function(e,r){var i=new t.UndoRedo.CreateColumnAction(e,r);n.done(i)}),e.addHook("beforeRemoveCol",function(r,i){var s=n.instance.getData();r=(n.instance.countCols()+r)%n.instance.countCols();var o=[];for(var u=0,a=s.length;u<a;u++)o[u]=s[u].slice(r,r+i);var f;t.helper.isArray(e.getSettings().colHeaders)&&(f=e.getSettings().colHeaders.slice(r,r+o.length));var l=new t.UndoRedo.RemoveColumnAction(r,o,f);n.done(l)})},t.UndoRedo.prototype.done=function(e){this.ignoreNewActions||(this.doneActions.push(e),this.undoneActions.length=0)},t.UndoRedo.prototype.undo=function(){if(this.isUndoAvailable()){var e=this.doneActions.pop();this.ignoreNewActions=!0,e.undo(this.instance),this.ignoreNewActions=!1,this.undoneActions.push(e)}},t.UndoRedo.prototype.redo=function(){if(this.isRedoAvailable()){var e=this.undoneActions.pop();this.ignoreNewActions=!0,e.redo(this.instance),this.ignoreNewActions=!0,this.doneActions.push(e)}},t.UndoRedo.prototype.isUndoAvailable=function(){return this.doneActions.length>0},t.UndoRedo.prototype.isRedoAvailable=function(){return this.undoneActions.length>0},t.UndoRedo.prototype.clear=function(){this.doneActions.length=0,this.undoneActions.length=0},t.UndoRedo.Action=function(){},t.UndoRedo.Action.prototype.undo=function(){},t.UndoRedo.Action.prototype.redo=function(){},t.UndoRedo.ChangeAction=function(e){this.changes=e},t.helper.inherit(t.UndoRedo.ChangeAction,t.UndoRedo.Action),t.UndoRedo.ChangeAction.prototype.undo=function(t){var n=e.extend(!0,[],this.changes);for(var r=0,i=n.length;r<i;r++)n[r].splice(3,1);t.setDataAtRowProp(n,null,null,"undo")},t.UndoRedo.ChangeAction.prototype.redo=function(t){var n=e.extend(!0,[],this.changes);for(var r=0,i=n.length;r<i;r++)n[r].splice(2,1);t.setDataAtRowProp(n,null,null,"redo")},t.UndoRedo.CreateRowAction=function(e,t){this.index=e,this.amount=t},t.helper.inherit(t.UndoRedo.CreateRowAction,t.UndoRedo.Action),t.UndoRedo.CreateRowAction.prototype.undo=function(e){e.alter("remove_row",this.index,this.amount)},t.UndoRedo.CreateRowAction.prototype.redo=function(e){e.alter("insert_row",this.index+1,this.amount)},t.UndoRedo.RemoveRowAction=function(e,t){this.index=e,this.data=t},t.helper.inherit(t.UndoRedo.RemoveRowAction,t.UndoRedo.Action),t.UndoRedo.RemoveRowAction.prototype.undo=function(e){var t=[this.index,0];Array.prototype.push.apply(t,this.data),Array.prototype.splice.apply(e.getData(),t),e.render()},t.UndoRedo.RemoveRowAction.prototype.redo=function(e){e.alter("remove_row",this.index,this.data.length)},t.UndoRedo.CreateColumnAction=function(e,t){this.index=e,this.amount=t},t.helper.inherit(t.UndoRedo.CreateColumnAction,t.UndoRedo.Action),t.UndoRedo.CreateColumnAction.prototype.undo=function(e){e.alter("remove_col",this.index,this.amount)},t.UndoRedo.CreateColumnAction.prototype.redo=function(e){e.alter("insert_col",this.index+1,this.amount)},t.UndoRedo.RemoveColumnAction=function(e,t,n){this.index=e,this.data=t,this.amount=this.data[0].length,this.headers=
n},t.helper.inherit(t.UndoRedo.RemoveColumnAction,t.UndoRedo.Action),t.UndoRedo.RemoveColumnAction.prototype.undo=function(e){var t,n;for(var r=0,i=e.getData().length;r<i;r++)t=e.getDataAtRow(r),n=[this.index,0],Array.prototype.push.apply(n,this.data[r]),Array.prototype.splice.apply(t,n);typeof this.headers!="undefined"&&(n=[this.index,0],Array.prototype.push.apply(n,this.headers),Array.prototype.splice.apply(e.getSettings().colHeaders,n)),e.render()},t.UndoRedo.RemoveColumnAction.prototype.redo=function(e){e.alter("remove_col",this.index,this.amount)}}(n),function(e){function t(){var t=this,o=typeof t.getSettings().undo=="undefined"||t.getSettings().undo;o?t.undoRedo||(t.undoRedo=new e.UndoRedo(t),i(t),t.addHook("beforeKeyDown",n),t.addHook("afterChange",r)):t.undoRedo&&(delete t.undoRedo,s(t),t.removeHook("beforeKeyDown",n),t.removeHook("afterChange",r))}function n(e){var t=this,n=(e.ctrlKey||e.metaKey)&&!e.altKey;n&&(e.keyCode===89||e.shiftKey&&e.keyCode===90?(t.undoRedo.redo(),e.stopImmediatePropagation()):e.keyCode===90&&(t.undoRedo.undo(),e.stopImmediatePropagation()))}function r(e,t){var n=this;if(t=="loadData")return n.undoRedo.clear()}function i(e){e.undo=function(){return e.undoRedo.undo()},e.redo=function(){return e.undoRedo.redo()},e.isUndoAvailable=function(){return e.undoRedo.isUndoAvailable()},e.isRedoAvailable=function(){return e.undoRedo.isRedoAvailable()},e.clearUndo=function(){return e.undoRedo.clear()}}function s(e){delete e.undo,delete e.redo,delete e.isUndoAvailable,delete e.isRedoAvailable,delete e.clearUndo}e.PluginHooks.add("afterInit",t),e.PluginHooks.add("afterUpdateSettings",t)}(n),v.prototype.setBoundaries=function(e){this.boundaries=e},v.prototype.setCallback=function(e){this.callback=e},v.prototype.check=function(e,t){var n=0,r=0;t<this.boundaries.top?r=t-this.boundaries.top:t>this.boundaries.bottom&&(r=t-this.boundaries.bottom),e<this.boundaries.left?n=e-this.boundaries.left:e>this.boundaries.right&&(n=e-this.boundaries.right),this.callback(n,r)};var m=!1,g,y;if(typeof n!="undefined"){var b=function(e){var n=e.view.wt.wtScrollbars.vertical.scrollHandler;g=new v;if(n===t)return;n?g.setBoundaries(n.getBoundingClientRect()):g.setBoundaries(e.$table[0].getBoundingClientRect()),g.setCallback(function(t,r){t<0?n?n.scrollLeft-=50:e.view.wt.scrollHorizontal(-1).draw():t>0&&(n?n.scrollLeft+=50:e.view.wt.scrollHorizontal(1).draw()),r<0?n?n.scrollTop-=20:e.view.wt.scrollVertical(-1).draw():r>0&&(n?n.scrollTop+=20:e.view.wt.scrollVertical(1).draw())}),m=!0};n.PluginHooks.add("afterInit",function(){e(document).on("mouseup."+this.guid,function(){m=!1}),e(document).on("mousemove."+this.guid,function(e){m&&g.check(e.clientX,e.clientY)})}),n.PluginHooks.add("destroy",function(){e(document).off("."+this.guid)}),n.PluginHooks.add("afterOnCellMouseDown",function(){b(this)}),n.PluginHooks.add("afterOnCellCornerMouseDown",function(){b(this)})}(function(e,t,n){function r(r){function s(){if(!r.isListening())return;r.selection.empty()}function o(e){if(!r.isListening()||!r.selection.isSelected())return;var t=e.replace(/^[\r\n]*/g,"").replace(/[\r\n]*$/g,""),i=n.parse(t),s=r.getSelected(),o=r.getCornerCoords([{row:s[0],col:s[1]},{row:s[2],col:s[3]}]),u=o.TL,a={row:Math.max(o.BR.row,i.length-1+o.TL.row),col:Math.max(o.BR.col,i[0].length-1+o.TL.col)};r.PluginHooks.once("afterChange",function(e,t){e&&e.length&&this.selectCell(u.row,u.col,a.row,a.col)}),r.populateFromArray(u.row,u.col,i,a.row,a.col,"paste",r.getSettings().pasteMode)}function u(t){if(e.helper.isCtrlKey(t.keyCode)&&r.getSelected()){i.setCopyableText(),t.stopImmediatePropagation();return}var n=(t.ctrlKey||t.metaKey)&&!t.altKey;t.keyCode==e.helper.keyCode.A&&n&&setTimeout(e.helper.proxy(i.setCopyableText,i))}this.copyPasteInstance=t.getInstance(),this.copyPasteInstance.onCut(s),this.copyPasteInstance.onPaste(o);var i=this;r.addHook("beforeKeyDown",u),this.destroy=function(){this.copyPasteInstance.removeCallback(s),this.copyPasteInstance.removeCallback(o),this.copyPasteInstance.destroy(),r.removeHook("beforeKeyDown",u)},r.addHook("afterDestroy",e.helper.proxy(this.destroy,this)),this.triggerPaste=e.helper.proxy(this.copyPasteInstance.triggerPaste,this.copyPasteInstance),this.triggerCut=e.helper.proxy(this.copyPasteInstance.triggerCut,this.copyPasteInstance),this.setCopyableText=function(){var e=r.getSelected(),t=r.getSettings(),n=t.copyRowsLimit,i=t.copyColsLimit,s=Math.min(e[0],e[2]),o=Math.min(e[1],e[3]),u=Math.max(e[0],e[2]),a=Math.max(e[1],e[3]),f=Math.min(u,s+n-1),l=Math.min(a,o+i-1);r.copyPaste.copyPasteInstance.copyable(r.getCopyableData(s,o,f,l)),(u!==f||a!==l)&&r.PluginHooks.run("afterCopyLimit",u-s+1,a-o+1,n,i)}}function i(){var e=this,t=e.getSettings().copyPaste!==!1;t&&!e.copyPaste?e.copyPaste=new r(e):!t&&e.copyPaste&&(e.copyPaste.destroy(),delete e.copyPaste)}e.PluginHooks.add("afterInit",i),e.PluginHooks.add("afterUpdateSettings",i)})(n,s,SheetClip),w.prototype.init=function(){this.TABLE=this.instance.wtTable.TABLE,this.fixed=this.instance.wtTable.hider,this.fixedContainer=this.instance.wtTable.holder,this.fixed.style.position="absolute",this.fixed.style.left="0",this.scrollHandler=this.getScrollableElement(this.TABLE),this.$scrollHandler=e(this.scrollHandler)},w.prototype.makeClone=function(e){var t=document.createElement("DIV");t.className="ht_clone_"+e+" handsontable",t.style.position="fixed",t.style.overflow="hidden";var n=document.createElement("TABLE");return n.className=this.instance.wtTable.TABLE.className,t.appendChild(n),this.instance.wtTable.holder.parentNode.appendChild(t),new k({cloneSource:this.instance,cloneOverlay:this,table:n})},w.prototype.getScrollableElement=function(e){var n=e.parentNode;while(n&&n.style){if(n.style.overflow!=="visible"&&n.style.overflow!=="")return n;if(this instanceof R&&n.style.overflowX!=="visible"&&n.style.overflowX!=="")return n;n=n.parentNode}return t},w.prototype.prepare=function(){},w.prototype.onScroll=function(e){this.windowScrollPosition=this.getScrollPosition(),this.readSettings(),e&&(this.windowScrollPosition=e),this.resetFixedPosition()},w.prototype.availableSize=function(){var e;return this.windowScrollPosition>this.tableParentOffset?this.instance.wtTable.getLastVisibleRow()===this.total-1?e=this.instance.wtDom.outerHeight(this.TABLE):e=this.windowSize:e=this.windowSize-this.tableParentOffset,e},w.prototype.refresh=function(e){var t=this.getLastCell();this.measureBefore=this.sumCellSizes(0,this.offset),t===-1?this.measureAfter=0:this.measureAfter=this.sumCellSizes(t,this.total-t),this.applyToDOM(),this.clone&&this.clone.draw(e)},w.prototype.destroy=function(){this.$scrollHandler.off("."+this.instance.guid),e(t).off("."+this.instance.guid),e(document).off("."+this.instance.guid)},E.prototype.appear=function(e){var t,n,r,i,s,o,u,a,f,l,c,h;if(this.disabled)return;var p=this.instance,d,v,m,g,y=!1,b=!1,w=!1,E=!1,S,x,T;p.wtTable.isRowInViewport(e[0])||(y=!0),p.wtTable.isRowInViewport(e[2])||(w=!0),x=p.wtTable.rowStrategy.countVisible();for(S=0;S<x;S++){T=p.wtTable.rowFilter.visibleToSource(S);if(T>=e[0]&&T<=e[2]){d=T;break}}for(S=x-1;S>=0;S--){T=p.wtTable.rowFilter.visibleToSource(S);if(T>=e[0]&&T<=e[2]){m=T;break}}if(y&&w)b=!0,E=!0;else{p.wtTable.isColumnInViewport(e[1])||(b=!0),p.wtTable.isColumnInViewport(e[3])||(E=!0),x=p.wtTable.columnStrategy.countVisible();for(S=0;S<x;S++){T=p.wtTable.columnFilter.visibleToSource(S);if(T>=e[1]&&T<=e[3]){v=T;break}}for(S=x-1;S>=0;S--){T=p.wtTable.columnFilter.visibleToSource(S);if(T>=e[1]&&T<=e[3]){g=T;break}}}if(d===void 0||v===void 0){this.disappear();return}t=d!==m||v!==g,n=p.wtTable.getCell([d,v]),r=t?p.wtTable.getCell([m,g]):n,i=this.wtDom.offset(n),s=t?this.wtDom.offset(r):i,o=this.wtDom.offset(p.wtTable.TABLE),a=i.top,c=s.top+this.wtDom.outerHeight(r)-a,l=i.left,h=s.left+this.wtDom.outerWidth(r)-l,u=a-o.top-1,f=l-o.left-1;var N=this.wtDom.getComputedStyle(n);parseInt(N.borderTopWidth,10)>0&&(u+=1,c=c>0?c-1:0),parseInt(N.borderLeftWidth,10)>0&&(f+=1,h=h>0?h-1:0),y?this.topStyle.display="none":(this.topStyle.top=u+"px",this.topStyle.left=f+"px",this.topStyle.width=h+"px",this.topStyle.display="block"),b?this.leftStyle.display="none":(this.leftStyle.top=u+"px",this.leftStyle.left=f+"px",this.leftStyle.height=c+"px",this.leftStyle.display="block");var C=Math.floor(this.settings.border.width/2);w?this.bottomStyle.display="none":(this.bottomStyle.top=u+c-C+"px",this.bottomStyle.left=f+"px",this.bottomStyle.width=h+"px",this.bottomStyle.display="block"),E?this.rightStyle.display="none":(this.rightStyle.top=u+"px",this.rightStyle.left=f+h-C+"px",this.rightStyle.height=c+1+"px",this.rightStyle.display="block"),w||E||!this.hasSetting(this.settings.border.cornerVisible)?this.cornerStyle.display="none":(this.cornerStyle.top=u+c-4+"px",this.cornerStyle.left=f+h-4+"px",this.cornerStyle.display="block")},E.prototype.disappear=function(){this.topStyle.display="none",this.leftStyle.display="none",this.bottomStyle.display="none",this.rightStyle.display="none",this.cornerStyle.display="none"},E.prototype.hasSetting=function(e){return typeof e=="function"?e():!!e},S.prototype.source=function(e){return e},S.prototype.offsetted=function(e){return e+this.offset},S.prototype.unOffsetted=function(e){return e-this.offset},S.prototype.fixed=function(e){return e<this.fixedCount?e-this.offset:e},S.prototype.unFixed=function(e){return e<this.fixedCount?e+this.offset:e},S.prototype.visibleToSource=function(e){return this.source(this.offsetted(this.fixed(e)))},S.prototype.sourceToVisible=function(e){return this.source(this.unOffsetted(this.unFixed(e)))},x.prototype.getSize=function(e){return this.cellSizes[e]},x.prototype.getContainerSize=function(e){return typeof this.containerSizeFn=="function"?this.containerSizeFn(e):this.containerSizeFn},x.prototype.countVisible=function(){return this.cellCount},x.prototype.isLastIncomplete=function(){if(this.instance.getSetting("nativeScrollbars")){var e=this.instance.cloneFrom?this.instance.cloneFrom.wtScrollbars.vertical:this.instance.wtScrollbars.vertical;return this.remainingSize>e.sumCellSizes(e.offset,e.offset+e.curOuts+1)}return this.remainingSize>0},T.prototype.add=function(e,t,n){this.cache[e]||(this.cache[e]=[]),this.cache[e][t]||(this.cache[e][t]=[]),this.cache[e][t][n]=!0},T.prototype.test=function(e,t,n){return this.cache[e]&&this.cache[e][t]&&this.cache[e][t][n]},N.prototype=new S,N.prototype.readSettings=function(e){this.offset=e.wtSettings.settings.offsetColumn,this.total=e.getSetting("totalColumns"),this.fixedCount=e.getSetting("fixedColumnsLeft"),this.countTH=e.getSetting("rowHeaders").length},N.prototype.offsettedTH=function(e){return e-this.countTH},N.prototype.unOffsettedTH=function(e){return e+this.countTH},N.prototype.visibleRowHeadedColumnToSourceColumn=function(e){return this.visibleToSource(this.offsettedTH(e))},N.prototype.sourceColumnToVisibleRowHeadedColumn=function(e){return this.unOffsettedTH(this.sourceToVisible(e))},C.prototype=new x,C.prototype.getSize=function(e){return this.cellSizes[e]+(this.cellStretch[e]||0)},C.prototype.stretch=function(){var e=this.getContainerSize(this.cellSizesSum),t=0;this.remainingSize=this.cellSizesSum-e,this.cellStretch.length=0;if(this.strategy==="all"){if(this.remainingSize<0){var n=e/this.cellSizesSum,r;while(t<this.cellCount-1)r=Math.floor(n*this.cellSizes[t]),this.remainingSize+=r-this.cellSizes[t],this.cellStretch[t]=r-this.cellSizes[t],t++;this.cellStretch[this.cellCount-1]=-this.remainingSize,this.remainingSize=0}}else this.strategy==="last"&&this.remainingSize<0&&e!==Infinity&&(this.cellStretch[this.cellCount-1]=-this.remainingSize,this.remainingSize=0)},k.prototype.draw=function(e){this.drawInterrupted=!1;if(!e&&!this.wtDom.isVisible(this.wtTable.TABLE)){this.drawInterrupted=!0;return}return this.getSetting("beforeDraw",!e),e=e&&this.getSetting("offsetRow")===this.lastOffsetRow&&this.getSetting("offsetColumn")===this.lastOffsetColumn,this.drawn&&(this.scrollVertical(0),this.scrollHorizontal(0)),this.lastOffsetRow=this.getSetting("offsetRow"),this.lastOffsetColumn=this.getSetting("offsetColumn"),this.wtTable.draw(e),this.cloneSource||this.getSetting("onDraw",!e),this},k.prototype.update=function(e,t){return this.wtSettings.update(e,t)},k.prototype.scrollVertical=function(e){var t=this.wtScroll.scrollVertical(e);return this.getSetting("onScrollVertically"),t},k.prototype.scrollHorizontal=function(e){var t=this.wtScroll.scrollHorizontal(e);return this.getSetting("onScrollHorizontally"),t},k.prototype.scrollViewport=function(e){return this.wtScroll.scrollViewport(e),this},k.prototype.getViewport=function(){return[this.wtTable.rowFilter.visibleToSource(0),this.wtTable.columnFilter.visibleToSource(0),this.wtTable.getLastVisibleRow(),this.wtTable.getLastVisibleColumn()]},k.prototype.getSetting=function(e,t,n,r){return this.wtSettings.getSetting(e,t,n,r)},k.prototype.hasSetting=function(e){return this.wtSettings.has(e)},k.prototype.destroy=function(){e(document.body).off("."+this.guid),this.wtScrollbars.destroy(),clearTimeout(this.wheelTimeout),this.wtEvent&&this.wtEvent.destroy()},L.prototype=new w,L.prototype.resetFixedPosition=function(){if(!this.instance.wtTable.holder.parentNode)return;var e=this.clone.wtTable.holder.parentNode,t=this.instance.wtTable.holder.getBoundingClientRect();e.style.top=Math.ceil(t.top,10)+"px",e.style.left=Math.ceil(t.left,10)+"px"},L.prototype.prepare=function(){},L.prototype.refresh=function(e){this.clone&&this.clone.draw(e)},L.prototype.getScrollPosition=function(){},L.prototype.getLastCell=function(){},L.prototype.applyToDOM=function(){},L.prototype.scrollTo=function(){},L.prototype.readWindowSize=function(){},L.prototype.readSettings=function(){},A.prototype.closest=function(e,t,n){while(e!=null&&e!==n){if(e.nodeType===1&&t.indexOf(e.nodeName)>-1)return e;e=e.parentNode}return null},A.prototype.isChildOf=function(e,t){var n=e.parentNode;while(n!=null){if(n==t)return!0;n=n.parentNode}return!1},A.prototype.index=function(e){var t=0;while(e=e.previousSibling)++t;return t},document.documentElement.classList?(A.prototype.hasClass=function(e,t){return e.classList.contains(t)},A.prototype.addClass=function(e,t){e.classList.add(t)},A.prototype.removeClass=function(e,t){e.classList.remove(t)}):(A.prototype.hasClass=function(e,t){return e.className.match(new RegExp("(\\s|^)"+t+"(\\s|$)"))},A.prototype.addClass=function(e,t){this.hasClass(e,t)||(e.className+=" "+t)},A.prototype.removeClass=function(e,t){if(this.hasClass(e,t)){var n=new RegExp("(\\s|^)"+t+"(\\s|$)");e.className=e.className.replace(n," ").trim()}}),A.prototype.removeTextNodes=function(e,t){if(e.nodeType===3)t.removeChild(e);else if(["TABLE","THEAD","TBODY","TFOOT","TR"].indexOf(e.nodeName)>-1){var n=e.childNodes;for(var r=n.length-1;r>=0;r--)this.removeTextNodes(n[r],e)}},A.prototype.empty=function(e){var t;while(t=e.lastChild)e.removeChild(t)},A.prototype.HTML_CHARACTERS=/(<(.*)>|&(.*);)/,A.prototype.fastInnerHTML=function(e,t){this.HTML_CHARACTERS.test(t)?e.innerHTML=t:this.fastInnerText(e,t)},document.createTextNode("test").textContent?A.prototype.fastInnerText=function(e,t){var n=e.firstChild;n&&n.nodeType===3&&n.nextSibling===null?n.textContent=t:(this.empty(e),e.appendChild(document.createTextNode(t)))}:A.prototype.fastInnerText=function(e,t){var n=e.firstChild;n&&n.nodeType===3&&n.nextSibling===null?n.data=t:(this.empty(e),e.appendChild(document.createTextNode(t)))},A.prototype.isVisible=function(e){var t=e;while(t!==document.documentElement){if(t===null)return!1;if(t.nodeType===11){if(t.host){if(t.host.impl)return A.prototype.isVisible(t.host.impl);if(t.host)return A.prototype.isVisible(t.host);throw new Error("Lost in Web Components world")}return!1}if(t.style.display==="none")return!1;t=t.parentNode}return!0},A.prototype.offset=function(e){if(this.hasCaptionProblem()&&e.firstChild&&e.firstChild.nodeName==="CAPTION"){var n=e.getBoundingClientRect();return{top:n.top+(t.pageYOffset||document.documentElement.scrollTop)-(document.documentElement.clientTop||0),left:n.left+(t.pageXOffset||document.documentElement.scrollLeft)-(document.documentElement.clientLeft||0)}}var r=e.offsetLeft,i=e.offsetTop,s=e;while(e=e.offsetParent){if(e===document.body)break;r+=e.offsetLeft,i+=e.offsetTop,s=e}return s&&s.style.position==="fixed"&&(r+=t.pageXOffset||document.documentElement.scrollLeft,i+=t.pageYOffset||document.documentElement.scrollTop),{left:r,top:i}},A.prototype.getComputedStyle=function(e){return e.currentStyle||document.defaultView.getComputedStyle(e)},A.prototype.outerWidth=function(e){return e.offsetWidth},A.prototype.outerHeight=function(e){return this.hasCaptionProblem()&&e.firstChild&&e.firstChild.nodeName==="CAPTION"?e.offsetHeight+e.firstChild.offsetHeight:e.offsetHeight},function(){function t(){var t=document.createElement("TABLE");t.style.borderSpacing=0,t.style.borderWidth=0,t.style.padding=0;var n=document.createElement("TBODY");t.appendChild(n),n.appendChild(document.createElement("TR")),n.firstChild.appendChild(document.createElement("TD")),n.firstChild.firstChild.innerHTML="<tr><td>t<br>t</td></tr>";var r=document.createElement("CAPTION");r.innerHTML="c<br>c<br>c<br>c",r.style.padding=0,r.style.margin=0,t.insertBefore(r,n),document.body.appendChild(t),e=t.offsetHeight<2*t.lastChild.offsetHeight,document.body.removeChild(t)}function r(){var e=document.createElement("p");e.style.width="100%",e.style.height="200px";var t=document.createElement("div");t.style.position="absolute",t.style.top="0px",t.style.left="0px",t.style.visibility="hidden",t.style.width="200px",t.style.height="150px",t.style.overflow="hidden",t.appendChild(e),(document.body||document.documentElement).appendChild(t);var n=e.offsetWidth;t.style.overflow="scroll";var r=e.offsetWidth;return n==r&&(r=t.clientWidth),(document.body||document.documentElement).removeChild(t),n-r}var e;A.prototype.hasCaptionProblem=function(){return e===void 0&&t(),e},A.prototype.getCaretPosition=function(e){if(e.selectionStart)return e.selectionStart;if(document.selection){e.focus();var t=document.selection.createRange();if(t==null)return 0;var n=e.createTextRange(),r=n.duplicate();return n.moveToBookmark(t.getBookmark()),r.setEndPoint("EndToStart",n),r.text.length}return 0},A.prototype.setCaretPosition=function(e,t,n){n===void 0&&(n=t);if(e.setSelectionRange)e.focus(),e.setSelectionRange(t,n);else if(e.createTextRange){var r=e.createTextRange();r.collapse(!0),r.moveEnd("character",n),r.moveStart("character",t),r.select()}};var n;A.prototype.getScrollbarWidth=function(){return n===void 0&&(n=r()),n}}(),O.prototype.parentCell=function(e){var t={},n=this.instance.wtTable.TABLE,r=this.wtDom.closest(e,["TD","TH"],n);return r&&this.wtDom.isChildOf(r,n)?(t.coords=this.instance.wtTable.getCoords(r),t.TD=r):this.wtDom.hasClass(e,"wtBorder")&&this.wtDom.hasClass(e,"current")&&(t.coords=this.instance.selections.current.selected[0],t.TD=this.instance.wtTable.getCell(t.coords)),t},O.prototype.destroy=function(){clearTimeout(this.dblClickTimeout0),clearTimeout(this.dblClickTimeout1)},t.requestAnimFrame=function(){return t.requestAnimationFrame||t.webkitRequestAnimationFrame||t.mozRequestAnimationFrame||t.oRequestAnimationFrame||t.msRequestAnimationFrame||function(e,n){return t.setTimeout(e,1e3/60)}}(),t.cancelRequestAnimFrame=function(){return t.cancelAnimationFrame||t.webkitCancelRequestAnimationFrame||t.mozCancelRequestAnimationFrame||t.oCancelRequestAnimationFrame||t.msCancelRequestAnimationFrame||clearTimeout}(),t.getComputedStyle||function(){var e,n={getPropertyValue:function(n){return n=="float"&&(n="styleFloat"),e.currentStyle[n.toUpperCase()]||null}};t.getComputedStyle=function(t){return e=t,n}}();if(!String.prototype.trim){var D=/^\s+|\s+$/g;String.prototype.trim=function(){return this.replace(D,"")}}P.prototype=new S,P.prototype.readSettings=function(e){e.cloneOverlay instanceof L?this.offset=0:this.offset=e.wtSettings.settings.offsetRow,this.total=e.getSetting("totalRows"),this.fixedCount=e.getSetting("fixedRowsTop")},H.prototype=new x,H.prototype.add=function(e,t,n){if(!this.isLastIncomplete()&&this.remainingSize!=0){var r=this.sizeAtIndex(e,t);if(r===void 0)return!1;var i=this.getContainerSize(this.cellSizesSum+r);return n?this.cellSizes.unshift(r):this.cellSizes.push(r),this.cellSizesSum+=r,this.cellCount++,this.remainingSize=this.cellSizesSum-i,n&&this.isLastIncomplete()?!1:!0}return!1},H.prototype.remove=function(){var e=this.cellSizes.pop();this.cellSizesSum-=e,this.cellCount--,this.remainingSize-=e},H.prototype.removeOutstanding=function(){while(this.cellCount>0&&this.cellSizes[this.cellCount-1]<this.remainingSize)this.remove()},B.prototype.scrollVertical=function(e){if(!this.instance.drawn)throw new Error("scrollVertical can only be called after table was drawn to DOM");var t=this.instance,n,r=t.getSetting("offsetRow"),i=t.getSetting("fixedRowsTop"),s=t.getSetting("totalRows"),o=t.wtViewport.getViewportHeight();return s>0?n=this.scrollLogicVertical(e,r,s,i,o,function(e){return e-r<i&&e-r>=0?t.getSetting("rowHeight",e-r):t.getSetting("rowHeight",e)},function(e){t.wtTable.verticalRenderReverse=e}):n=0,n!==r&&this.instance.wtScrollbars.vertical.scrollTo(n),t},B.prototype.scrollHorizontal=function(e){if(!this.instance.drawn)throw new Error("scrollHorizontal can only be called after table was drawn to DOM");var t=this.instance,n,r=t.getSetting("offsetColumn"),i=t.getSetting("fixedColumnsLeft"),s=t.getSetting("totalColumns"),o=t.wtViewport.getViewportWidth();return s>0?n=this.scrollLogicHorizontal(e,r,s,i,o,function(e){return e-r<i&&e-r>=0?t.getSetting("columnWidth",e-r):t.getSetting("columnWidth",e)}):n=0,n!==r&&this.instance.wtScrollbars.horizontal.scrollTo(n),t},B.prototype.scrollLogicVertical=function(e,t,n,r,i,s,o){var u=t+e;return u>=n-r&&(u=n-r-1,o(!0)),u<0&&(u=0),u},B.prototype.scrollLogicHorizontal=function(e,t,n,r,i,s){var o=t+e,u=0,a;if(o>r){o>=n-r&&(o=n-r-1),a=o;while(u<i&&a<n)u+=s(a),a++;if(u<i)while(o>0){u+=s(o-1);if(!(u<i))break;o--}}else o<0&&(o=0);return o},B.prototype.scrollViewport=function(e){if(!this.instance.drawn)return;var n=this.instance.getSetting("offsetRow"),r=this.instance.getSetting("offsetColumn"),i=this.instance.wtTable.getLastVisibleRow(),s=this.instance.getSetting("totalRows"),o=this.instance.getSetting("totalColumns"),u=this.instance.getSetting("fixedRowsTop"),a=this.instance.getSetting("fixedColumnsLeft");if(this.instance.getSetting("nativeScrollbars")){var f=this.instance.wtTable.getCell(e);if(typeof f=="object"){var l=A.prototype.offset(f),c=A.prototype.outerWidth(f),h=A.prototype.outerHeight(f),p=this.instance.wtScrollbars.horizontal.getScrollPosition(),d=this.instance.wtScrollbars.vertical.getScrollPosition(),v=A.prototype.outerWidth(this.instance.wtScrollbars.horizontal.scrollHandler),m=A.prototype.outerHeight(this.instance.wtScrollbars.vertical.scrollHandler);this.instance.wtScrollbars.horizontal.scrollHandler!==t&&(l.left=l.left-A.prototype.offset(this.instance.wtScrollbars.horizontal.scrollHandler).left),this.instance.wtScrollbars.vertical.scrollHandler!==t&&(l.top=l.top-A.prototype.offset(this.instance.wtScrollbars.vertical.scrollHandler).top),v-=20,m-=20,c<v&&(l.left<p?this.instance.wtScrollbars.horizontal.setScrollPosition(l.left):l.left+c>p+v&&this.instance.wtScrollbars.horizontal.setScrollPosition(l.left-v+c)),h<m&&(l.top<d?this.instance.wtScrollbars.vertical.setScrollPosition(l.top):l.top+h>d+m&&this.instance.wtScrollbars.vertical.setScrollPosition(l.top-m+h));return}}if(e[0]<0||e[0]>s-1)throw new Error("row "+e[0]+" does not exist");if(e[1]<0||e[1]>o-1)throw new Error("column "+e[1]+" does not exist");e[0]>i?(this.scrollVertical(e[0]-u-n),this.instance.wtTable.verticalRenderReverse=!0):e[0]===i&&this.instance.wtTable.rowStrategy.isLastIncomplete()?(this.scrollVertical(e[0]-u-n),this.instance.wtTable.verticalRenderReverse=!0):e[0]-u<n?this.scrollVertical(e[0]-u-n):this.scrollVertical(0);if(this.instance.wtTable.isColumnBeforeViewport(e[1]))this.instance.wtScrollbars.horizontal.scrollTo(e[1]-a);else if(this.instance.wtTable.isColumnAfterViewport(e[1])||this.instance.wtTable.getLastVisibleColumn()===e[1]&&!this.instance.wtTable.isLastColumnFullyVisible()){var g=0;for(var y=0;y<a;y++)g+=this.instance.getSetting("columnWidth",y);var b=e[1];g+=this.instance.getSetting("columnWidth",b);var w=this.instance.wtViewport.getViewportWidth();if(g<w){var E=this.instance.getSetting("columnWidth",b-1);while(g+E<=w&&b>=a)b--,g+=E,E=this.instance.getSetting("columnWidth",b-1)}this.instance.wtScrollbars.horizontal.scrollTo(b-a)}return this.instance},j.prototype.init=function(){var t=this;this.$table=e(this.instance.wtTable.TABLE),this.slider=document.createElement("DIV"),this.sliderStyle=this.slider.style,this.sliderStyle.position="absolute",this.sliderStyle.top="0",this.sliderStyle.left="0",this.sliderStyle.display="none",this.slider.className="dragdealer "+this.type,this.handle=document.createElement("DIV"),this.handleStyle=this.handle.style,this.handle.className="handle",this.slider.appendChild(this.handle),this.container=this.instance.wtTable.holder,this.container.appendChild(this.slider);var n=!0;this.dragTimeout=null;var r,i=function(){t.onScroll(r)};this.dragdealer=new Y(this.slider,{vertical:this.type==="vertical",horizontal:this.type==="horizontal",slide:!1,speed:100,animationCallback:function(e,s){if(n){n=!1;return}t.skipRefresh=!0,r=t.type==="vertical"?s:e,t.dragTimeout===null&&(t.dragTimeout=setInterval(i,100),i())},callback:function(e,n){t.skipRefresh=!1,clearInterval(t.dragTimeout),t.dragTimeout=null,r=t.type==="vertical"?n:e,t.onScroll(r)}}),this.skipRefresh=!1},j.prototype.onScroll=function(e){if(this.instance.drawn){this.readSettings();if(this.total>this.visibleCount){var t=Math.round(this.handlePosition*this.total/this.sliderSize);e===1?this.type==="vertical"?this.instance.scrollVertical(Infinity).draw():this.instance.scrollHorizontal(Infinity).draw():t!==this.offset?this.type==="vertical"?this.instance.scrollVertical(t-this.offset).draw():this.instance.scrollHorizontal(t-this.offset).draw():this.refresh()}}},j.prototype.getHandleSizeRatio=function(e,t){return!t||e>t||e==t?1:1/t},j.prototype.prepare=function(){if(this.skipRefresh)return;var e=this.getHandleSizeRatio(this.visibleCount,this.total);(e===1||isNaN(e))&&this.scrollMode==="auto"||this.scrollMode==="none"?this.visible=!1:this.visible=!0},j.prototype.refresh=function(){if(this.skipRefresh)return;if(!this.visible){this.sliderStyle.display="none";return}var e,t,n,r,i=this.visibleCount,s=this.instance.wtViewport.getWorkspaceWidth(),o=this.instance.wtViewport.getWorkspaceHeight();s===Infinity&&(s=this.instance.wtViewport.getWorkspaceActualWidth()),o===Infinity&&(o=this.instance.wtViewport.getWorkspaceActualHeight()),this.type==="vertical"?(this.instance.wtTable.rowStrategy.isLastIncomplete()&&i--,t=o-2,this.sliderStyle.top=this.instance.wtDom.offset(this.$table[0]).top-this.instance.wtDom.offset(this.container).top+"px",this.sliderStyle.left=s-1+"px",this.sliderStyle.height=Math.max(t,0)+"px"):(t=s-2,this.sliderStyle.left=this.instance.wtDom.offset(this.$table[0]).left-this.instance.wtDom.offset(this.container).left+"px",this.sliderStyle.top=o-1+"px",this.sliderStyle.width=Math.max(t,0)+"px"),e=this.getHandleSizeRatio(i,this.total),n=Math.round(t*e),n<10&&(n=15),r=Math.floor(t*(this.offset/this.total)),n+r>t&&(r=t-n),this.type==="vertical"?(this.handleStyle.height=n+"px",this.handleStyle.top=r+"px"):(this.handleStyle.width=n+"px",this.handleStyle.left=r+"px"),this.sliderStyle.display="block"},j.prototype.destroy=function(){clearInterval(this.dragdealer.interval)};var F=function(e){this.instance=e,this.type="vertical",this.init()};F.prototype=new j,F.prototype.scrollTo=function(e){this.instance.update("offsetRow",e)},F.prototype.readSettings=function(){this.scrollMode=this.instance.getSetting("scrollV"),this.offset=this.instance.getSetting("offsetRow"),this.total=this.instance.getSetting("totalRows"),this.visibleCount=this.instance.wtTable.rowStrategy.countVisible(),this.visibleCount>1&&this.instance.wtTable.rowStrategy.isLastIncomplete()&&this.visibleCount--,this.handlePosition=parseInt(this.handleStyle.top,10),this.sliderSize=parseInt(this.sliderStyle.height,10),this.fixedCount=this.instance.getSetting("fixedRowsTop")};var I=function(e){this.instance=e,this.type="horizontal",this.init()};I.prototype=new j,I.prototype.scrollTo=function(e){this.instance.update("offsetColumn",e)},I.prototype.readSettings=function(){this.scrollMode=this.instance.getSetting("scrollH"),this.offset=this.instance.getSetting("offsetColumn"),this.total=this.instance.getSetting("totalColumns"),this.visibleCount=this.instance.wtTable.columnStrategy.countVisible(),this.visibleCount>1&&this.instance.wtTable.columnStrategy.isLastIncomplete()&&this.visibleCount--,this.handlePosition=parseInt(this.handleStyle.left,10),this.sliderSize=parseInt(this.sliderStyle.width,10),this.fixedCount=this.instance.getSetting("fixedColumnsLeft")},I.prototype.getHandleSizeRatio=function(e,t){return!t||e>t||e==t?1:e/t},q.prototype=new w,q.prototype.resetFixedPosition=function(){if(!this.instance.wtTable.holder.parentNode)return;var e=this.clone.wtTable.holder.parentNode,n;if(this.scrollHandler===t){n=this.instance.wtTable.hider.getBoundingClientRect();var r=Math.ceil(n.top,10),i=Math.ceil(n.bottom,10);r<0&&i>0?e.style.top="0":e.style.top=r+"px";var s=Math.ceil(n.left,10),o=Math.ceil(n.right,10);s<0&&o>0?e.style.left="0":e.style.left=s+"px"}else n=this.scrollHandler.getBoundingClientRect(),e.style.top=Math.ceil(n.top,10)+"px",e.style.left=Math.ceil(n.left,10)+"px";e.style.width=A.prototype.outerWidth(this.clone.wtTable.TABLE)+4+"px",e.style.height=A.prototype.outerHeight(this.clone.wtTable.TABLE)+4+"px"},q.prototype.prepare=function(){},q.prototype.refresh=function(e){this.measureBefore=0,this.measureAfter=0,this.clone&&this.clone.draw(e)},q.prototype.getScrollPosition=function(){},q.prototype.getLastCell=function(){},q.prototype.applyToDOM=function(){},q.prototype.scrollTo=function(){},q.prototype.readWindowSize=function(){},q.prototype.readSettings=function(){},R.prototype=new w,R.prototype.resetFixedPosition=function(){if(!this.instance.wtTable.holder.parentNode)return;var e=this.clone.wtTable.holder.parentNode,n;if(this.scrollHandler===t){n=this.instance.wtTable.hider.getBoundingClientRect();var r=Math.ceil(n.left,10),i=Math.ceil(n.right,10);r<0&&i>0?e.style.left="0":e.style.left=r+"px"}else n=this.scrollHandler.getBoundingClientRect(),e.style.top=Math.ceil(n.top,10)+"px",e.style.left=Math.ceil(n.left,10)+"px"},R.prototype.react=function(){if(!this.instance.wtTable.holder.parentNode)return;var e=this.clone.wtTable.holder.parentNode;if(this.instance.wtScrollbars.vertical.scrollHandler===t){var n=this.instance.wtTable.hider.getBoundingClientRect();e.style.top=Math.ceil(n.top,10)+"px",e.style.height=A.prototype.outerHeight(this.clone.wtTable.TABLE)+"px"}else this.clone.wtTable.holder.style.top=-(this.instance.wtScrollbars.vertical.windowScrollPosition-this.instance.wtScrollbars.vertical.measureBefore)+"px",e.style.height=this.instance.wtViewport.getWorkspaceHeight()+"px";e.style.width=A.prototype.outerWidth(this.clone.wtTable.TABLE)+4+"px"},R.prototype.prepare=function(){},R.prototype.refresh=function(e){this.measureBefore=0,this.measureAfter=0,this.clone&&this.clone.draw(e)},R.prototype.getScrollPosition=function(){return this.scrollHandler===t?this.scrollHandler.scrollX:this.scrollHandler.scrollLeft},R.prototype.setScrollPosition=function(e){this.scrollHandler.scrollLeft=e},R.prototype.onScroll=function(){w.prototype.onScroll.apply(this,arguments),this.instance.getSetting("onScrollHorizontally")},R.prototype.getLastCell=function(){return this.instance.wtTable.getLastVisibleColumn()},R.prototype.applyToDOM=function(){this.fixedContainer.style.paddingLeft=this.measureBefore+"px",this.fixedContainer.style.paddingRight=this.measureAfter+"px"},R.prototype.scrollTo=function(e){this.$scrollHandler.scrollLeft(this.tableParentOffset+e*this.cellSize)},R.prototype.readWindowSize=function(){this.scrollHandler===t?(this.windowSize=document.documentElement.clientWidth,this.tableParentOffset=this.instance.wtTable.holderOffset.left):(this.windowSize=A.prototype.outerWidth(this.scrollHandler),this.tableParentOffset=0),this.windowScrollPosition=this.getScrollPosition()},R.prototype.readSettings=function(){this.offset=this.instance.getSetting("offsetColumn"),this.total=this.instance.getSetting("totalColumns")},U.prototype=new w,U.prototype.resetFixedPosition=function(){if(!this.instance.wtTable.holder.parentNode)return;var e=this.clone.wtTable.holder.parentNode,n;if(this.scrollHandler===t){n=this.instance.wtTable.hider.getBoundingClientRect();var r=Math.ceil(n.top,10),i=Math.ceil(n.bottom,10);r<0&&i>0?e.style.top="0"
:e.style.top=r+"px"}else n=this.instance.wtScrollbars.horizontal.scrollHandler.getBoundingClientRect(),e.style.top=Math.ceil(n.top,10)+"px",e.style.left=Math.ceil(n.left,10)+"px";this.instance.wtScrollbars.horizontal.scrollHandler===t?e.style.width=this.instance.wtViewport.getWorkspaceActualWidth()+"px":e.style.width=A.prototype.outerWidth(this.instance.wtTable.holder.parentNode)+"px",e.style.height=A.prototype.outerHeight(this.clone.wtTable.TABLE)+4+"px"},U.prototype.react=function(){if(!this.instance.wtTable.holder.parentNode)return;if(this.instance.wtScrollbars.horizontal.scrollHandler!==t){var e=this.clone.wtTable.holder.parentNode;e.firstChild.style.left=-this.instance.wtScrollbars.horizontal.windowScrollPosition+"px"}},U.prototype.getScrollPosition=function(){return this.scrollHandler===t?this.scrollHandler.scrollY:this.scrollHandler.scrollTop},U.prototype.setScrollPosition=function(e){this.scrollHandler.scrollTop=e},U.prototype.onScroll=function(e){w.prototype.onScroll.apply(this,arguments);var t,n=0;t=this.windowScrollPosition-this.tableParentOffset,z=0;if(t>0){var r=0,i;for(var s=0;s<this.total;s++){i=this.instance.getSetting("rowHeight",s),r+=i;if(r>t)break}this.offset>0&&(z=r-t),n=s,n=Math.min(n,this.total)}this.curOuts=n>this.maxOuts?this.maxOuts:n,n-=this.curOuts,this.instance.update("offsetRow",n),this.readSettings(),this.instance.draw(),this.instance.getSetting("onScrollVertically")},U.prototype.getLastCell=function(){return this.instance.getSetting("offsetRow")+this.instance.wtTable.tbodyChildrenLength-1};var z=0;U.prototype.sumCellSizes=function(e,t){var n=0;while(e<t)n+=this.instance.getSetting("rowHeight",e),e++;return n},U.prototype.applyToDOM=function(){var e=this.instance.wtViewport.getColumnHeaderHeight();this.fixedContainer.style.height=e+this.sumCellSizes(0,this.total)+4+"px",this.fixed.style.top=this.measureBefore+"px",this.fixed.style.bottom=""},U.prototype.scrollTo=function(e){var t=this.tableParentOffset+e*this.cellSize;this.$scrollHandler.scrollTop(t),this.onScroll(t)},U.prototype.readWindowSize=function(){this.scrollHandler===t?(this.windowSize=document.documentElement.clientHeight,this.tableParentOffset=this.instance.wtTable.holderOffset.top):(this.windowSize=this.scrollHandler.clientHeight,this.tableParentOffset=0),this.windowScrollPosition=this.getScrollPosition()},U.prototype.readSettings=function(){this.offset=this.instance.getSetting("offsetRow"),this.total=this.instance.getSetting("totalRows")},W.prototype.registerListeners=function(){function f(){if(!n.instance.wtTable.holder.parentNode){n.destroy();return}n.vertical.windowScrollPosition=n.vertical.getScrollPosition(),n.horizontal.windowScrollPosition=n.horizontal.getScrollPosition(),n.box=n.instance.wtTable.hider.getBoundingClientRect(),(n.box.width!==u||n.box.height!==a)&&n.instance.rowHeightCache&&(u=n.box.width,a=n.box.height,n.instance.draw());if(n.vertical.windowScrollPosition!==r||n.horizontal.windowScrollPosition!==i||n.box.top!==s||n.box.left!==o)n.vertical.onScroll(),n.horizontal.onScroll(),n.vertical.react(),n.horizontal.react(),r=n.vertical.windowScrollPosition,i=n.horizontal.windowScrollPosition,s=n.box.top,o=n.box.left}var n=this,r,i,s,o,u,a,l=e(t);this.vertical.$scrollHandler.on("scroll."+this.instance.guid,f),this.vertical.scrollHandler!==this.horizontal.scrollHandler&&this.horizontal.$scrollHandler.on("scroll."+this.instance.guid,f),this.vertical.scrollHandler!==t&&this.horizontal.scrollHandler!==t&&l.on("scroll."+this.instance.guid,f),l.on("load."+this.instance.guid,f),l.on("resize."+this.instance.guid,f),e(document).on("ready."+this.instance.guid,f),setInterval(f,100)},W.prototype.destroy=function(){this.vertical&&this.vertical.destroy(),this.horizontal&&this.horizontal.destroy()},W.prototype.refresh=function(e){this.horizontal&&this.horizontal.readSettings(),this.vertical&&this.vertical.readSettings(),this.horizontal&&this.horizontal.prepare(),this.vertical&&this.vertical.prepare(),this.horizontal&&this.horizontal.refresh(e),this.vertical&&this.vertical.refresh(e),this.corner&&this.corner.refresh(e),this.debug&&this.debug.refresh(e)},X.prototype.add=function(e){this.selected.push(e)},X.prototype.clear=function(){this.selected.length=0},X.prototype.getCorners=function(){var e,t,n,r,i,s=this.selected.length;if(s>0){e=n=this.selected[0][0],t=r=this.selected[0][1];if(s>1)for(i=1;i<s;i++)this.selected[i][0]<e?e=this.selected[i][0]:this.selected[i][0]>n&&(n=this.selected[i][0]),this.selected[i][1]<t?t=this.selected[i][1]:this.selected[i][1]>r&&(r=this.selected[i][1])}return[e,t,n,r]},X.prototype.draw=function(){var e,t,n,r,i,s=this.instance.wtTable.rowStrategy.countVisible(),o=this.instance.wtTable.columnStrategy.countVisible();if(this.selected.length){e=this.getCorners();for(t=0;t<s;t++)for(n=0;n<o;n++)r=this.instance.wtTable.rowFilter.visibleToSource(t),i=this.instance.wtTable.columnFilter.visibleToSource(n),r>=e[0]&&r<=e[2]&&i>=e[1]&&i<=e[3]?this.instance.wtTable.currentCellCache.add(t,n,this.settings.className):r>=e[0]&&r<=e[2]?this.instance.wtTable.currentCellCache.add(t,n,this.settings.highlightRowClassName):i>=e[1]&&i<=e[3]&&this.instance.wtTable.currentCellCache.add(t,n,this.settings.highlightColumnClassName);this.border&&this.border.appear(e)}else this.border&&this.border.disappear()},V.prototype.update=function(e,t){if(t===void 0)for(var n in e)e.hasOwnProperty(n)&&(this.settings[n]=e[n]);else this.settings[e]=t;return this.instance},V.prototype.getSetting=function(e,t,n,r){return this[e]?this[e](t,n,r):this._getSetting(e,t,n,r)},V.prototype._getSetting=function(e,t,n,r){return typeof this.settings[e]=="function"?this.settings[e](t,n,r):t!==void 0&&Object.prototype.toString.call(this.settings[e])==="[object Array]"?this.settings[e][t]:this.settings[e]},V.prototype.has=function(e){return!!this.settings[e]},V.prototype.rowHeight=function(e,t){this.instance.rowHeightCache||(this.instance.rowHeightCache=[]);if(this.instance.rowHeightCache[e]===void 0){var n=23;return t&&(n=this.instance.wtDom.outerHeight(t),this.instance.rowHeightCache[e]=n),n}return this.instance.rowHeightCache[e]},$.prototype.refreshHiderDimensions=function(){var e=this.instance.wtViewport.getWorkspaceHeight(),t=this.instance.wtViewport.getWorkspaceWidth(),n=this.spreader.style;e===Infinity&&t===Infinity||!!this.instance.getSetting("nativeScrollbars")?(n.position="relative",n.width="auto",n.height="auto"):(e===Infinity&&(e=this.instance.wtViewport.getWorkspaceActualHeight()),t===Infinity&&(t=this.instance.wtViewport.getWorkspaceActualWidth()),this.hiderStyle.overflow="hidden",n.position="absolute",n.top="0",n.left="0",this.instance.getSetting("nativeScrollbars")||(n.height="4000px",n.width="4000px"),e<0&&(e=0),this.hiderStyle.height=e+"px",this.hiderStyle.width=t+"px")},$.prototype.refreshStretching=function(){if(this.instance.cloneSource)return;var e=this.instance,t=e.getSetting("stretchH"),n=e.getSetting("totalRows"),r=e.getSetting("totalColumns"),i=e.getSetting("offsetColumn"),s=function(e){var t=o.instance.wtViewport.getViewportWidth(e);return t<e&&o.instance.getSetting("nativeScrollbars")?Infinity:t},o=this,u=function(t){var n=o.columnFilter.visibleToSource(t);if(n<r)return e.getSetting("columnWidth",n)};t==="hybrid"&&(i>0?t="last":t="none");var a=function(e){return o.instance.getSetting("nativeScrollbars")?o.instance.cloneOverlay instanceof L?Infinity:2*o.instance.wtViewport.getViewportHeight(e):o.instance.wtViewport.getViewportHeight(e)},f=function(e,t){if(o.instance.getSetting("nativeScrollbars"))return 20;var r=o.rowFilter.visibleToSource(e);if(r<n)return o.verticalRenderReverse&&e===0?o.instance.getSetting("rowHeight",r,t)-1:o.instance.getSetting("rowHeight",r,t)};this.columnStrategy=new C(e,s,u,t),this.rowStrategy=new H(e,a,f)},$.prototype.adjustAvailableNodes=function(){var e,t=this.instance.getSetting("rowHeaders"),n=t.length,r=this.instance.getSetting("columnHeaders"),i,s,o;while(this.colgroupChildrenLength<n)this.COLGROUP.appendChild(document.createElement("COL")),this.colgroupChildrenLength++;this.refreshStretching(),this.instance.cloneSource&&(this.instance.cloneOverlay instanceof R||this.instance.cloneOverlay instanceof q)?e=this.instance.getSetting("fixedColumnsLeft"):e=this.columnStrategy.cellCount;while(this.colgroupChildrenLength<e+n)this.COLGROUP.appendChild(document.createElement("COL")),this.colgroupChildrenLength++;while(this.colgroupChildrenLength>e+n)this.COLGROUP.removeChild(this.COLGROUP.lastChild),this.colgroupChildrenLength--;i=this.THEAD.firstChild;if(r.length){i||(i=document.createElement("TR"),this.THEAD.appendChild(i)),this.theadChildrenLength=i.childNodes.length;while(this.theadChildrenLength<e+n)i.appendChild(document.createElement("TH")),this.theadChildrenLength++;while(this.theadChildrenLength>e+n)i.removeChild(i.lastChild),this.theadChildrenLength--}else i&&this.wtDom.empty(i);for(o=0;o<this.colgroupChildrenLength;o++)o<n?this.wtDom.addClass(this.COLGROUP.childNodes[o],"rowHeader"):this.wtDom.removeClass(this.COLGROUP.childNodes[o],"rowHeader");if(r.length){i=this.THEAD.firstChild;if(n){s=i.firstChild;for(o=0;o<n;o++)t[o](-n+o,s),s=s.nextSibling}}for(o=0;o<e;o++)r.length&&r[0](this.columnFilter.visibleToSource(o),i.childNodes[n+o])},$.prototype.adjustColumns=function(e,t){var n=e.childNodes.length;while(n<t){var r=document.createElement("TD");e.appendChild(r),n++}while(n>t)e.removeChild(e.lastChild),n--},$.prototype.draw=function(e){return this.instance.getSetting("nativeScrollbars")&&(this.verticalRenderReverse=!1),this.rowFilter.readSettings(this.instance),this.columnFilter.readSettings(this.instance),e?this.instance.wtScrollbars&&this.instance.wtScrollbars.refresh(!0):(this.instance.getSetting("nativeScrollbars")?this.instance.cloneSource?this.tableOffset=this.instance.cloneSource.wtTable.tableOffset:(this.holderOffset=this.wtDom.offset(this.holder),this.tableOffset=this.wtDom.offset(this.TABLE),this.instance.wtScrollbars.vertical.readWindowSize(),this.instance.wtScrollbars.horizontal.readWindowSize(),this.instance.wtViewport.resetSettings()):(this.tableOffset=this.wtDom.offset(this.TABLE),this.instance.wtViewport.resetSettings()),this._doDraw()),this.refreshPositions(e),e||this.instance.getSetting("nativeScrollbars")&&(this.instance.cloneSource||(this.instance.wtScrollbars.vertical.resetFixedPosition(),this.instance.wtScrollbars.horizontal.resetFixedPosition(),this.instance.wtScrollbars.corner.resetFixedPosition(),this.instance.wtScrollbars.debug&&this.instance.wtScrollbars.debug.resetFixedPosition())),this.instance.drawn=!0,this},$.prototype._doDraw=function(){var e=0,t,n,r,i=this.instance.getSetting("offsetRow"),s=this.instance.getSetting("totalRows"),o=this.instance.getSetting("totalColumns"),u,a=this.instance.getSetting("rowHeaders"),f=a.length,l,c,h,p=!1,d,v,m;this.verticalRenderReverse&&(v=i);var g=!1;this.verticalRenderReverse&&(i===s-this.rowFilter.fixedCount-1?g=!0:(this.instance.update("offsetRow",i+1),this.rowFilter.readSettings(this.instance))),this.instance.cloneSource&&(this.columnStrategy=this.instance.cloneSource.wtTable.columnStrategy,this.rowStrategy=this.instance.cloneSource.wtTable.rowStrategy);if(o>0){t=this.rowFilter.visibleToSource(e);var y=this.instance.getSetting("fixedRowsTop"),b;this.instance.cloneSource&&(this.instance.cloneOverlay instanceof U||this.instance.cloneOverlay instanceof q?b=y:this.instance.cloneOverlay instanceof R&&(b=this.rowStrategy.countVisible())),this.adjustAvailableNodes(),p=!0,this.instance.cloneSource&&(this.instance.cloneOverlay instanceof R||this.instance.cloneOverlay instanceof q)?u=this.instance.getSetting("fixedColumnsLeft"):u=this.columnStrategy.cellCount,this.instance.cloneSource||(d=this.instance.wtViewport.getWorkspaceWidth(),this.columnStrategy.stretch());for(n=0;n<u;n++)this.COLGROUP.childNodes[n+f].style.width=this.columnStrategy.getSize(n)+"px";while(t<s&&t>=0){if(e>1e3)throw new Error("Security brake: Too much TRs. Please define height for your table, which will enforce scrollbars.");if(b!==void 0&&e===b)break;if(e>=this.tbodyChildrenLength||this.verticalRenderReverse&&e>=this.rowFilter.fixedCount){l=document.createElement("TR");for(n=0;n<f;n++)l.appendChild(document.createElement("TH"));this.verticalRenderReverse&&e>=this.rowFilter.fixedCount?this.TBODY.insertBefore(l,this.TBODY.childNodes[this.rowFilter.fixedCount]||this.TBODY.firstChild):this.TBODY.appendChild(l),this.tbodyChildrenLength++}else e===0?l=this.TBODY.firstChild:l=l.nextSibling;h=l.firstChild;for(n=0;n<f;n++)h.nodeName=="TD"&&(c=h,h=document.createElement("TH"),l.insertBefore(h,c),l.removeChild(c)),a[n](t,h),h=h.nextSibling;this.adjustColumns(l,u+f);for(n=0;n<u;n++)r=this.columnFilter.visibleToSource(n),n===0?c=l.childNodes[this.columnFilter.sourceColumnToVisibleRowHeadedColumn(r)]:c=c.nextSibling,c.nodeName=="TH"&&(h=c,c=document.createElement("TD"),l.insertBefore(c,h),l.removeChild(h)),c.className="",c.removeAttribute("style"),this.instance.getSetting("cellRenderer",t,r,c);i=this.instance.getSetting("offsetRow");if(this.verticalRenderReverse&&g){if(-this.wtDom.outerHeight(l.firstChild)<this.rowStrategy.remainingSize){this.TBODY.removeChild(l),this.instance.update("offsetRow",i+1),this.tbodyChildrenLength--,this.rowFilter.readSettings(this.instance);break}this.instance.cloneSource||(m=this.rowStrategy.add(e,c,this.verticalRenderReverse),m===!1&&this.rowStrategy.removeOutstanding())}else if(!this.instance.cloneSource){m=this.rowStrategy.add(e,c,this.verticalRenderReverse),m===!1&&(this.instance.getSetting("nativeScrollbars")||this.rowStrategy.removeOutstanding());if(this.rowStrategy.isLastIncomplete()){if(this.verticalRenderReverse&&!this.isRowInViewport(v)){this.verticalRenderReverse=!1,this.instance.update("offsetRow",v),this.draw();return}break}}this.instance.getSetting("nativeScrollbars")&&(this.instance.cloneSource?l.style.height=this.instance.getSetting("rowHeight",t)+"px":this.instance.getSetting("rowHeight",t,c));if(this.verticalRenderReverse&&e>=this.rowFilter.fixedCount){if(i===0)break;this.instance.update("offsetRow",i-1),this.rowFilter.readSettings(this.instance)}else e++;t=this.rowFilter.visibleToSource(e)}}p||this.adjustAvailableNodes();if(!(this.instance.cloneOverlay instanceof L)){e=this.rowStrategy.countVisible();while(this.tbodyChildrenLength>e)this.TBODY.removeChild(this.TBODY.lastChild),this.tbodyChildrenLength--}this.instance.wtScrollbars&&this.instance.wtScrollbars.refresh(!1);if(!this.instance.cloneSource&&d!==this.instance.wtViewport.getWorkspaceWidth()){this.columnStrategy.stretch();for(n=0;n<this.columnStrategy.cellCount;n++)this.COLGROUP.childNodes[n+f].style.width=this.columnStrategy.getSize(n)+"px"}this.verticalRenderReverse=!1},$.prototype.refreshPositions=function(e){this.refreshHiderDimensions(),this.refreshSelections(e)},$.prototype.refreshSelections=function(e){var t,n,r,i,s,o,u=[],a=this.rowStrategy.countVisible(),f=this.columnStrategy.countVisible();this.oldCellCache=this.currentCellCache,this.currentCellCache=new T;if(this.instance.selections)for(n in this.instance.selections)this.instance.selections.hasOwnProperty(n)&&(this.instance.selections[n].draw(),this.instance.selections[n].settings.className&&u.push(this.instance.selections[n].settings.className),this.instance.selections[n].settings.highlightRowClassName&&u.push(this.instance.selections[n].settings.highlightRowClassName),this.instance.selections[n].settings.highlightColumnClassName&&u.push(this.instance.selections[n].settings.highlightColumnClassName));o=u.length;for(t=0;t<a;t++)for(r=0;r<f;r++){n=this.rowFilter.visibleToSource(t),i=this.columnFilter.visibleToSource(r);for(s=0;s<o;s++)this.currentCellCache.test(t,r,u[s])?this.wtDom.addClass(this.getCell([n,i]),u[s]):e&&this.oldCellCache.test(t,r,u[s])&&this.wtDom.removeClass(this.getCell([n,i]),u[s])}},$.prototype.getCell=function(e){return this.isRowBeforeViewport(e[0])?-1:this.isRowAfterViewport(e[0])?-2:this.isColumnBeforeViewport(e[1])?-3:this.isColumnAfterViewport(e[1])?-4:this.TBODY.childNodes[this.rowFilter.sourceToVisible(e[0])].childNodes[this.columnFilter.sourceColumnToVisibleRowHeadedColumn(e[1])]},$.prototype.getCoords=function(e){return[this.rowFilter.visibleToSource(this.wtDom.index(e.parentNode)),this.columnFilter.visibleRowHeadedColumnToSourceColumn(e.cellIndex)]},$.prototype.getLastVisibleRow=function(){return this.rowFilter.visibleToSource(this.rowStrategy.cellCount-1)},$.prototype.getLastVisibleColumn=function(){return this.columnFilter.visibleToSource(this.columnStrategy.cellCount-1)},$.prototype.isRowBeforeViewport=function(e){return this.rowFilter.sourceToVisible(e)<this.rowFilter.fixedCount&&e>=this.rowFilter.fixedCount},$.prototype.isRowAfterViewport=function(e){return e>this.getLastVisibleRow()},$.prototype.isColumnBeforeViewport=function(e){return this.columnFilter.sourceToVisible(e)<this.columnFilter.fixedCount&&e>=this.columnFilter.fixedCount},$.prototype.isColumnAfterViewport=function(e){return e>this.getLastVisibleColumn()},$.prototype.isRowInViewport=function(e){return!this.isRowBeforeViewport(e)&&!this.isRowAfterViewport(e)},$.prototype.isColumnInViewport=function(e){return!this.isColumnBeforeViewport(e)&&!this.isColumnAfterViewport(e)},$.prototype.isLastRowFullyVisible=function(){return this.getLastVisibleRow()===this.instance.getSetting("totalRows")-1&&!this.rowStrategy.isLastIncomplete()},$.prototype.isLastColumnFullyVisible=function(){return this.getLastVisibleColumn()===this.instance.getSetting("totalColumns")-1&&!this.columnStrategy.isLastIncomplete()},J.prototype.getWorkspaceHeight=function(e){if(this.instance.getSetting("nativeScrollbars"))return this.instance.wtScrollbars.vertical.windowSize;var t=this.instance.getSetting("height");if(t===Infinity||t===void 0||t===null||t<1)this.instance.wtScrollbars.vertical instanceof w?t=this.instance.wtScrollbars.vertical.availableSize():t=Infinity;return t!==Infinity&&(e>=t?t-=this.instance.getSetting("scrollbarHeight"):this.instance.wtScrollbars.horizontal.visible&&(t-=this.instance.getSetting("scrollbarHeight"))),t},J.prototype.getWorkspaceWidth=function(e){var t=this.instance.getSetting("width");if(t===Infinity||t===void 0||t===null||t<1)this.instance.wtScrollbars.horizontal instanceof w?t=this.instance.wtScrollbars.horizontal.availableSize():t=Infinity;return t!==Infinity&&(e>=t?t-=this.instance.getSetting("scrollbarWidth"):this.instance.wtScrollbars.vertical.visible&&(t-=this.instance.getSetting("scrollbarWidth"))),t},J.prototype.getWorkspaceActualHeight=function(){return this.instance.wtDom.outerHeight(this.instance.wtTable.TABLE)},J.prototype.getWorkspaceActualWidth=function(){return this.instance.wtDom.outerWidth(this.instance.wtTable.TABLE)||this.instance.wtDom.outerWidth(this.instance.wtTable.TBODY)||this.instance.wtDom.outerWidth(this.instance.wtTable.THEAD)},J.prototype.getColumnHeaderHeight=function(){if(isNaN(this.columnHeaderHeight)){var e=this.instance.wtDom.offset(this.instance.wtTable.TBODY),t=this.instance.wtTable.tableOffset;this.columnHeaderHeight=e.top-t.top}return this.columnHeaderHeight},J.prototype.getViewportHeight=function(e){var t=this.getWorkspaceHeight(e);if(t===Infinity)return t;var n=this.getColumnHeaderHeight();return n>0?t-n:t},J.prototype.getRowHeaderWidth=function(){if(this.instance.cloneSource)return this.instance.cloneSource.wtViewport.getRowHeaderWidth();if(isNaN(this.rowHeaderWidth)){var e=this.instance.getSetting("rowHeaders");if(e.length){var t=this.instance.wtTable.TABLE.querySelector("TH");this.rowHeaderWidth=0;for(var n=0,r=e.length;n<r;n++)t?(this.rowHeaderWidth+=this.instance.wtDom.outerWidth(t),t=t.nextSibling):this.rowHeaderWidth+=50}else this.rowHeaderWidth=0}return this.rowHeaderWidth},J.prototype.getViewportWidth=function(e){var t=this.getWorkspaceWidth(e);if(t===Infinity)return t;var n=this.getRowHeaderWidth();return n>0?t-n:t},J.prototype.resetSettings=function(){this.rowHeaderWidth=NaN,this.columnHeaderHeight=NaN};var Q={x:0,y:0,init:function(){this.setEvent("mouse"),this.setEvent("touch")},setEvent:function(e){var t=document["on"+e+"move"]||function(){};document["on"+e+"move"]=function(e){t(e),Q.refresh(e)}},refresh:function(e){e||(e=t.event),e.type=="mousemove"?this.set(e):e.touches&&this.set(e.touches[0])},set:function(e){e.pageX||e.pageY?(this.x=e.pageX,this.y=e.pageY):document.body&&(e.clientX||e.clientY)&&(this.x=e.clientX+document.body.scrollLeft+document.documentElement.scrollLeft,this.y=e.clientY+document.body.scrollTop+document.documentElement.scrollTop)}};Q.init();var G={get:function(e){var t=0,n=0;if(e.offsetParent)do n+=e.offsetLeft,t+=e.offsetTop;while(e=e.offsetParent);return[n,t]}},Y=function(e,t){typeof e=="string"&&(e=document.getElementById(e));if(!e)return;var n=e.getElementsByTagName("div")[0];if(!n||n.className.search(/(^|\s)handle(\s|$)/)==-1)return;this.init(e,n,t||{}),this.setup()};Y.prototype={init:function(e,t,n){this.wrapper=e,this.handle=t,this.options=n,this.disabled=this.getOption("disabled",!1),this.horizontal=this.getOption("horizontal",!0),this.vertical=this.getOption("vertical",!1),this.slide=this.getOption("slide",!0),this.steps=this.getOption("steps",0),this.snap=this.getOption("snap",!1),this.loose=this.getOption("loose",!1),this.speed=this.getOption("speed",10)/100,this.xPrecision=this.getOption("xPrecision",0),this.yPrecision=this.getOption("yPrecision",0),this.callback=n.callback||null,this.animationCallback=n.animationCallback||null,this.bounds={left:n.left||0,right:-(n.right||0),top:n.top||0,bottom:-(n.bottom||0),x0:0,x1:0,xRange:0,y0:0,y1:0,yRange:0},this.value={prev:[-1,-1],current:[n.x||0,n.y||0],target:[n.x||0,n.y||0]},this.offset={wrapper:[0,0],mouse:[0,0],prev:[-999999,-999999],current:[0,0],target:[0,0]},this.change=[0,0],this.activity=!1,this.dragging=!1,this.tapping=!1},getOption:function(e,t){return this.options[e]!==undefined?this.options[e]:t},setup:function(){this.setWrapperOffset(),this.setBoundsPadding(),this.setBounds(),this.setSteps(),this.addListeners()},setWrapperOffset:function(){this.offset.wrapper=G.get(this.wrapper)},setBoundsPadding:function(){!this.bounds.left&&!this.bounds.right&&(this.bounds.left=G.get(this.handle)[0]-this.offset.wrapper[0],this.bounds.right=-this.bounds.left),!this.bounds.top&&!this.bounds.bottom&&(this.bounds.top=G.get(this.handle)[1]-this.offset.wrapper[1],this.bounds.bottom=-this.bounds.top)},setBounds:function(){this.bounds.x0=this.bounds.left,this.bounds.x1=this.wrapper.offsetWidth+this.bounds.right,this.bounds.xRange=this.bounds.x1-this.bounds.x0-this.handle.offsetWidth,this.bounds.y0=this.bounds.top,this.bounds.y1=this.wrapper.offsetHeight+this.bounds.bottom,this.bounds.yRange=this.bounds.y1-this.bounds.y0-this.handle.offsetHeight,this.bounds.xStep=1/(this.xPrecision||Math.max(this.wrapper.offsetWidth,this.handle.offsetWidth)),this.bounds.yStep=1/(this.yPrecision||Math.max(this.wrapper.offsetHeight,this.handle.offsetHeight))},setSteps:function(){if(this.steps>1){this.stepRatios=[];for(var e=0;e<=this.steps-1;e++)this.stepRatios[e]=e/(this.steps-1)}},addListeners:function(){var e=this;this.wrapper.onselectstart=function(){return!1},this.handle.onmousedown=this.handle.ontouchstart=function(t){e.handleDownHandler(t)},this.wrapper.onmousedown=this.wrapper.ontouchstart=function(t){e.wrapperDownHandler(t)};var n=document.onmouseup||function(){};document.onmouseup=function(t){n(t),e.documentUpHandler(t)};var r=document.ontouchend||function(){};document.ontouchend=function(t){r(t),e.documentUpHandler(t)};var i=t.onresize||function(){};t.onresize=function(t){i(t),e.documentResizeHandler(t)},this.wrapper.onmousemove=function(t){e.activity=!0},this.wrapper.onclick=function(t){return!e.activity},this.interval=setInterval(function(){e.animate()},25),e.animate(!1,!0)},handleDownHandler:function(e){this.activity=!1,Q.refresh(e),this.preventDefaults(e,!0),this.startDrag()},wrapperDownHandler:function(e){Q.refresh(e),this.preventDefaults(e,!0),this.startTap()},documentUpHandler:function(e){this.stopDrag(),this.stopTap()},documentResizeHandler:function(e){this.setWrapperOffset(),this.setBounds(),this.update()},enable:function(){this.disabled=!1,this.handle.className=this.handle.className.replace(/\s?disabled/g,"")},disable:function(){this.disabled=!0,this.handle.className+=" disabled"},setStep:function(e,t,n){this.setValue(this.steps&&e>1?(e-1)/(this.steps-1):0,this.steps&&t>1?(t-1)/(this.steps-1):0,n)},setValue:function(e,t,n){this.setTargetValue([e,t||0]),n&&this.groupCopy(this.value.current,this.value.target)},startTap:function(e){if(this.disabled)return;this.tapping=!0,this.setWrapperOffset(),this.setBounds(),e===undefined&&(e=[Q.x-this.offset.wrapper[0]-this.handle.offsetWidth/2,Q.y-this.offset.wrapper[1]-this.handle.offsetHeight/2]),this.setTargetOffset(e)},stopTap:function(){if(this.disabled||!this.tapping)return;this.tapping=!1,this.setTargetValue(this.value.current),this.result()},startDrag:function(){if(this.disabled)return;this.setWrapperOffset(),this.setBounds(),this.offset.mouse=[Q.x-G.get(this.handle)[0],Q.y-G.get(this.handle)[1]],this.dragging=!0},stopDrag:function(){if(this.disabled||!this.dragging)return;this.dragging=!1;var e=this.groupClone(this.value.current);if(this.slide){var t=this.change;e[0]+=t[0]*4,e[1]+=t[1]*4}this.setTargetValue(e),this.result()},feedback:function(){var e=this.value.current;this.snap&&this.steps>1&&(e=this.getClosestSteps(e)),this.groupCompare(e,this.value.prev)||(typeof this.animationCallback=="function"&&this.animationCallback(e[0],e[1]),this.groupCopy(this.value.prev,e))},result:function(){typeof this.callback=="function"&&this.callback(this.value.target[0],this.value.target[1])},animate:function(e,t){if(e&&!this.dragging)return;if(this.dragging){var n=this.groupClone(this.value.target),r=[Q.x-this.offset.wrapper[0]-this.offset.mouse[0],Q.y-this.offset.wrapper[1]-this.offset.mouse[1]];this.setTargetOffset(r,this.loose),this.change=[this.value.target[0]-n[0],this.value.target[1]-n[1]]}(this.dragging||t)&&this.groupCopy(this.value.current,this.value.target);if(this.dragging||this.glide()||t)this.update(),this.feedback()},glide:function(){var e=[this.value.target[0]-this.value.current[0],this.value.target[1]-this.value.current[1]];return!e[0]&&!e[1]?!1:(Math.abs(e[0])>this.bounds.xStep||Math.abs(e[1])>this.bounds.yStep?(this.value.current[0]+=e[0]*this.speed,this.value.current[1]+=e[1]*this.speed):this.groupCopy(this.value.current,this.value.target),!0)},update:function(){this.snap?this.offset.current=this.getOffsetsByRatios(this.getClosestSteps(this.value.current)):this.offset.current=this.getOffsetsByRatios(this.value.current),this.show()},show:function(){this.groupCompare(this.offset.current,this.offset.prev)||(this.horizontal&&(this.handle.style.left=String(this.offset.current[0])+"px"),this.vertical&&(this.handle.style.top=String(this.offset.current[1])+"px"),this.groupCopy(this.offset.prev,this.offset.current))},setTargetValue:function(e,t){var n=t?this.getLooseValue(e):this.getProperValue(e);this.groupCopy(this.value.target,n),this.offset.target=this.getOffsetsByRatios(n)},setTargetOffset:function(e,t){var n=this.getRatiosByOffsets(e),r=t?this.getLooseValue(n):this.getProperValue(n);this.groupCopy(this.value.target,r),this.offset.target=this.getOffsetsByRatios(r)},getLooseValue:function(e){var t=this.getProperValue(e);return[t[0]+(e[0]-t[0])/4,t[1]+(e[1]-t[1])/4]},getProperValue:function(e){var t=this.groupClone(e);return t[0]=Math.max(t[0],0),t[1]=Math.max(t[1],0),t[0]=Math.min(t[0],1),t[1]=Math.min(t[1],1),(!this.dragging&&!this.tapping||this.snap)&&this.steps>1&&(t=this.getClosestSteps(t)),t},getRatiosByOffsets:function(e){return[this.getRatioByOffset(e[0],this.bounds.xRange,this.bounds.x0),this.getRatioByOffset(e[1],this.bounds.yRange,this.bounds.y0)]},getRatioByOffset:function(e,t,n){return t?(e-n)/t:0},getOffsetsByRatios:function(e){return[this.getOffsetByRatio(e[0],this.bounds.xRange,this.bounds.x0),this.getOffsetByRatio(e[1],this.bounds.yRange,this.bounds.y0)]},getOffsetByRatio:function(e,t,n){return Math.round(e*t)+n},getClosestSteps:function(e){return[this.getClosestStep(e[0]),this.getClosestStep(e[1])]},getClosestStep:function(e){var t=0,n=1;for(var r=0;r<=this.steps-1;r++)Math.abs(this.stepRatios[r]-e)<n&&(n=Math.abs(this.stepRatios[r]-e),t=r);return this.stepRatios[t]},groupCompare:function(e,t){return e[0]==t[0]&&e[1]==t[1]},groupCopy:function(e,t){e[0]=t[0],e[1]=t[1]},groupClone:function(e){return[e[0],e[1]]},preventDefaults:function(e,n){e||(e=t.event),e.preventDefault&&e.preventDefault(),e.returnValue=!1,n&&document.selection&&document.selection.empty()},cancelEvent:function(e){e||(e=t.event),e.stopPropagation&&e.stopPropagation(),e.cancelBubble=!0}},function(e){typeof define=="function"&&define.amd?define(["jquery"],e):typeof exports=="object"?module.exports=e:e(jQuery)}(function(e){function u(n){var r=n||t.event,o=[].slice.call(arguments,1),u=0,a=0,f=0,l=0,c=0,h;n=e.event.fix(r),n.type="mousewheel",r.wheelDelta&&(u=r.wheelDelta),r.detail&&(u=r.detail*-1),r.deltaY&&(f=r.deltaY*-1,u=f),r.deltaX&&(a=r.deltaX,u=a*-1),r.wheelDeltaY!==undefined&&(f=r.wheelDeltaY),r.wheelDeltaX!==undefined&&(a=r.wheelDeltaX*-1),l=Math.abs(u);if(!i||l<i)i=l;c=Math.max(Math.abs(f),Math.abs(a));if(!s||c<s)s=c;return h=u>0?"floor":"ceil",u=Math[h](u/i),a=Math[h](a/s),f=Math[h](f/s),o.unshift(n,u,a,f),(e.event.dispatch||e.event.handle).apply(this,o)}var n=["wheel","mousewheel","DOMMouseScroll","MozMousePixelScroll"],r="onwheel"in document||document.documentMode>=9?["wheel"]:["mousewheel","DomMouseScroll","MozMousePixelScroll"],i,s;if(e.event.fixHooks)for(var o=n.length;o;)e.event.fixHooks[n[--o]]=e.event.mouseHooks;e.event.special.mousewheel={setup:function(){if(this.addEventListener)for(var e=r.length;e;)this.addEventListener(r[--e],u,!1);else this.onmousewheel=u},teardown:function(){if(this.removeEventListener)for(var e=r.length;e;)this.removeEventListener(r[--e],u,!1);else this.onmousewheel=null}},e.fn.extend({mousewheel:function(e){return e?this.bind("mousewheel",e):this.trigger("mousewheel")},unmousewheel:function(e){return this.unbind("mousewheel",e)}})})})(jQuery,window,Handsontable),function(){function o(e){this._n=e}function u(e,t,n){var r=Math.pow(10,t),i;i=(Math.round(e*r)/r).toFixed(t);if(n){var s=new RegExp("0{1,"+n+"}$");i=i.replace(s,"")}return i}function a(e,t){var n;return t.indexOf("$")>-1?n=l(e,t):t.indexOf("%")>-1?n=c(e,t):t.indexOf(":")>-1?n=h(e,t):n=d(e,t),n}function f(e,t){if(t.indexOf(":")>-1)e._n=p(t);else if(t===i)e._n=0;else{var s=t;n[r].delimiters.decimal!=="."&&(t=t.replace(/\./g,"").replace(n[r].delimiters.decimal,"."));var o=new RegExp(n[r].abbreviations.thousand+"(?:\\)|(\\"+n[r].currency.symbol+")?(?:\\))?)?$"),u=new RegExp(n[r].abbreviations.million+"(?:\\)|(\\"+n[r].currency.symbol+")?(?:\\))?)?$"),a=new RegExp(n[r].abbreviations.billion+"(?:\\)|(\\"+n[r].currency.symbol+")?(?:\\))?)?$"),f=new RegExp(n[r].abbreviations.trillion+"(?:\\)|(\\"+n[r].currency.symbol+")?(?:\\))?)?$"),l=["KB","MB","GB","TB","PB","EB","ZB","YB"],c=!1;for(var h=0;h<=l.length;h++){c=t.indexOf(l[h])>-1?Math.pow(1024,h+1):!1;if(c)break}e._n=(c?c:1)*(s.match(o)?Math.pow(10,3):1)*(s.match(u)?Math.pow(10,6):1)*(s.match(a)?Math.pow(10,9):1)*(s.match(f)?Math.pow(10,12):1)*(t.indexOf("%")>-1?.01:1)*Number((t.indexOf("(")>-1?"-":"")+t.replace(/[^0-9\.-]+/g,"")),e._n=c?Math.ceil(e._n):e._n}return e._n}function l(e,t){var i=t.indexOf("$")<=1?!0:!1,s="";t.indexOf(" $")>-1?(s=" ",t=t.replace(" $","")):t.indexOf("$ ")>-1?(s=" ",t=t.replace("$ ","")):t=t.replace("$","");var o=a(e,t);return i?o.indexOf("(")>-1||o.indexOf("-")>-1?(o=o.split(""),o.splice(1,0,n[r].currency.symbol+s),o=o.join("")):o=n[r].currency.symbol+s+o:o.indexOf(")")>-1?(o=o.split(""),o.splice(-1,0,s+n[r].currency.symbol),o=o.join("")):o=o+s+n[r].currency.symbol,o}function c(e,t){var n="";t.indexOf(" %")>-1?(n=" ",t=t.replace(" %","")):t=t.replace("%",""),e._n=e._n*100;var r=a(e,t);return r.indexOf(")")>-1?(r=r.split(""),r.splice(-1,0,n+"%"),r=r.join("")):r=r+n+"%",r}function h(e,t){var n=Math.floor(e._n/60/60),r=Math.floor((e._n-n*60*60)/60),i=Math.round(e._n-n*60*60-r*60);return n+":"+(r<10?"0"+r:r)+":"+(i<10?"0"+i:i)}function p(e){var t=e.split(":"),n=0;return t.length===3?(n+=Number(t[0])*60*60,n+=Number(t[1])*60,n+=Number(t[2])):t.lenght===2&&(n+=Number(t[0])*60,n+=Number(t[1])),Number(n)}function d(e,t){var s=!1,o=!1,a="",f="",l="",c=Math.abs(e._n);if(e._n===0&&i!==null)return i;t.indexOf("(")>-1&&(s=!0,t=t.slice(1,-1)),t.indexOf("a")>-1&&(t.indexOf(" a")>-1?(a=" ",t=t.replace(" a","")):t=t.replace("a",""),c>=Math.pow(10,12)?(a+=n[r].abbreviations.trillion,e._n=e._n/Math.pow(10,12)):c<Math.pow(10,12)&&c>=Math.pow(10,9)?(a+=n[r].abbreviations.billion,e._n=e._n/Math.pow(10,9)):c<Math.pow(10,9)&&c>=Math.pow(10,6)?(a+=n[r].abbreviations
.million,e._n=e._n/Math.pow(10,6)):c<Math.pow(10,6)&&c>=Math.pow(10,3)&&(a+=n[r].abbreviations.thousand,e._n=e._n/Math.pow(10,3)));if(t.indexOf("b")>-1){t.indexOf(" b")>-1?(f=" ",t=t.replace(" b","")):t=t.replace("b","");var h=["B","KB","MB","GB","TB","PB","EB","ZB","YB"],p,d;for(var v=0;v<=h.length;v++){p=Math.pow(1024,v),d=Math.pow(1024,v+1);if(e._n>=p&&e._n<d){f+=h[v],p>0&&(e._n=e._n/p);break}}}t.indexOf("o")>-1&&(t.indexOf(" o")>-1?(l=" ",t=t.replace(" o","")):t=t.replace("o",""),l+=n[r].ordinal(e._n)),t.indexOf("[.]")>-1&&(o=!0,t=t.replace("[.]","."));var m=e._n.toString().split(".")[0],g=t.split(".")[1],y=t.indexOf(","),b="",w=!1;return g?(g.indexOf("[")>-1?(g=g.replace("]",""),g=g.split("["),b=u(e._n,g[0].length+g[1].length,g[1].length)):b=u(e._n,g.length),m=b.split(".")[0],b.split(".")[1].length?b=n[r].delimiters.decimal+b.split(".")[1]:b="",o&&Number(b)===0&&(b="")):m=u(e._n,null),m.indexOf("-")>-1&&(m=m.slice(1),w=!0),y>-1&&(m=m.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1"+n[r].delimiters.thousands)),t.indexOf(".")===0&&(m=""),(s&&w?"(":"")+(!s&&w?"-":"")+m+b+(l?l:"")+(a?a:"")+(f?f:"")+(s&&w?")":"")}function v(e,t){n[e]=t}var e,t="1.4.7",n={},r="en",i=null,s=typeof module!="undefined"&&module.exports;e=function(t){return e.isNumeral(t)?t=t.value():Number(t)||(t=0),new o(Number(t))},e.version=t,e.isNumeral=function(e){return e instanceof o},e.language=function(t,i){return t?(t&&!i&&(r=t),(i||!n[t])&&v(t,i),e):r},e.language("en",{delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(e){var t=e%10;return~~(e%100/10)===1?"th":t===1?"st":t===2?"nd":t===3?"rd":"th"},currency:{symbol:"$"}}),e.zeroFormat=function(e){typeof e=="string"?i=e:i=null},e.fn=o.prototype={clone:function(){return e(this)},format:function(t){return a(this,t?t:e.defaultFormat)},unformat:function(t){return f(this,t?t:e.defaultFormat)},value:function(){return this._n},valueOf:function(){return this._n},set:function(e){return this._n=Number(e),this},add:function(e){return this._n=this._n+Number(e),this},subtract:function(e){return this._n=this._n-Number(e),this},multiply:function(e){return this._n=this._n*Number(e),this},divide:function(e){return this._n=this._n/Number(e),this},difference:function(e){var t=this._n-Number(e);return t<0&&(t=-t),t}},s&&(module.exports=e),typeof ender=="undefined"&&(this.numeral=e),typeof define=="function"&&define.amd&&define([],function(){return e})}.call(this);