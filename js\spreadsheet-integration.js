// js/spreadsheet-integration.js

/**
 * Simplified Spreadsheet Integration Helper
 * Provides basic utilities for interacting with the spreadsheet
 */
export class SpreadsheetIntegration {
    constructor() {
        this.initialized = false;
        this.invoicesData = null;
    }

    /**
     * Initialize the spreadsheet integration
     */
    init() {
        if (this.initialized) {
            console.log('Spreadsheet integration already initialized');
            return true;
        }

        try {
            console.log('Initializing spreadsheet integration...');

            // Initialize properties
            this.spreadsheetReadyListenerSet = false;

            // Set up event listener for when the spreadsheet is ready
            document.addEventListener('spreadsheetReady', () => {
                console.log('Spreadsheet is ready, applying any pending data');
                if (this.invoicesData) {
                    this.applyPendingData();
                }
            });
            this.spreadsheetReadyListenerSet = true;

            // Check if spreadsheet is already available
            if (window.spreadsheet) {
                console.log('Spreadsheet is already available during initialization');
                document.dispatchEvent(new CustomEvent('spreadsheetReady'));
            }

            this.initialized = true;
            console.log('Spreadsheet integration initialized successfully');
            return true;
        } catch (error) {
            console.error('Failed to initialize spreadsheet integration:', error);
            return false;
        }
    }

    /**
     * Apply any pending data to the spreadsheet when it becomes available
     */
    applyPendingData() {
        if (!this.invoicesData) {
            console.log('No pending invoice data to apply');
            return;
        }

        try {
            console.log('Applying pending invoice data to spreadsheet');

            // DIRECT APPROACH: Try to insert data directly to the current sheet
            // This is the most direct approach and should work regardless of the spreadsheet implementation
            if (this.insertDirectlyToCurrentSheet(this.invoicesData)) {
                console.log('Successfully inserted data directly to current sheet');
                this.invoicesData = null; // Clear pending data
                return;
            }

            // If direct insertion failed, try other methods

            // Check if spreadsheet is available through window.spreadsheet
            if (window.spreadsheet) {
                console.log('Spreadsheet is available via window.spreadsheet, inserting data now');
                this.insertInvoicesData(this.invoicesData);
                this.invoicesData = null; // Clear pending data
            }
            // Check if we can access the current sheet directly
            else if (window.currentSheet) {
                console.log('Current sheet is available via window.currentSheet, inserting data now');
                this.insertInvoicesDataToCurrentSheet(this.invoicesData);
                this.invoicesData = null; // Clear pending data
            }
            // Check if we can access the workbook directly
            else if (window.workbook) {
                console.log('Workbook is available via window.workbook, trying to get active sheet');
                try {
                    const sheet = window.workbook.activeSheet();
                    if (sheet) {
                        console.log('Found active sheet from workbook, inserting data now');
                        this.insertInvoicesDataToSheet(this.invoicesData, sheet);
                        this.invoicesData = null; // Clear pending data
                    } else {
                        console.log('No active sheet found in workbook, will try again later');
                        setTimeout(() => this.applyPendingData(), 1000);
                    }
                } catch (sheetError) {
                    console.error('Error getting active sheet from workbook:', sheetError);
                    setTimeout(() => this.applyPendingData(), 1000);
                }
            }
            // Check if there's an Excel table in the DOM
            else if (document.getElementById('excelTable')) {
                console.log('Excel table found in DOM, trying direct DOM insertion');
                this.insertInvoicesDataToDom(this.invoicesData);
                this.invoicesData = null; // Clear pending data
            }
            else {
                console.log('No spreadsheet or sheet available yet, will try again later');
                // Schedule another check in 1 second
                setTimeout(() => this.applyPendingData(), 1000);
            }
        } catch (error) {
            console.error('Error applying pending data:', error);
        }
    }

    /**
     * Insert data directly to the current sheet without relying on any specific API
     * This is a fallback method that should work with any spreadsheet implementation
     * @param {Array} invoices - The invoices to insert
     * @returns {boolean} - Whether the insertion was successful
     */
    insertDirectlyToCurrentSheet(invoices) {
        try {
            console.log('Attempting direct insertion to current sheet');

            // Validate invoices data
            if (!invoices || !Array.isArray(invoices) || invoices.length === 0) {
                console.error('Invalid invoices data for direct insertion');
                return false;
            }

            // Extract headers from the first invoice
            const headers = this.extractHeaders(invoices[0]);
            console.log('Extracted headers for direct insertion:', headers.length, 'headers');

            // Prepare data for insertion
            const data = [];
            data.push(headers); // Add headers as first row

            // Add invoice data
            invoices.forEach((invoice, index) => {
                try {
                    const row = headers.map(header => {
                        // Handle nested properties like ShipToContact
                        if (header.includes('.')) {
                            const [parent, child] = header.split('.');
                            return invoice[parent] && invoice[parent][child] && invoice[parent][child].value !== undefined
                                ? invoice[parent][child].value
                                : '';
                        }

                        // Handle regular properties
                        return invoice[header] && invoice[header].value !== undefined
                            ? invoice[header].value
                            : '';
                    });

                    data.push(row);
                } catch (rowError) {
                    console.error(`Error processing invoice at index ${index} for direct insertion:`, rowError);
                }
            });

            console.log('Prepared data for direct insertion, rows:', data.length);

            // Find the table element in the DOM
            const table = document.querySelector('table.excel-table') || document.querySelector('table');

            if (!table) {
                console.error('No table found in DOM for direct insertion');
                return false;
            }

            console.log('Found table for direct insertion:', table);

            // Clear existing table content
            while (table.rows.length > 0) {
                table.deleteRow(0);
            }

            // Insert data into the table
            data.forEach((row, rowIndex) => {
                const tableRow = table.insertRow();

                // Add row header (row number) if it's a data row
                if (rowIndex > 0) {
                    const rowHeader = document.createElement('th');
                    rowHeader.className = 'row-header';
                    rowHeader.textContent = rowIndex.toString();
                    tableRow.appendChild(rowHeader);
                } else {
                    // For header row, add corner cell
                    const cornerCell = document.createElement('th');
                    cornerCell.className = 'corner-cell';
                    tableRow.appendChild(cornerCell);
                }

                // Add cells
                row.forEach((cellValue, colIndex) => {
                    if (rowIndex === 0) {
                        // Header row
                        const th = document.createElement('th');
                        th.className = 'column-header';
                        th.textContent = cellValue;
                        tableRow.appendChild(th);
                    } else {
                        // Data row
                        const cell = tableRow.insertCell();
                        const input = document.createElement('input');
                        input.type = 'text';
                        input.className = 'cell-input';
                        input.value = cellValue;
                        cell.appendChild(input);
                    }
                });
            });

            console.log('Successfully inserted data directly to table');
            return true;
        } catch (error) {
            console.error('Error in direct insertion to current sheet:', error);
            return false;
        }
    }

    /**
     * Save invoices data to be inserted when spreadsheet is ready
     * @param {Array} invoices - The invoices data to insert
     */
    saveInvoicesData(invoices) {
        if (!invoices || !Array.isArray(invoices) || invoices.length === 0) {
            console.warn('No valid invoices data provided to save');
            return;
        }

        console.log('Saving invoices data for later insertion', invoices.length, 'invoices');
        this.invoicesData = invoices;

        // Always save to Chrome storage for persistence
        this.saveToStorage(invoices);

        // Try to apply immediately if any spreadsheet interface is available
        if (window.spreadsheet || window.currentSheet || window.workbook || document.getElementById('excelTable')) {
            console.log('Some spreadsheet interface is available, applying data immediately');
            this.applyPendingData();
        } else {
            console.log('No spreadsheet interface available yet, data will be applied when ready');

            // Check if we need to set up the spreadsheet ready event listener
            if (!this.spreadsheetReadyListenerSet) {
                console.log('Setting up spreadsheet ready event listener');
                document.addEventListener('spreadsheetReady', () => {
                    console.log('Received spreadsheetReady event, applying pending data');
                    this.applyPendingData();
                });
                this.spreadsheetReadyListenerSet = true;
            }
        }
    }

    /**
     * Insert invoices data to the current sheet
     * @param {Array} invoices - The invoices to insert
     * @returns {boolean} - Whether the insertion was successful
     */
    insertInvoicesDataToCurrentSheet(invoices) {
        try {
            console.log('Inserting invoices data into current sheet', invoices.length, 'invoices');

            if (!window.currentSheet) {
                console.error('Current sheet not available');
                return false;
            }

            return this.insertInvoicesDataToSheet(invoices, window.currentSheet);
        } catch (error) {
            console.error('Error inserting invoices data to current sheet:', error);
            return false;
        }
    }

    /**
     * Insert invoices data to a specific sheet - SIMPLIFIED VERSION
     * @param {Array} invoices - The invoices to insert
     * @param {Object} sheet - The sheet to insert data into
     * @returns {boolean} - Whether the insertion was successful
     */
    insertInvoicesDataToSheet(invoices, sheet) {
        try {
            console.log('Inserting invoices data into specific sheet - SIMPLIFIED VERSION', invoices.length, 'invoices');

            if (!sheet) {
                console.error('Sheet not provided');
                return false;
            }

            // Validate invoices data
            if (!invoices || !Array.isArray(invoices) || invoices.length === 0) {
                console.error('Invalid invoices data:', invoices);
                return false;
            }

            // Extract headers from the first invoice
            const headers = this.extractHeaders(invoices[0]);
            console.log('Extracted headers:', headers.length, 'headers');

            // Prepare data for insertion
            const data = [];
            data.push(headers); // Add headers as first row

            // Add invoice data
            invoices.forEach((invoice, index) => {
                try {
                    const row = headers.map(header => {
                        // Handle nested properties like ShipToContact
                        if (header.includes('.')) {
                            const [parent, child] = header.split('.');
                            return invoice[parent] && invoice[parent][child] && invoice[parent][child].value !== undefined
                                ? invoice[parent][child].value
                                : '';
                        }

                        // Handle regular properties
                        return invoice[header] && invoice[header].value !== undefined
                            ? invoice[header].value
                            : '';
                    });

                    data.push(row);
                } catch (rowError) {
                    console.error(`Error processing invoice at index ${index}:`, rowError);
                }
            });

            console.log('Prepared data for insertion, rows:', data.length);

            // DIRECT INSERTION APPROACH - Try all possible methods

            // Method 1: Try using setData if available
            if (typeof sheet.setData === 'function') {
                try {
                    sheet.setData(data);
                    console.log('Successfully inserted data using setData method');
                    return true;
                } catch (setDataError) {
                    console.error('Error using setData method:', setDataError);
                    // Continue to next method
                }
            }

            // Method 2: Try using cell method if available
            if (typeof sheet.cell === 'function') {
                try {
                    console.log('Trying cell-by-cell insertion method');
                    // Insert data cell by cell
                    data.forEach((row, rowIndex) => {
                        row.forEach((value, colIndex) => {
                            sheet.cell(rowIndex + 1, colIndex + 1).value(value);
                        });
                    });
                    console.log('Successfully inserted data using cell-by-cell method');
                    return true;
                } catch (cellError) {
                    console.error('Error using cell-by-cell method:', cellError);
                    // Continue to next method
                }
            }

            // Method 3: Try using setValue if available
            if (typeof sheet.setValue === 'function') {
                try {
                    console.log('Trying setValue method');
                    data.forEach((row, rowIndex) => {
                        row.forEach((value, colIndex) => {
                            sheet.setValue(rowIndex + 1, colIndex + 1, value);
                        });
                    });
                    console.log('Successfully inserted data using setValue method');
                    return true;
                } catch (setValueError) {
                    console.error('Error using setValue method:', setValueError);
                    // Continue to next method
                }
            }

            // Method 4: Try using a direct property assignment if available
            try {
                console.log('Trying direct data property assignment');
                sheet.data = data;
                console.log('Successfully assigned data directly to sheet.data');
                return true;
            } catch (directAssignError) {
                console.error('Error assigning data directly:', directAssignError);
            }

            // If we got here, none of the methods worked
            console.error('All insertion methods failed');
            return false;
        } catch (error) {
            console.error('Error inserting invoices data to sheet:', error);
            return false;
        }
    }

    /**
     * Insert invoices data directly to the DOM table
     * @param {Array} invoices - The invoices to insert
     * @returns {boolean} - Whether the insertion was successful
     */
    insertInvoicesDataToDom(invoices) {
        try {
            console.log('Inserting invoices data directly to DOM table', invoices.length, 'invoices');

            const table = document.getElementById('excelTable');
            if (!table) {
                console.error('Excel table not found in DOM');
                return false;
            }

            // Validate invoices data
            if (!invoices || !Array.isArray(invoices) || invoices.length === 0) {
                console.error('Invalid invoices data:', invoices);
                return false;
            }

            // Extract headers from the first invoice
            const headers = this.extractHeaders(invoices[0]);
            console.log('Extracted headers:', headers);

            // Clear existing table content
            while (table.rows.length > 0) {
                table.deleteRow(0);
            }

            // Create header row
            const headerRow = table.insertRow();
            headerRow.className = 'header-row';

            // Add corner cell
            const cornerCell = document.createElement('th');
            cornerCell.className = 'corner-cell';
            headerRow.appendChild(cornerCell);

            // Add header cells
            headers.forEach((header, index) => {
                const th = document.createElement('th');
                th.className = 'column-header';
                th.textContent = header;
                th.dataset.col = index + 1;
                headerRow.appendChild(th);
            });

            // Add data rows
            invoices.forEach((invoice, rowIndex) => {
                try {
                    const row = table.insertRow();

                    // Add row header (row number)
                    const rowHeader = document.createElement('th');
                    rowHeader.className = 'row-header';
                    rowHeader.textContent = (rowIndex + 1).toString();
                    rowHeader.dataset.row = rowIndex + 1;
                    row.appendChild(rowHeader);

                    // Add data cells
                    headers.forEach(header => {
                        const cell = row.insertCell();
                        const input = document.createElement('input');
                        input.type = 'text';
                        input.className = 'cell-input';

                        // Set cell value
                        let value = '';
                        if (header.includes('.')) {
                            const [parent, child] = header.split('.');
                            value = invoice[parent] && invoice[parent][child] && invoice[parent][child].value !== undefined
                                ? invoice[parent][child].value
                                : '';
                        } else {
                            value = invoice[header] && invoice[header].value !== undefined
                                ? invoice[header].value
                                : '';
                        }

                        input.value = value;
                        cell.appendChild(input);
                    });
                } catch (rowError) {
                    console.error(`Error processing invoice row at index ${rowIndex}:`, rowError);
                }
            });

            console.log('Successfully inserted invoices data to DOM table');
            return true;
        } catch (error) {
            console.error('Error inserting invoices data to DOM:', error);
            return false;
        }
    }

    /**
     * Save invoices to Chrome storage
     * @param {Array} invoices - The invoices to save
     */
    saveToStorage(invoices) {
        try {
            if (!chrome || !chrome.storage || !chrome.storage.local) {
                console.warn('Chrome storage not available');
                return;
            }

            chrome.storage.local.set({ 'acumatica_invoices': invoices }, () => {
                if (chrome.runtime.lastError) {
                    console.error('Error saving invoices to storage:', chrome.runtime.lastError);
                } else {
                    console.log('Invoices saved to Chrome storage');
                }
            });
        } catch (error) {
            console.error('Error saving to storage:', error);
        }
    }

    /**
     * Load invoices from Chrome storage
     * @returns {Promise<Array>} - The loaded invoices
     */
    loadFromStorage() {
        return new Promise((resolve) => {
            try {
                if (!chrome || !chrome.storage || !chrome.storage.local) {
                    console.warn('Chrome storage not available');
                    resolve([]);
                    return;
                }

                chrome.storage.local.get(['acumatica_invoices'], (result) => {
                    if (chrome.runtime.lastError) {
                        console.error('Error loading invoices from storage:', chrome.runtime.lastError);
                        resolve([]);
                    } else if (result.acumatica_invoices) {
                        console.log('Loaded invoices from Chrome storage');
                        resolve(result.acumatica_invoices);
                    } else {
                        console.log('No invoices found in Chrome storage');
                        resolve([]);
                    }
                });
            } catch (error) {
                console.error('Error loading from storage:', error);
                resolve([]);
            }
        });
    }

    /**
     * Insert invoices data into the spreadsheet
     * @param {Array} invoices - The invoices to insert
     * @returns {boolean} - Whether the insertion was successful
     */
    insertInvoicesData(invoices) {
        try {
            console.log('Inserting invoices data into spreadsheet', invoices.length, 'invoices');

            if (!window.spreadsheet) {
                console.error('Spreadsheet not available for data insertion');

                // Try alternative methods
                if (window.currentSheet) {
                    console.log('Trying to insert using current sheet');
                    return this.insertInvoicesDataToCurrentSheet(invoices);
                } else if (window.workbook) {
                    console.log('Trying to insert using workbook');
                    try {
                        const sheet = window.workbook.activeSheet();
                        if (sheet) {
                            return this.insertInvoicesDataToSheet(invoices, sheet);
                        }
                    } catch (workbookError) {
                        console.error('Error getting active sheet from workbook:', workbookError);
                    }
                } else if (document.getElementById('excelTable')) {
                    console.log('Trying to insert directly to DOM');
                    return this.insertInvoicesDataToDom(invoices);
                }

                // Save the data for later and retry
                this.invoicesData = invoices;
                return false;
            }

            // Validate invoices data
            if (!invoices || !Array.isArray(invoices) || invoices.length === 0) {
                console.error('Invalid invoices data:', invoices);
                return false;
            }

            // Get active sheet or create a new one
            let sheet = window.spreadsheet.getActiveSheet();
            if (!sheet) {
                console.log('No active sheet, creating a new one');
                try {
                    sheet = window.spreadsheet.createSheet('Acumatica Invoices');
                } catch (sheetError) {
                    console.error('Error creating sheet:', sheetError);
                }

                if (!sheet) {
                    console.error('Failed to create sheet, trying alternative method');
                    // Try alternative method if available
                    if (typeof window.spreadsheet.addSheet === 'function') {
                        sheet = window.spreadsheet.addSheet('Acumatica Invoices');
                    }

                    if (!sheet) {
                        console.error('All attempts to create sheet failed');

                        // Try DOM insertion as a last resort
                        if (document.getElementById('excelTable')) {
                            console.log('Trying direct DOM insertion as last resort');
                            return this.insertInvoicesDataToDom(invoices);
                        }

                        return false;
                    }
                }
            }

            // Use the sheet-specific insertion method
            return this.insertInvoicesDataToSheet(invoices, sheet);
        } catch (error) {
            console.error('Error inserting invoices data:', error);

            // Try DOM insertion as a fallback
            if (document.getElementById('excelTable')) {
                console.log('Trying direct DOM insertion as fallback after error');
                try {
                    return this.insertInvoicesDataToDom(invoices);
                } catch (domError) {
                    console.error('Error in DOM fallback insertion:', domError);
                }
            }

            return false;
        }
    }

    /**
     * Extract headers from an invoice object
     * @param {Object} invoice - The invoice object
     * @returns {Array} - The extracted headers
     */
    extractHeaders(invoice) {
        const headers = [];

        // Process regular properties
        Object.keys(invoice).forEach(key => {
            // Skip _links and custom properties
            if (key !== '_links' && key !== 'custom' && key !== 'rowNumber' && key !== 'id') {
                // If it's a nested object like ShipToContact, process its properties
                if (key === 'ShipToContact' && invoice[key] && typeof invoice[key] === 'object') {
                    Object.keys(invoice[key]).forEach(nestedKey => {
                        // Skip metadata properties
                        if (nestedKey !== 'id' && nestedKey !== 'rowNumber' && nestedKey !== 'note' && nestedKey !== 'custom') {
                            headers.push(`ShipToContact.${nestedKey}`);
                        }
                    });
                } else {
                    headers.push(key);
                }
            }
        });

        return headers;
    }
}

// Create and export singleton instance
export const spreadsheetIntegration = new SpreadsheetIntegration();

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM content loaded, initializing spreadsheet integration');
    spreadsheetIntegration.init();

    // Create a custom event when the spreadsheet is ready
    const checkSpreadsheetReady = () => {
        console.log('Checking if spreadsheet is ready, window.spreadsheet =', !!window.spreadsheet);

        if (window.spreadsheet) {
            console.log('Spreadsheet is ready, dispatching spreadsheetReady event');
            document.dispatchEvent(new CustomEvent('spreadsheetReady'));
        } else {
            console.log('Spreadsheet not ready yet, will check again in 1 second');
            setTimeout(checkSpreadsheetReady, 1000);
        }
    };

    // Start checking if spreadsheet is ready
    console.log('Starting spreadsheet ready check');
    setTimeout(checkSpreadsheetReady, 1000);

    // Also set up a backup check in case the main one fails
    let checkCount = 0;
    const maxChecks = 30; // Check for 30 seconds max

    const backupCheck = () => {
        checkCount++;
        console.log(`Backup check ${checkCount}/${maxChecks} for spreadsheet ready`);

        // Check if we have pending data
        if (spreadsheetIntegration.invoicesData) {
            console.log('Backup check: We have pending data, trying direct insertion first');

            // Try direct insertion first - this should work in most cases
            if (spreadsheetIntegration.insertDirectlyToCurrentSheet(spreadsheetIntegration.invoicesData)) {
                console.log('Backup check: Direct insertion successful');
                spreadsheetIntegration.invoicesData = null;
                return;
            }

            console.log('Backup check: Direct insertion failed, trying other methods');

            // Try all available methods
            if (window.spreadsheet) {
                console.log('Backup check: window.spreadsheet is available');
                spreadsheetIntegration.applyPendingData();
            } else if (window.currentSheet) {
                console.log('Backup check: window.currentSheet is available');
                spreadsheetIntegration.insertInvoicesDataToCurrentSheet(spreadsheetIntegration.invoicesData);
                spreadsheetIntegration.invoicesData = null;
            } else if (window.workbook) {
                console.log('Backup check: window.workbook is available');
                try {
                    const sheet = window.workbook.activeSheet();
                    if (sheet) {
                        spreadsheetIntegration.insertInvoicesDataToSheet(spreadsheetIntegration.invoicesData, sheet);
                        spreadsheetIntegration.invoicesData = null;
                    }
                } catch (error) {
                    console.error('Backup check: Error getting active sheet from workbook:', error);
                }
            } else if (document.getElementById('excelTable')) {
                console.log('Backup check: excelTable is available in DOM');
                spreadsheetIntegration.insertInvoicesDataToDom(spreadsheetIntegration.invoicesData);
                spreadsheetIntegration.invoicesData = null;
            } else if (checkCount >= maxChecks / 2) {
                // After half the checks, try a more aggressive approach
                console.log('Backup check: Trying more aggressive approach - looking for any table in the DOM');

                // Look for any table in the DOM that might be the spreadsheet
                const tables = document.querySelectorAll('table');
                if (tables.length > 0) {
                    console.log(`Backup check: Found ${tables.length} tables in the DOM, trying the first one`);

                    // Try direct insertion to the first table
                    const table = tables[0];

                    // Clear existing table content
                    while (table.rows.length > 0) {
                        table.deleteRow(0);
                    }

                    // Extract headers and prepare data
                    const headers = spreadsheetIntegration.extractHeaders(spreadsheetIntegration.invoicesData[0]);
                    const data = [headers];

                    // Add invoice data
                    spreadsheetIntegration.invoicesData.forEach(invoice => {
                        const row = headers.map(header => {
                            if (header.includes('.')) {
                                const [parent, child] = header.split('.');
                                return invoice[parent] && invoice[parent][child] && invoice[parent][child].value !== undefined
                                    ? invoice[parent][child].value
                                    : '';
                            }
                            return invoice[header] && invoice[header].value !== undefined
                                ? invoice[header].value
                                : '';
                        });
                        data.push(row);
                    });

                    // Insert data into the table
                    data.forEach((row, rowIndex) => {
                        const tableRow = table.insertRow();
                        row.forEach((cellValue, colIndex) => {
                            const cell = tableRow.insertCell();
                            cell.textContent = cellValue;
                        });
                    });

                    console.log('Backup check: Inserted data directly into existing table');
                    spreadsheetIntegration.invoicesData = null;
                }
            }
        }

        // Continue checking if we still have pending data and haven't reached max checks
        if (spreadsheetIntegration.invoicesData && checkCount < maxChecks) {
            setTimeout(backupCheck, 1000);
        } else if (!spreadsheetIntegration.invoicesData) {
            console.log('Backup check: Data has been successfully inserted, stopping checks');
        } else {
            console.log('Backup check: Maximum checks reached, giving up');

            // Final attempt - create our own table if none exists
            if (spreadsheetIntegration.invoicesData) {
                console.log('Backup check: Final attempt - creating our own table');

                // Create a table element
                const container = document.getElementById('spreadsheetContainer') || document.body;
                const table = document.createElement('table');
                table.className = 'excel-table';
                container.appendChild(table);

                // Extract headers and prepare data
                const headers = spreadsheetIntegration.extractHeaders(spreadsheetIntegration.invoicesData[0]);
                const data = [headers];

                // Add invoice data
                spreadsheetIntegration.invoicesData.forEach(invoice => {
                    const row = headers.map(header => {
                        if (header.includes('.')) {
                            const [parent, child] = header.split('.');
                            return invoice[parent] && invoice[parent][child] && invoice[parent][child].value !== undefined
                                ? invoice[parent][child].value
                                : '';
                        }
                        return invoice[header] && invoice[header].value !== undefined
                            ? invoice[header].value
                            : '';
                    });
                    data.push(row);
                });

                // Insert data into the table
                data.forEach((row, rowIndex) => {
                    const tableRow = table.insertRow();
                    row.forEach((cellValue, colIndex) => {
                        const cell = tableRow.insertCell();
                        cell.textContent = cellValue;
                    });
                });

                console.log('Backup check: Created new table and inserted data');
                spreadsheetIntegration.invoicesData = null;
            }
        }
    };

    // Start backup check after 5 seconds
    setTimeout(backupCheck, 5000);
});
