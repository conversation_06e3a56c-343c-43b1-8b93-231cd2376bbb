// js/styler.js
// Store format painter source styles
let formatPainterActive = false;
let formatPainterStyles = null;
let formatPainterSourceCell = null;

export function applyCellStylesToElement(xlsxCell, htmlElement) {
    if (!xlsxCell || !htmlElement) return;
    let value = xlsxCell.value();
    // Handle rich text from XlsxPopulate
    if (typeof value === 'object' && value && value.richText) {
        // For simplicity, concatenate rich text. True rich text rendering in an input is complex.
        htmlElement.value = value.richText.map(rt => rt.text).join('');
    } else {
        htmlElement.value = (value !== undefined && value !== null) ? value : '';
    }

    htmlElement.classList.toggle('bold', !!xlsxCell.style("bold"));
    htmlElement.classList.toggle('italic', !!xlsxCell.style("italic"));
    htmlElement.classList.toggle('underline', !!xlsxCell.style("underline"));

    const fontColor = xlsxCell.style("fontColor");
    htmlElement.style.color = fontColor ? `#${fontColor.rgb || fontColor}` : '';

    const fill = xlsxCell.style("fill");
    if (fill) {
        const colorVal = (typeof fill === 'string') ? fill : (fill.color ? (fill.color.rgb || fill.color.hex || fill.color) : null);
        htmlElement.style.backgroundColor = colorVal ? `#${colorVal}` : '';
    } else {
        htmlElement.style.backgroundColor = '';
    }

    const fontSize = xlsxCell.style("fontSize");
    htmlElement.style.fontSize = fontSize ? `${fontSize}pt` : '';

    const hAlign = xlsxCell.style("horizontalAlignment");
    htmlElement.style.textAlign = hAlign || 'left'; // XlsxPopulate default might be general

    // Number format doesn't directly translate to input style, but we might display it somewhere
    // const numFmt = xlsxCell.style("numberFormat");
}

export function applyToolbarStyleToSelection(sheet, selectionRange, activeCellElement, styleName, styleValue) {
    if (!sheet || (!selectionRange && !activeCellElement)) return;

    // Import history manager if available
    let historyManager = null;
    try {
        if (window.historyManager) {
            historyManager = window.historyManager;
        }
    } catch (error) {
        console.error('Error accessing history manager:', error);
    }

    // Record state before making changes if history manager is available
    if (historyManager) {
        historyManager.recordState(sheet, {
            type: 'style',
            description: `Apply ${styleName} style`
        });
    }

    const RANGES_TO_STYLE = [];
    if (selectionRange && selectionRange.start && selectionRange.end) { // If a range is selected
        RANGES_TO_STYLE.push(sheet.range(selectionRange.start.r, selectionRange.start.c, selectionRange.end.r, selectionRange.end.c));
    } else if (activeCellElement) { // Fallback to active cell
        RANGES_TO_STYLE.push(sheet.cell(activeCellElement.dataset.address));
    }

    RANGES_TO_STYLE.forEach(rangeOrCell => {
        if (styleName === 'bold' || styleName === 'italic' || styleName === 'underline') {
            const currentVal = rangeOrCell.style(styleName); // Get style from first cell in range
            rangeOrCell.style(styleName, !currentVal);
        } else if (styleName === 'fontColor' || styleName === 'fill') {
            rangeOrCell.style(styleName, styleValue.substring(1)); // Remove #
        } else if (styleName === 'fontSize') {
            rangeOrCell.style(styleName, styleValue);
        } else if (styleName === 'numberFormat' || styleName === 'horizontalAlignment') {
            rangeOrCell.style(styleName, styleValue);
        }
    });

    // After applying to workbook, re-apply to HTML elements in the selection
    const affectedElements = [];
     if (selectionRange && selectionRange.start && selectionRange.end) {
        for (let r = selectionRange.start.r; r <= selectionRange.end.r; r++) {
            for (let c = selectionRange.start.c; c <= selectionRange.end.c; c++) {
                const el = document.querySelector(`.cell-input[data-row="${r}"][data-col="${c}"]`);
                if (el) affectedElements.push(el);
            }
        }
    } else if (activeCellElement) {
        affectedElements.push(activeCellElement);
    }

    affectedElements.forEach(el => {
        const cellAddress = el.dataset.address;
        applyCellStylesToElement(sheet.cell(cellAddress), el);
    });
}

export function updateToolbarForSelection(toolbarElements, sheet, selectionRange, activeCellElement) {
    // Determine the reference cell (top-left of selection or active cell)
    let refCell;
    if (selectionRange && selectionRange.start) {
        refCell = sheet.cell(selectionRange.start.r, selectionRange.start.c);
    } else if (activeCellElement) {
        refCell = sheet.cell(activeCellElement.dataset.address);
    } else {
        // Reset toolbar to defaults if no selection
        if (toolbarElements.boldBtn) toolbarElements.boldBtn.classList.remove('active');
        if (toolbarElements.italicBtn) toolbarElements.italicBtn.classList.remove('active');
        if (toolbarElements.underlineBtn) toolbarElements.underlineBtn.classList.remove('active');
        if (toolbarElements.fontColorPicker) toolbarElements.fontColorPicker.value = '#000000';
        if (toolbarElements.bgColorPicker) toolbarElements.bgColorPicker.value = '#ffffff';
        if (toolbarElements.numberFormatSelect) toolbarElements.numberFormatSelect.value = 'General';
        if (toolbarElements.horizontalAlignSelect) toolbarElements.horizontalAlignSelect.value = 'left';
        if (toolbarElements.fontFamilySelect) toolbarElements.fontFamilySelect.value = 'Arial';
        return;
    }

    if (toolbarElements.boldBtn) toolbarElements.boldBtn.classList.toggle('active', !!refCell.style("bold"));
    if (toolbarElements.italicBtn) toolbarElements.italicBtn.classList.toggle('active', !!refCell.style("italic"));
    if (toolbarElements.underlineBtn) toolbarElements.underlineBtn.classList.toggle('active', !!refCell.style("underline"));

    const fontColor = refCell.style("fontColor");
    if (toolbarElements.fontColorPicker) toolbarElements.fontColorPicker.value = fontColor ? `#${fontColor.rgb || fontColor}` : '#000000';

    const fill = refCell.style("fill");
    if (toolbarElements.bgColorPicker) {
        if (fill) {
            const colorVal = (typeof fill === 'string') ? fill : (fill.color ? (fill.color.rgb || fill.color.hex || fill.color) : null);
            toolbarElements.bgColorPicker.value = colorVal ? `#${colorVal}` : '#ffffff';
        } else {
            toolbarElements.bgColorPicker.value = '#ffffff';
        }
    }

    const fontFamily = refCell.style("fontFamily");
    if (toolbarElements.fontFamilySelect && fontFamily) {
        toolbarElements.fontFamilySelect.value = fontFamily;
    }

    const numberFormat = refCell.style("numberFormat");
    if (toolbarElements.numberFormatSelect && numberFormat) {
        toolbarElements.numberFormatSelect.value = numberFormat;
    }

    const horizontalAlignment = refCell.style("horizontalAlignment") || 'left';

    // Update alignment buttons based on current alignment
    if (toolbarElements.alignLeftBtn) {
        toolbarElements.alignLeftBtn.classList.toggle('active', horizontalAlignment === 'left');
    }
    if (toolbarElements.alignCenterBtn) {
        toolbarElements.alignCenterBtn.classList.toggle('active', horizontalAlignment === 'center');
    }
    if (toolbarElements.alignRightBtn) {
        toolbarElements.alignRightBtn.classList.toggle('active', horizontalAlignment === 'right');
    }

    // Add null checks to prevent errors when toolbar elements don't exist
    if (toolbarElements.numberFormatSelect) {
        toolbarElements.numberFormatSelect.value = refCell.style("numberFormat") || "General";
    }

    // Update format painter button state
    if (toolbarElements.formatPainterBtn) {
        toolbarElements.formatPainterBtn.classList.toggle('active', formatPainterActive);
    }

    // Font size would require getting it and maybe displaying it or just having +/- buttons
}

/**
 * Toggle format painter mode
 * @param {Object} sheet - The current sheet
 * @param {Object} selectionRange - The current selection range
 * @param {HTMLElement} activeCellElement - The active cell element
 * @returns {boolean} - Whether format painter is active
 */
export function toggleFormatPainter(sheet, selectionRange, activeCellElement) {
    if (!sheet) return false;

    // If format painter is already active, deactivate it
    if (formatPainterActive) {
        formatPainterActive = false;
        formatPainterStyles = null;
        formatPainterSourceCell = null;

        // Update cursor
        document.body.classList.remove('format-painter-cursor');

        // Update button state
        const formatPainterBtn = document.getElementById('formatPainterBtn');
        if (formatPainterBtn) {
            formatPainterBtn.classList.remove('active');
        }

        return false;
    }

    // Get the source cell for format copying
    let sourceCell;
    if (selectionRange && selectionRange.start) {
        sourceCell = sheet.cell(selectionRange.start.r, selectionRange.start.c);
        formatPainterSourceCell = { r: selectionRange.start.r, c: selectionRange.start.c };
    } else if (activeCellElement) {
        sourceCell = sheet.cell(activeCellElement.dataset.address);
        formatPainterSourceCell = {
            r: parseInt(activeCellElement.dataset.row),
            c: parseInt(activeCellElement.dataset.col)
        };
    } else {
        return false; // No source cell
    }

    // Capture styles from the source cell
    formatPainterStyles = {};
    const styleProps = ['bold', 'italic', 'underline', 'fontColor', 'fill',
                       'horizontalAlignment', 'fontSize', 'fontFamily', 'numberFormat'];

    styleProps.forEach(prop => {
        const style = sourceCell.style(prop);
        if (style !== undefined) {
            formatPainterStyles[prop] = style;
        }
    });

    // Activate format painter
    formatPainterActive = true;

    // Update cursor
    document.body.classList.add('format-painter-cursor');

    // Update button state
    const formatPainterBtn = document.getElementById('formatPainterBtn');
    if (formatPainterBtn) {
        formatPainterBtn.classList.add('active');
    }

    return true;
}

/**
 * Apply format painter styles to a cell or range
 * @param {Object} sheet - The current sheet
 * @param {Object} selectionRange - The target selection range
 * @param {HTMLElement} activeCellElement - The target active cell element
 * @returns {boolean} - Whether styles were applied
 */
export function applyFormatPainter(sheet, selectionRange, activeCellElement) {
    if (!sheet || !formatPainterActive || !formatPainterStyles) return false;

    // Import history manager if available
    let historyManager = null;
    try {
        if (window.historyManager) {
            historyManager = window.historyManager;
        }
    } catch (error) {
        console.error('Error accessing history manager:', error);
    }

    // Record state before making changes if history manager is available
    if (historyManager) {
        historyManager.recordState(sheet, {
            type: 'formatPainter',
            description: 'Apply format painter'
        });
    }

    const RANGES_TO_STYLE = [];
    if (selectionRange && selectionRange.start && selectionRange.end) {
        RANGES_TO_STYLE.push(sheet.range(selectionRange.start.r, selectionRange.start.c, selectionRange.end.r, selectionRange.end.c));
    } else if (activeCellElement) {
        RANGES_TO_STYLE.push(sheet.cell(activeCellElement.dataset.address));
    }

    RANGES_TO_STYLE.forEach(rangeOrCell => {
        // Apply all captured styles
        Object.entries(formatPainterStyles).forEach(([prop, value]) => {
            rangeOrCell.style(prop, value);
        });
    });

    // After applying to workbook, re-apply to HTML elements in the selection
    const affectedElements = [];
    if (selectionRange && selectionRange.start && selectionRange.end) {
        for (let r = selectionRange.start.r; r <= selectionRange.end.r; r++) {
            for (let c = selectionRange.start.c; c <= selectionRange.end.c; c++) {
                const el = document.querySelector(`.cell-input[data-row="${r}"][data-col="${c}"]`);
                if (el) affectedElements.push(el);
            }
        }
    } else if (activeCellElement) {
        affectedElements.push(activeCellElement);
    }

    affectedElements.forEach(el => {
        const cellAddress = el.dataset.address;
        applyCellStylesToElement(sheet.cell(cellAddress), el);
    });

    // Deactivate format painter after one use
    formatPainterActive = false;

    // Update cursor
    document.body.classList.remove('format-painter-cursor');

    // Update button state
    const formatPainterBtn = document.getElementById('formatPainterBtn');
    if (formatPainterBtn) {
        formatPainterBtn.classList.remove('active');
    }

    return true;
}

/**
 * Check if format painter is active
 * @returns {boolean} - Whether format painter is active
 */
export function isFormatPainterActive() {
    return formatPainterActive;
}