// improved_cell_resizer.js - Enhanced cell resizing functionality for Excel-like tables

/**
 * This module enhances the cell resizing functionality by:
 * 1. Adding the ability to resize rows from both top and bottom
 * 2. Improving the resize handle visibility and usability
 * 3. Ensuring smooth resizing operations
 */

document.addEventListener('DOMContentLoaded', function() {
    // Wait a short time to ensure the table is fully rendered
    setTimeout(initImprovedResizing, 500);

    // Also listen for table-rendered event which might be dispatched when the table is redrawn
    document.addEventListener('table-rendered', function() {
        console.log('Table rendered event detected, initializing resizers...');
        setTimeout(initImprovedResizing, 100);
    });

    // Fallback: periodically check for table changes
    setInterval(function() {
        const table = document.getElementById('excelTable');
        if (table) {
            // Check if row headers exist but don't have resizers
            const rowHeaders = table.querySelectorAll('th.row-header');
            let needsResizers = false;

            for (let i = 0; i < Math.min(rowHeaders.length, 3); i++) { // Check first few headers
                if (!rowHeaders[i].querySelector('.row-resizer')) {
                    needsResizers = true;
                    break;
                }
            }

            if (needsResizers) {
                console.log('Detected row headers without resizers, reinitializing...');
                initImprovedResizing();
            }
        }
    }, 2000); // Check every 2 seconds
});

/**
 * Initialize the improved resizing functionality
 */
function initImprovedResizing() {
    console.log('Initializing improved cell resizing...');

    // Get the Excel table
    const table = document.getElementById('excelTable');
    if (!table) {
        console.warn('Excel table not found, will try again later');
        // Try again later if table not found
        setTimeout(initImprovedResizing, 1000);
        return;
    }

    // Add improved column resizing
    initImprovedColumnResizing(table);

    // Add improved row resizing
    initImprovedRowResizing(table);

    // Add window resize handler to maintain resize handles
    window.addEventListener('resize', function() {
        // Refresh resize handles on window resize
        refreshResizeHandles(table);
    });

    console.log('Improved cell resizing initialized');
}

/**
 * Initialize improved column resizing
 * @param {HTMLElement} table - The Excel table element
 */
function initImprovedColumnResizing(table) {
    // Get all header cells
    const headerCells = table.querySelectorAll('th');

    headerCells.forEach(th => {
        // Skip if it's not a column header
        if (!th.classList.contains('column-header') && !th.dataset.col) return;

        // Create and append resize handle if it doesn't exist
        let resizer = th.querySelector('.column-resizer');
        if (!resizer) {
            resizer = document.createElement('div');
            resizer.className = 'column-resizer';
            th.appendChild(resizer);
        }

        // Set up resize event handlers
        setupColumnResizeHandlers(th, resizer);
    });
}

/**
 * Initialize improved row resizing
 * @param {HTMLElement} table - The Excel table element
 */
function initImprovedRowResizing(table) {
    console.log('Initializing improved row resizing...');

    // Get all rows in the table
    const rows = table.querySelectorAll('tr');

    rows.forEach((tr, index) => {
        // Skip the header row (first row)
        if (index === 0) return;

        // Get the row header cell (first cell in row which could be a th for row headers)
        let rowHeader = tr.querySelector('th.row-header');

        // If row header not found with th.row-header, try td:first-child as fallback
        if (!rowHeader) {
            rowHeader = tr.querySelector('td:first-child');
        }

        if (!rowHeader) {
            console.warn('Row header not found for row:', index);
            return;
        }

        // Store the row index for debugging
        const rowIndex = rowHeader.dataset.row || index;
        console.log(`Adding resizers to row ${rowIndex} header`);

        // Create and append bottom resize handle if it doesn't exist
        let bottomResizer = rowHeader.querySelector('.row-resizer');
        if (!bottomResizer) {
            bottomResizer = document.createElement('div');
            bottomResizer.className = 'row-resizer';
            bottomResizer.title = 'Drag to resize row';
            rowHeader.appendChild(bottomResizer);
        }

        // Create and append top resize handle if it doesn't exist
        let topResizer = rowHeader.querySelector('.row-resizer-top');
        if (!topResizer) {
            topResizer = document.createElement('div');
            topResizer.className = 'row-resizer-top';
            topResizer.title = 'Drag to resize row';
            rowHeader.appendChild(topResizer);
        }

        // Make sure the resizers are visible by adding a hover effect
        rowHeader.addEventListener('mouseenter', () => {
            bottomResizer.style.backgroundColor = '#e0e0e0';
        });

        rowHeader.addEventListener('mouseleave', () => {
            bottomResizer.style.backgroundColor = 'transparent';
        });

        // Set up resize event handlers
        setupRowResizeHandlers(tr, rowHeader, bottomResizer, topResizer);
    });
}

/**
 * Set up column resize event handlers
 * @param {HTMLElement} th - The header cell
 * @param {HTMLElement} resizer - The resize handle
 */
function setupColumnResizeHandlers(th, resizer) {
    let startX, startWidth, columnIndex;

    // Mouse down event on resizer element
    resizer.addEventListener('mousedown', function(e) {
        startX = e.pageX;
        columnIndex = parseInt(th.dataset.col || 0);
        startWidth = th.offsetWidth;

        // Add event listeners for resize operation
        document.addEventListener('mousemove', resize);
        document.addEventListener('mouseup', stopResize);

        // Add resize class to indicate active resize operation
        th.classList.add('resizing');
        document.body.classList.add('no-select'); // Prevent text selection during resize

        e.preventDefault();
    });

    // Resize operation
    function resize(e) {
        const width = startWidth + (e.pageX - startX);
        if (width > 30) { // Minimum width
            th.style.width = width + 'px';

            // Also resize all corresponding data cells in this column
            const cells = document.querySelectorAll(`td[data-col="${columnIndex}"]`);
            cells.forEach(cell => {
                cell.style.width = width + 'px';
            });
        }
    }

    // End resize operation
    function stopResize() {
        document.removeEventListener('mousemove', resize);
        document.removeEventListener('mouseup', stopResize);
        th.classList.remove('resizing');
        document.body.classList.remove('no-select');

        // Update history if using history manager
        try {
            if (window.historyManager && typeof window.historyManager.addState === 'function' && window.workbook) {
                window.historyManager.addState(window.workbook, 'Resized column');
            } else {
                // Fall back to simple modification flag if history manager isn't available
                if (!window.workbookModified) window.workbookModified = true;
            }
        } catch (historyError) {
            console.warn('Could not add column resize to history:', historyError);
        }
    }
}

/**
 * Set up row resize event handlers
 * @param {HTMLElement} tr - The table row
 * @param {HTMLElement} rowHeader - The row header cell
 * @param {HTMLElement} bottomResizer - The bottom resize handle
 * @param {HTMLElement} topResizer - The top resize handle
 */
function setupRowResizeHandlers(tr, rowHeader, bottomResizer, topResizer) {
    let startY, startHeight, rowIndex;

    // Get the row index from either the tr or rowHeader
    const getRowIndex = () => {
        // Try to get row index from different possible sources
        if (tr.dataset && tr.dataset.row) {
            return parseInt(tr.dataset.row);
        } else if (rowHeader.dataset && rowHeader.dataset.row) {
            return parseInt(rowHeader.dataset.row);
        } else {
            // Fallback: find the row index by counting rows
            const table = tr.closest('table');
            if (table) {
                const rows = Array.from(table.querySelectorAll('tr'));
                return rows.indexOf(tr);
            }
            return 0;
        }
    };

    // Bottom resizer (traditional resize from bottom)
    bottomResizer.addEventListener('mousedown', function(e) {
        e.stopPropagation(); // Prevent event bubbling
        startY = e.pageY;
        rowIndex = getRowIndex();
        startHeight = tr.offsetHeight;

        console.log(`Starting resize from bottom for row ${rowIndex}, current height: ${startHeight}px`);

        // Add event listeners for resize operation
        document.addEventListener('mousemove', resizeFromBottom);
        document.addEventListener('mouseup', stopResize);

        // Add resize class to indicate active resize operation
        tr.classList.add('resizing');
        document.body.classList.add('no-select'); // Prevent text selection during resize

        // Add visual feedback
        bottomResizer.style.backgroundColor = '#1a73e8';

        e.preventDefault();
    });

    // Top resizer (resize from top)
    topResizer.addEventListener('mousedown', function(e) {
        e.stopPropagation(); // Prevent event bubbling
        startY = e.pageY;
        rowIndex = getRowIndex();
        startHeight = tr.offsetHeight;

        console.log(`Starting resize from top for row ${rowIndex}, current height: ${startHeight}px`);

        // Add event listeners for resize operation
        document.addEventListener('mousemove', resizeFromTop);
        document.addEventListener('mouseup', stopResize);

        // Add resize class to indicate active resize operation
        tr.classList.add('resizing');
        document.body.classList.add('no-select'); // Prevent text selection during resize

        // Add visual feedback
        topResizer.style.backgroundColor = '#1a73e8';

        e.preventDefault();
    });

    // Resize from bottom operation
    function resizeFromBottom(e) {
        const height = startHeight + (e.pageY - startY);
        if (height > 20) { // Minimum height
            tr.style.height = height + 'px';

            // Update all cells in this row (both th and td elements)
            const cells = tr.querySelectorAll('td, th');
            cells.forEach(cell => {
                cell.style.height = height + 'px';
            });

            // If using XlsxPopulate, update row height in the sheet
            updateRowHeightInSheet(rowIndex, height);

            // Update visual feedback
            bottomResizer.style.backgroundColor = '#1a73e8';
        }
    }

    // Resize from top operation
    function resizeFromTop(e) {
        const diff = e.pageY - startY;
        const height = startHeight - diff;

        if (height > 20) { // Minimum height
            tr.style.height = height + 'px';

            // Update all cells in this row (both th and td elements)
            const cells = tr.querySelectorAll('td, th');
            cells.forEach(cell => {
                cell.style.height = height + 'px';
            });

            // If using XlsxPopulate, update row height in the sheet
            updateRowHeightInSheet(rowIndex, height);

            // Update visual feedback
            topResizer.style.backgroundColor = '#1a73e8';
        }
    }

    // End resize operation
    function stopResize() {
        document.removeEventListener('mousemove', resizeFromBottom);
        document.removeEventListener('mousemove', resizeFromTop);
        document.removeEventListener('mouseup', stopResize);
        tr.classList.remove('resizing');
        document.body.classList.remove('no-select');

        // Reset visual feedback
        bottomResizer.style.backgroundColor = 'transparent';
        topResizer.style.backgroundColor = 'transparent';

        // Update history if using history manager
        try {
            if (window.historyManager && typeof window.historyManager.addState === 'function' && window.workbook) {
                window.historyManager.addState(window.workbook, 'Resized row');
                console.log(`Row ${rowIndex} resized and saved to history`);
            } else {
                // Fall back to simple modification flag if history manager isn't available
                if (!window.workbookModified) window.workbookModified = true;
                console.log(`Row ${rowIndex} resized, workbook marked as modified`);
            }
        } catch (historyError) {
            console.warn('Could not add row resize to history:', historyError);
        }
    }
}

/**
 * Update row height in the sheet model
 * @param {number} rowIndex - The row index
 * @param {number} heightPx - The height in pixels
 */
function updateRowHeightInSheet(rowIndex, heightPx) {
    if (window.currentSheet && typeof window.currentSheet.row === 'function') {
        try {
            // Convert pixels to excel row height units (approx.)
            // Excel row height is in points
            const excelHeight = heightPx * 0.75; // Approximate conversion (pixels to points)
            window.currentSheet.row(rowIndex).height(excelHeight);
        } catch (error) {
            console.warn('Could not set row height in sheet:', error);
        }
    }
}

/**
 * Refresh all resize handles in the table
 * @param {HTMLElement} table - The Excel table element
 */
function refreshResizeHandles(table) {
    // Re-initialize column resizing
    initImprovedColumnResizing(table);

    // Re-initialize row resizing
    initImprovedRowResizing(table);
}

// Make functions available globally
window.initImprovedResizing = initImprovedResizing;
window.refreshResizeHandles = refreshResizeHandles;
