/**
 * Improved Chart Dialog Styles
 * Centered, wider modal with better UI elements
 */

/* Chart dialog content styles only - modal container styling is handled by standardized-modal.css */
.excel-modal-content {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    width: 100%;
    max-width: 100%;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    animation: modal-appear 0.3s ease-out forwards;
}

@keyframes modal-appear {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.excel-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid #e0e0e0;
    background-color: #f8f9fa;
}

.excel-modal-header h2 {
    margin: 0;
    font-size: 20px;
    color: #333;
}

.excel-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    margin: 0;
    line-height: 1;
}

.excel-modal-close:hover {
    color: #333;
}

.excel-modal-body {
    padding: 24px;
    overflow-y: auto;
    flex: 1;
}

.excel-modal-footer {
    display: flex;
    justify-content: flex-end;
    padding: 16px 24px;
    border-top: 1px solid #e0e0e0;
    background-color: #f8f9fa;
}

/* Chart Dialog Specific Styles */
.chart-dialog .excel-modal-content {
    height: 80vh;
}

.chart-sidebar {
    width: 300px;
    padding: 20px;
    border-right: 1px solid #e0e0e0;
    overflow-y: auto;
    background-color: #f8f9fa;
}

.chart-preview {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
    background-color: #fff;
}

.chart-type-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin-bottom: 20px;
}

.chart-type-item {
    padding: 12px 8px;
    border: 2px solid #ddd;
    border-radius: 6px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.chart-type-item:hover {
    background-color: #f0f0f0;
    border-color: #bbb;
}

.chart-type-item.selected {
    background-color: #e3f2fd;
    border-color: #1a73e8;
}

.chart-type-item .material-icons {
    font-size: 24px;
    margin-bottom: 4px;
    color: #555;
}

.chart-type-item.selected .material-icons {
    color: #1a73e8;
}

.chart-type-item div {
    font-size: 13px;
    color: #333;
}

.chart-color-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin-bottom: 20px;
}

.chart-color-item {
    width: 36px;
    height: 36px;
    border-radius: 6px;
    cursor: pointer;
    transition: transform 0.2s ease;
    border: 2px solid transparent;
}

.chart-color-item:hover {
    transform: scale(1.1);
}

.chart-color-item.selected {
    border-color: #333;
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-control:focus {
    border-color: #1a73e8;
    outline: none;
    box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

.chart-options .option-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.chart-options .option-item:last-child {
    margin-bottom: 0;
}

.chart-options input[type="checkbox"] {
    margin: 0;
}

.chart-options label {
    margin: 0 0 0 8px;
    font-weight: normal;
}

.data-source-info {
    padding: 12px;
    background-color: #f5f5f5;
    border-radius: 6px;
    margin-bottom: 15px;
    font-size: 13px;
}

.data-source-info p {
    margin: 0 0 6px 0;
}

.data-source-info p:last-child {
    margin-bottom: 0;
}

.btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: #1a73e8;
    color: white;
    border: none;
}

.btn-primary:hover {
    background-color: #1669d9;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.btn-secondary {
    background-color: #f0f0f0;
    color: #333;
    border: 1px solid #ddd;
}

.btn-secondary:hover {
    background-color: #e0e0e0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Range Selection Dialog */
.range-selection-modal .excel-modal-content {
    width: 500px;
}

.range-input-container {
    display: flex;
    gap: 8px;
}

.range-input-container input {
    flex: 1;
}

/* Chart Preview */
#chartPreview {
    flex: 1;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f9f9f9;
    overflow: hidden;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .excel-modal-content {
        width: 95%;
        max-height: 95vh;
    }

    .chart-dialog .excel-modal-body {
        flex-direction: column;
    }

    .chart-sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid #e0e0e0;
        max-height: 50vh;
    }

    .chart-type-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
