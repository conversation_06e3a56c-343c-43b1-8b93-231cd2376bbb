/* CSS for Color Palette */
.color-palette-container {
    position: fixed; /* Changed from absolute to fixed */
    background: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    padding: 8px;
    z-index: 9999; /* Increased z-index to ensure it's on top */
    display: none;
    width: 220px;
    /* Position will be set dynamically in JS */
}

.color-palette-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    color: #333;
    text-align: center;
}

.color-palette-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 4px;
}

.color-swatch {
    width: 30px;
    height: 30px;
    border-radius: 4px;
    cursor: pointer;
    border: 1px solid #ddd;
    transition: transform 0.1s ease;
}

.color-swatch:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.color-palette-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
}

.color-palette-footer button {
    padding: 4px 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background: #f5f5f5;
    cursor: pointer;
    font-size: 12px;
}

.color-palette-footer button:hover {
    background: #e0e0e0;
}

.color-picker-wrapper {
    position: relative;
    display: inline-block;
}

/* Style for color picker labels */
.color-picker-label {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s;
    margin: 0 4px;
    position: relative;
}

.color-picker-label:hover {
    background-color: #f0f0f0;
}

.color-picker-label .material-icons {
    font-size: 20px;
}

/* Style for the color indicator */
.color-indicator {
    width: 100%;
    height: 3px;
    position: absolute;
    bottom: 0;
    left: 0;
    border-radius: 1px;
}

/* Hide the actual color input but keep it accessible */
input[type="color"] {
    width: 0;
    height: 0;
    padding: 0;
    border: none;
    opacity: 0;
    position: absolute;
    pointer-events: none;
}
