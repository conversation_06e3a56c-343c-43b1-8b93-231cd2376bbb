/* Welcome Modal Styles */
.welcome-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: none;
}

.welcome-modal {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    width: 500px;
    max-width: 90%;
    overflow: hidden;
    animation: modalFadeIn 0.3s ease-out;
    display: flex;
    flex-direction: column;
}

.welcome-header {
    background: linear-gradient(135deg, #1a73e8, #6c5ce7);
    color: white;
    padding: 20px;
    text-align: center;
    position: relative;
}

.welcome-close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: transparent;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.welcome-close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.welcome-close-btn:focus {
    outline: none;
}

.welcome-header h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 500;
}

.welcome-header p {
    margin: 10px 0 0;
    font-size: 14px;
    opacity: 0.9;
}

.welcome-logo {
    width: 80px;
    height: 80px;
    margin: 0 auto 15px;
    background-color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.welcome-logo img {
    width: 50px;
    height: 50px;
}

.welcome-content {
    padding: 30px;
    text-align: center;
}

.welcome-content p {
    margin: 0 0 25px;
    color: #555;
    font-size: 16px;
    line-height: 1.5;
}

.welcome-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
}

.welcome-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 25px;
    border-radius: 8px;
    border: none;
    background-color: #f8f9fa;
    color: #333;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 120px;
}

.welcome-action-btn:hover {
    background-color: #e8eaed;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.welcome-action-btn.primary {
    background-color: #1a73e8;
    color: white;
}

.welcome-action-btn.primary:hover {
    background-color: #1765cc;
}

.welcome-action-btn i {
    font-size: 24px;
    margin-bottom: 8px;
}

.welcome-action-btn span {
    font-size: 14px;
    font-weight: 500;
}

.welcome-footer {
    padding: 15px;
    text-align: center;
    background-color: #f8f9fa;
    border-top: 1px solid #e8eaed;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

.welcome-footer p {
    margin: 0;
    font-size: 12px;
    color: #70757a;
    text-align: center;
    width: 100%;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Recent files section */
.recent-files {
    margin-top: 20px;
    text-align: left;
    border-top: 1px solid #e8eaed;
    padding-top: 20px;
}

.recent-files h3 {
    font-size: 16px;
    margin: 0 0 10px;
    color: #555;
}

.recent-file-item {
    display: flex;
    align-items: center;
    padding: 8px 10px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.recent-file-item:hover {
    background-color: #f1f3f4;
}

.recent-file-icon {
    color: #1a73e8;
    margin-right: 10px;
    font-size: 18px;
}

.recent-file-name {
    font-size: 14px;
    color: #333;
}

.recent-file-date {
    margin-left: auto;
    font-size: 12px;
    color: #70757a;
}

.no-recent-files {
    padding: 15px;
    text-align: center;
    color: #70757a;
    font-style: italic;
    background-color: #f8f9fa;
    border-radius: 4px;
    margin-top: 10px;
}
