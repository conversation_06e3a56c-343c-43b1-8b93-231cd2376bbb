/* Toolbar Container */
#toolbar {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    flex-wrap: wrap;
}

/* Toolbar Groups */
.toolbar-group {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 2px;
    border-radius: 4px;
}

/* Toolbar Buttons */
#toolbar button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6px;
    border: 1px solid transparent;
    background: transparent;
    border-radius: 4px;
    cursor: pointer;
    color: #495057;
    transition: all 0.2s ease;
}

#toolbar button:hover {
    background: #e9ecef;
    border-color: #dee2e6;
}

#toolbar button:active {
    background: #dee2e6;
    border-color: #ced4da;
}

#toolbar button.active {
    background: #e9ecef;
    border-color: #ced4da;
    color: #228be6;
}

/* Material Icons in Toolbar */
#toolbar .material-icons {
    font-size: 20px;
}

/* Toolbar Separators */
.toolbar-separator {
    width: 1px;
    height: 24px;
    background: #dee2e6;
    margin: 0 8px;
}

/* Color Pickers */
#toolbar input[type="color"] {
    width: 24px;
    height: 24px;
    padding: 0;
    border: 1px solid #ced4da;
    border-radius: 4px;
    cursor: pointer;
}

/* Labels */
#toolbar label {
    font-size: 14px;
    color: #495057;
    margin-right: 4px;
}

/* Select Dropdowns */
#toolbar select {
    padding: 4px 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background: white;
    color: #495057;
    font-size: 14px;
    cursor: pointer;
}

#toolbar select:hover {
    border-color: #adb5bd;
}

#toolbar select:focus {
    border-color: #228be6;
    outline: none;
    box-shadow: 0 0 0 2px rgba(34,139,230,0.1);
}