// @core/notification-system.js

/**
 * Simple notification system for displaying messages to the user
 */
export class NotificationSystem {
    constructor() {
        this.notifications = [];
        this.container = null;
        this.createContainer();
    }

    /**
     * Create the notification container
     */
    createContainer() {
        // Check if container already exists
        if (document.getElementById('notification-container')) {
            this.container = document.getElementById('notification-container');
            return;
        }

        // Create container
        this.container = document.createElement('div');
        this.container.id = 'notification-container';
        this.container.style.position = 'fixed';
        this.container.style.bottom = '20px';
        this.container.style.right = '20px';
        this.container.style.zIndex = '10000';
        this.container.style.display = 'flex';
        this.container.style.flexDirection = 'column';
        this.container.style.gap = '10px';
        document.body.appendChild(this.container);

        // Add styles
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(20px); }
                to { opacity: 1; transform: translateY(0); }
            }
            @keyframes fadeOut {
                from { opacity: 1; transform: translateY(0); }
                to { opacity: 0; transform: translateY(20px); }
            }
            .notification {
                padding: 12px 16px;
                border-radius: 4px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                max-width: 300px;
                animation: fadeIn 0.3s;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
            .notification.success {
                background-color: #4caf50;
                color: white;
            }
            .notification.error {
                background-color: #f44336;
                color: white;
            }
            .notification.warning {
                background-color: #ff9800;
                color: white;
            }
            .notification.info {
                background-color: #2196f3;
                color: white;
            }
            .notification-close {
                background: none;
                border: none;
                color: inherit;
                cursor: pointer;
                margin-left: 8px;
                padding: 0;
                font-size: 18px;
                opacity: 0.7;
            }
            .notification-close:hover {
                opacity: 1;
            }
            .notification-message {
                flex: 1;
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Add a notification
     * @param {string} message - The notification message
     * @param {string} type - The notification type (success, error, warning, info)
     * @param {number} duration - The duration in milliseconds (default: 3000)
     */
    addNotification(message, type = 'info', duration = 3000) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        
        // Create message element
        const messageElement = document.createElement('div');
        messageElement.className = 'notification-message';
        messageElement.textContent = message;
        notification.appendChild(messageElement);
        
        // Create close button
        const closeButton = document.createElement('button');
        closeButton.className = 'notification-close';
        closeButton.innerHTML = '&times;';
        closeButton.addEventListener('click', () => {
            this.removeNotification(notification);
        });
        notification.appendChild(closeButton);
        
        // Add to container
        this.container.appendChild(notification);
        
        // Add to notifications array
        this.notifications.push(notification);
        
        // Remove after duration
        setTimeout(() => {
            this.removeNotification(notification);
        }, duration);
        
        return notification;
    }

    /**
     * Remove a notification
     * @param {HTMLElement} notification - The notification element to remove
     */
    removeNotification(notification) {
        // Add fadeOut animation
        notification.style.animation = 'fadeOut 0.3s';
        
        // Remove after animation completes
        setTimeout(() => {
            if (notification.parentNode === this.container) {
                this.container.removeChild(notification);
            }
            
            // Remove from notifications array
            const index = this.notifications.indexOf(notification);
            if (index !== -1) {
                this.notifications.splice(index, 1);
            }
        }, 300);
    }

    /**
     * Clear all notifications
     */
    clearAll() {
        // Clone the array to avoid issues during iteration
        const notificationsToRemove = [...this.notifications];
        
        // Remove each notification
        notificationsToRemove.forEach(notification => {
            this.removeNotification(notification);
        });
    }
}

// Export a singleton instance
export const notificationSystem = new NotificationSystem();
