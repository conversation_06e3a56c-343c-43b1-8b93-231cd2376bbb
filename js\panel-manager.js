// js/panel-manager.js

/**
 * PanelManager class for handling UI panels
 */
class PanelManager {
    constructor() {
        this.panels = {};
        this.activePanel = null;
        console.log('PanelManager initialized');
    }

    /**
     * Register a new panel
     * @param {string} id - Panel identifier
     * @param {HTMLElement} element - Panel DOM element
     */
    registerPanel(id, element) {
        if (!id || !element) {
            console.error('Invalid panel registration parameters');
            return;
        }
        
        this.panels[id] = {
            element: element,
            isVisible: false
        };
        
        // Initially hide the panel
        element.style.display = 'none';
    }

    /**
     * Show a panel
     * @param {string} id - Panel identifier
     */
    showPanel(id) {
        if (!this.panels[id]) {
            console.error(`Panel ${id} not found`);
            return;
        }
        
        // Hide current active panel if exists
        if (this.activePanel && this.panels[this.activePanel]) {
            this.panels[this.activePanel].element.style.display = 'none';
            this.panels[this.activePanel].isVisible = false;
        }
        
        // Show the requested panel
        this.panels[id].element.style.display = 'block';
        this.panels[id].isVisible = true;
        this.activePanel = id;
    }

    /**
     * Hide a panel
     * @param {string} id - Panel identifier
     */
    hidePanel(id) {
        if (!this.panels[id]) {
            console.error(`Panel ${id} not found`);
            return;
        }
        
        this.panels[id].element.style.display = 'none';
        this.panels[id].isVisible = false;
        
        if (this.activePanel === id) {
            this.activePanel = null;
        }
    }

    /**
     * Toggle panel visibility
     * @param {string} id - Panel identifier
     */
    togglePanel(id) {
        if (!this.panels[id]) {
            console.error(`Panel ${id} not found`);
            return;
        }
        
        if (this.panels[id].isVisible) {
            this.hidePanel(id);
        } else {
            this.showPanel(id);
        }
    }

    /**
     * Get the active panel
     * @returns {string|null} - Active panel ID or null
     */
    getActivePanel() {
        return this.activePanel;
    }
}

// Export the PanelManager class
export default PanelManager;
