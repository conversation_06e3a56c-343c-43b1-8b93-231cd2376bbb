// js/sheet_manager.js

// Import required modules
import utils from './utils.js';
import { renderSheetData } from './renderer.js';

/**
 * SheetManager class for handling multiple sheets in a workbook
 */
class SheetManager {
    constructor() {
        this.workbook = null;
        this.sheets = [];
        this.activeSheetIndex = 0;
        this.sheetTabsContainer = null;
        this.spreadsheetContainer = null;
        this.onSheetChange = null;
    }

    /**
     * Initialize the sheet manager
     * @param {Object} workbook - The workbook object
     * @param {Function} onSheetChangeCallback - Callback function when sheet changes
     * @param {Object} onSheetChangeCallback.sheet - The new active sheet
     * @param {number} onSheetChangeCallback.index - The index of the new active sheet
     * @returns {void}
     */
    initialize(workbook, onSheetChangeCallback) {
        this.workbook = workbook;
        this.onSheetChange = onSheetChangeCallback;

        // Check if DOM is ready before proceeding
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            this._completeInitialization();
        } else {
            // Defer initialization until DOM is ready
            document.addEventListener('DOMContentLoaded', () => this._completeInitialization());
        }
    }

    /**
     * Complete the initialization process once DOM is ready
     * @private
     */
    _completeInitialization() {
        this.sheetTabsContainer = document.getElementById('sheetTabsContainer');
        this.spreadsheetContainer = document.getElementById('spreadsheetContainer');

        if (!this.sheetTabsContainer || !this.spreadsheetContainer) {
            console.error('Required DOM elements not found. Sheet manager initialization failed.');
            return;
        }

        // Initialize sheets array based on the active library
        this.initializeSheets();

        // Set up event listeners
        this.setupEventListeners();

        // Render the sheet tabs
        this.renderSheetTabs();

        // Activate the first sheet
        this.activateSheet(0);
    }

    /**
     * Initialize sheets array based on the active library
     */
    initializeSheets() {
        this.sheets = [];

        try {
            switch (utils.excel.activeLibrary) {
                case 'xlsx-populate':
                    // XlsxPopulate stores sheets in the workbook
                    for (let i = 0; i < this.workbook.sheets().length; i++) {
                        const sheet = this.workbook.sheet(i);
                        this.sheets.push({
                            name: sheet.name(),
                            sheet: sheet
                        });
                    }
                    break;

                case 'xlsx':
                    // XLSX stores sheet names in SheetNames array
                    for (let i = 0; i < this.workbook.SheetNames.length; i++) {
                        const sheetName = this.workbook.SheetNames[i];
                        this.sheets.push({
                            name: sheetName,
                            sheet: this.workbook.Sheets[sheetName]
                        });
                    }
                    break;

                case 'exceljs':
                    // ExcelJS stores worksheets in a collection
                    this.workbook.eachSheet((sheet, id) => {
                        this.sheets.push({
                            name: sheet.name,
                            sheet: sheet
                        });
                    });
                    break;

                case 'luckysheet':
                    // Luckysheet data is an array of sheets
                    for (let i = 0; i < this.workbook.length; i++) {
                        this.sheets.push({
                            name: this.workbook[i].name,
                            sheet: this.workbook[i]
                        });
                    }
                    break;

                case 'handsontable':
                    // For Handsontable, we only have one sheet by default
                    this.sheets.push({
                        name: 'Sheet1',
                        sheet: this.workbook
                    });
                    break;

                default:
                    console.error(`Library ${utils.excel.activeLibrary} not supported for multi-sheet`);
                    // Add a default sheet
                    this.sheets.push({
                        name: 'Sheet1',
                        sheet: this.workbook
                    });
            }
        } catch (error) {
            console.error('Error initializing sheets:', error);
            // Add a default sheet
            this.sheets.push({
                name: 'Sheet1',
                sheet: this.workbook
            });
        }

        // If no sheets were found, add a default one
        if (this.sheets.length === 0) {
            this.sheets.push({
                name: 'Sheet1',
                sheet: this.workbook
            });
        }
    }

    /**
     * Set up event listeners for sheet tabs
     */
    setupEventListeners() {
        // Add sheet button
        const addSheetBtn = document.getElementById('addSheetBtn');
        if (addSheetBtn) {
            addSheetBtn.addEventListener('click', () => this.addNewSheet());
        }

        // Sheet tab context menu
        document.addEventListener('contextmenu', (e) => {
            const target = e.target.closest('.sheet-tab');
            if (target) {
                e.preventDefault();
                this.showSheetTabMenu(target, e.clientX, e.clientY);
            }
        });

        // Close sheet tab menu when clicking elsewhere
        document.addEventListener('click', () => {
            const menu = document.querySelector('.sheet-tab-menu');
            if (menu) {
                menu.remove();
            }
        });
    }

    /**
     * Render the sheet tabs
     */
    renderSheetTabs() {
        // Clear existing tabs (except the add button)
        const addSheetBtn = document.getElementById('addSheetBtn');
        this.sheetTabsContainer.innerHTML = '';
        this.sheetTabsContainer.appendChild(addSheetBtn);

        // Create tabs for each sheet
        this.sheets.forEach((sheet, index) => {
            const tab = document.createElement('div');
            tab.className = 'sheet-tab';
            tab.dataset.index = index;
            tab.textContent = sheet.name;

            // Add click event to activate the sheet
            tab.addEventListener('click', () => this.activateSheet(index));

            // Add double-click event to rename the sheet
            tab.addEventListener('dblclick', (e) => {
                e.stopPropagation();
                this.startRenameSheet(index);
            });

            // Insert before the add button
            this.sheetTabsContainer.insertBefore(tab, addSheetBtn);
        });
    }

    /**
     * Activate a sheet by index
     * @param {number} index - The index of the sheet to activate
     */
    activateSheet(index) {
        if (index < 0 || index >= this.sheets.length) {
            console.error(`Invalid sheet index: ${index}`);
            return;
        }

        // Update active sheet index
        this.activeSheetIndex = index;

        // Update tab styling
        const tabs = this.sheetTabsContainer.querySelectorAll('.sheet-tab');
        tabs.forEach(tab => tab.classList.remove('active'));
        tabs[index].classList.add('active');

        // Get the active sheet
        const activeSheet = this.sheets[index].sheet;

        // Update global currentSheet reference
        window.currentSheet = activeSheet;

        // Notify history manager about sheet change if available
        if (window.historyManager) {
            try {
                // Update the current sheet in the history manager
                window.historyManager.getSheetHistory(activeSheet);
                // Update the undo/redo buttons state
                window.historyManager.updateUndoRedoButtons();
            } catch (error) {
                console.error('Error updating history manager for sheet change:', error);
            }
        }

        // Render the sheet
        renderSheetData(activeSheet, this.spreadsheetContainer);

        // Call the onSheetChange callback if provided
        if (typeof this.onSheetChange === 'function') {
            this.onSheetChange(activeSheet, index);
        }
    }

    /**
     * Add a new sheet to the workbook
     */
    addNewSheet() {
        const sheetName = this.generateUniqueSheetName();
        let newSheet;

        try {
            switch (utils.excel.activeLibrary) {
                case 'xlsx-populate':
                    newSheet = this.workbook.addSheet(sheetName);
                    break;

                case 'xlsx':
                    newSheet = XLSX.utils.aoa_to_sheet([[]]);
                    XLSX.utils.book_append_sheet(this.workbook, newSheet, sheetName);
                    break;

                case 'exceljs':
                    newSheet = this.workbook.addWorksheet(sheetName);
                    break;

                case 'luckysheet':
                    newSheet = {
                        name: sheetName,
                        index: this.workbook.length,
                        status: 1,
                        order: this.workbook.length,
                        celldata: [],
                        config: {}
                    };
                    this.workbook.push(newSheet);
                    break;

                case 'handsontable':
                    // Handsontable doesn't support multiple sheets natively
                    // We'll create a new data array
                    newSheet = { data: [[]] };
                    break;

                default:
                    console.error(`Library ${utils.excel.activeLibrary} not supported for adding sheets`);
                    return;
            }

            // Add to sheets array
            this.sheets.push({
                name: sheetName,
                sheet: newSheet
            });

            // Re-render sheet tabs
            this.renderSheetTabs();

            // Activate the new sheet
            this.activateSheet(this.sheets.length - 1);

        } catch (error) {
            console.error('Error adding new sheet:', error);
        }
    }

    /**
     * Generate a unique sheet name
     * @returns {string} A unique sheet name
     */
    generateUniqueSheetName() {
        const baseSheetName = 'Sheet';
        let counter = this.sheets.length + 1;
        let sheetName = `${baseSheetName}${counter}`;

        // Check if the name already exists
        while (this.sheets.some(sheet => sheet.name === sheetName)) {
            counter++;
            sheetName = `${baseSheetName}${counter}`;
        }

        return sheetName;
    }

    /**
     * Start renaming a sheet
     * @param {number} index - The index of the sheet to rename
     */
    startRenameSheet(index) {
        const tab = this.sheetTabsContainer.querySelectorAll('.sheet-tab')[index];
        const currentName = this.sheets[index].name;

        // Create input element
        const input = document.createElement('input');
        input.type = 'text';
        input.className = 'rename-input';
        input.value = currentName;

        // Clear the tab content
        tab.textContent = '';
        tab.appendChild(input);

        // Focus the input
        input.focus();
        input.select();

        // Handle input events
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                this.finishRenameSheet(index, input.value);
            } else if (e.key === 'Escape') {
                this.cancelRenameSheet(index);
            }
        });

        input.addEventListener('blur', () => {
            this.finishRenameSheet(index, input.value);
        });
    }

    /**
     * Finish renaming a sheet
     * @param {number} index - The index of the sheet to rename
     * @param {string} newName - The new name for the sheet
     */
    finishRenameSheet(index, newName) {
        // Validate the new name
        newName = newName.trim();
        if (!newName) {
            newName = this.sheets[index].name; // Revert to original name if empty
        }

        try {
            // Update the sheet name in the workbook
            switch (utils.excel.activeLibrary) {
                case 'xlsx-populate':
                    this.sheets[index].sheet.name(newName);
                    break;

                case 'xlsx':
                    // For XLSX, we need to rename the sheet in the workbook
                    const oldName = this.workbook.SheetNames[index];
                    const sheet = this.workbook.Sheets[oldName];

                    // Remove the old name
                    delete this.workbook.Sheets[oldName];

                    // Add with the new name
                    this.workbook.SheetNames[index] = newName;
                    this.workbook.Sheets[newName] = sheet;
                    break;

                case 'exceljs':
                    this.sheets[index].sheet.name = newName;
                    break;

                case 'luckysheet':
                    this.sheets[index].sheet.name = newName;
                    break;

                case 'handsontable':
                    // Handsontable doesn't have sheet names
                    break;
            }

            // Update the name in our sheets array
            this.sheets[index].name = newName;

            // Re-render the sheet tabs
            this.renderSheetTabs();

            // Re-activate the current sheet
            this.activateSheet(index);

        } catch (error) {
            console.error('Error renaming sheet:', error);
            this.cancelRenameSheet(index);
        }
    }

    /**
     * Cancel renaming a sheet
     * @param {number} index - The index of the sheet
     */
    cancelRenameSheet(index) {
        // Re-render the sheet tabs
        this.renderSheetTabs();

        // Re-activate the current sheet
        this.activateSheet(index);
    }

    /**
     * Delete a sheet
     * @param {number} index - The index of the sheet to delete
     */
    deleteSheet(index) {
        // Don't allow deleting the last sheet
        if (this.sheets.length <= 1) {
            alert('Cannot delete the last sheet.');
            return;
        }

        try {
            // Remove the sheet from the workbook
            switch (utils.excel.activeLibrary) {
                case 'xlsx-populate':
                    this.workbook.deleteSheet(this.sheets[index].name);
                    break;

                case 'xlsx':
                    // For XLSX, we need to remove the sheet from the workbook
                    const sheetName = this.workbook.SheetNames[index];

                    // Remove the sheet
                    this.workbook.SheetNames.splice(index, 1);
                    delete this.workbook.Sheets[sheetName];
                    break;

                case 'exceljs':
                    this.workbook.removeWorksheet(this.sheets[index].sheet.id);
                    break;

                case 'luckysheet':
                    this.workbook.splice(index, 1);
                    break;

                case 'handsontable':
                    // Handsontable doesn't support multiple sheets natively
                    break;
            }

            // Remove from our sheets array
            this.sheets.splice(index, 1);

            // Re-render the sheet tabs
            this.renderSheetTabs();

            // Activate the previous sheet or the first sheet if we deleted the first one
            const newIndex = index > 0 ? index - 1 : 0;
            this.activateSheet(newIndex);

        } catch (error) {
            console.error('Error deleting sheet:', error);
        }
    }

    /**
     * Show the sheet tab context menu
     * @param {HTMLElement} tab - The tab element
     * @param {number} x - The x position for the menu
     * @param {number} y - The y position for the menu
     */
    showSheetTabMenu(tab, x, y) {
        // Remove any existing menu
        const existingMenu = document.querySelector('.sheet-tab-menu');
        if (existingMenu) {
            existingMenu.remove();
        }

        const index = parseInt(tab.dataset.index);

        // Create menu
        const menu = document.createElement('div');
        menu.className = 'sheet-tab-menu';
        menu.style.left = `${x}px`;
        menu.style.top = `${y}px`;

        // Add menu items
        const menuItems = [
            { text: 'Rename', icon: 'edit', action: () => this.startRenameSheet(index) },
            { text: 'Delete', icon: 'delete', action: () => this.deleteSheet(index) },
            { text: 'Move Left', icon: 'arrow_back', action: () => this.moveSheet(index, -1) },
            { text: 'Move Right', icon: 'arrow_forward', action: () => this.moveSheet(index, 1) },
            { text: 'Duplicate', icon: 'content_copy', action: () => this.duplicateSheet(index) }
        ];

        menuItems.forEach(item => {
            const menuItem = document.createElement('div');
            menuItem.className = 'sheet-tab-menu-item';
            menuItem.innerHTML = `
                <span class="material-icons">${item.icon}</span>
                ${item.text}
            `;
            menuItem.addEventListener('click', (e) => {
                e.stopPropagation();
                menu.remove();
                item.action();
            });
            menu.appendChild(menuItem);
        });

        // Add to document
        document.body.appendChild(menu);
    }

    /**
     * Move a sheet to a new position
     * @param {number} index - The current index of the sheet
     * @param {number} direction - The direction to move (-1 for left, 1 for right)
     */
    moveSheet(index, direction) {
        const newIndex = index + direction;

        // Check if the new index is valid
        if (newIndex < 0 || newIndex >= this.sheets.length) {
            return;
        }

        try {
            // Move the sheet in the workbook
            switch (utils.excel.activeLibrary) {
                case 'xlsx-populate':
                    // XlsxPopulate doesn't have a direct method to move sheets
                    // We'll need to recreate the order
                    break;

                case 'xlsx':
                    // For XLSX, we need to move the sheet in the SheetNames array
                    const sheetName = this.workbook.SheetNames[index];
                    this.workbook.SheetNames.splice(index, 1);
                    this.workbook.SheetNames.splice(newIndex, 0, sheetName);
                    break;

                case 'exceljs':
                    // ExcelJS doesn't have a direct method to move sheets
                    break;

                case 'luckysheet':
                    // Move the sheet in the array
                    const sheet = this.workbook[index];
                    this.workbook.splice(index, 1);
                    this.workbook.splice(newIndex, 0, sheet);
                    break;

                case 'handsontable':
                    // Handsontable doesn't support multiple sheets natively
                    break;
            }

            // Move in our sheets array
            const sheet = this.sheets[index];
            this.sheets.splice(index, 1);
            this.sheets.splice(newIndex, 0, sheet);

            // Re-render the sheet tabs
            this.renderSheetTabs();

            // Activate the moved sheet
            this.activateSheet(newIndex);

        } catch (error) {
            console.error('Error moving sheet:', error);
        }
    }

    /**
     * Duplicate a sheet
     * @param {number} index - The index of the sheet to duplicate
     */
    duplicateSheet(index) {
        try {
            const originalSheet = this.sheets[index];
            const newSheetName = `${originalSheet.name} (Copy)`;
            let newSheet;

            switch (utils.excel.activeLibrary) {
                case 'xlsx-populate':
                    // Clone the sheet
                    newSheet = this.workbook.cloneSheet(originalSheet.sheet, newSheetName);
                    break;

                case 'xlsx':
                    // For XLSX, we need to clone the sheet data
                    const originalData = XLSX.utils.sheet_to_json(originalSheet.sheet, { header: 1 });
                    newSheet = XLSX.utils.aoa_to_sheet(originalData);
                    XLSX.utils.book_append_sheet(this.workbook, newSheet, newSheetName);
                    break;

                case 'exceljs':
                    // ExcelJS has a copyWorksheet method
                    newSheet = this.workbook.addWorksheet(newSheetName);
                    // Copy cells and properties
                    originalSheet.sheet.eachRow({ includeEmpty: true }, (row, rowIndex) => {
                        row.eachCell({ includeEmpty: true }, (cell, colIndex) => {
                            const newCell = newSheet.getCell(rowIndex, colIndex);
                            newCell.value = cell.value;
                            newCell.style = Object.assign({}, cell.style);
                        });
                    });
                    break;

                case 'luckysheet':
                    // Clone the sheet data
                    newSheet = JSON.parse(JSON.stringify(originalSheet.sheet));
                    newSheet.name = newSheetName;
                    newSheet.index = this.workbook.length;
                    newSheet.order = this.workbook.length;
                    this.workbook.push(newSheet);
                    break;

                case 'handsontable':
                    // Clone the data
                    newSheet = {
                        data: JSON.parse(JSON.stringify(originalSheet.sheet.data))
                    };
                    break;

                default:
                    console.error(`Library ${utils.excel.activeLibrary} not supported for duplicating sheets`);
                    return;
            }

            // Add to sheets array
            this.sheets.push({
                name: newSheetName,
                sheet: newSheet
            });

            // Re-render sheet tabs
            this.renderSheetTabs();

            // Activate the new sheet
            this.activateSheet(this.sheets.length - 1);

        } catch (error) {
            console.error('Error duplicating sheet:', error);
        }
    }

    /**
     * Get the current active sheet
     * @returns {Object} The active sheet object
     */
    getActiveSheet() {
        return this.sheets[this.activeSheetIndex].sheet;
    }

    /**
     * Get the current active sheet name
     * @returns {string} The active sheet name
     */
    getActiveSheetName() {
        return this.sheets[this.activeSheetIndex].name;
    }

    /**
     * Get all sheets
     * @returns {Array} Array of sheet objects
     */
    getAllSheets() {
        return this.sheets;
    }
}

// Export a singleton instance
const sheetManager = new SheetManager();
export default sheetManager;
