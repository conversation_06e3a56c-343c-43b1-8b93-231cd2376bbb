// js/sheet_actions.js

// Note: rendererFn is a function passed in to redraw the sheet (e.g., from main_script.js)
let rendererFn;
export function initializeActions(renderFunc) {
    rendererFn = renderFunc;
}

export function mergeSelectedCells(sheet, selectionRange) {
    if (!sheet || !selectionRange || !selectionRange.start || !selectionRange.end) return false;
    if (selectionRange.start.r === selectionRange.end.r && selectionRange.start.c === selectionRange.end.c) return false; // Cannot merge single cell

    try {
        sheet.range(
            selectionRange.start.r, selectionRange.start.c,
            selectionRange.end.r, selectionRange.end.c
        ).merged(true);
        if (rendererFn) rendererFn();
        return true;
    } catch (e) { console.error("Error merging cells:", e); return false; }
}

export function unmergeSelectedCells(sheet, selectionRange, activeCellElement) {
    if (!sheet) return false;
    let targetCellForUnmerge;
    if (selectionRange && selectionRange.start) { // Prefer range
        targetCellForUnmerge = sheet.cell(selectionRange.start.r, selectionRange.start.c);
    } else if (activeCellElement) { // Fallback to active cell
        targetCellForUnmerge = sheet.cell(activeCellElement.dataset.address);
    } else {
        return false;
    }

    try {
        // XlsxPopulate unmerges based on any cell within the merged range
        targetCellForUnmerge.merged(false);
        if (rendererFn) rendererFn();
        return true;
    } catch (e) { console.error("Error unmerging cells:", e); return false; }
}

export function insertSheetRow(sheet, rowIndex, position = 'below') { // position can be 'above' or 'below'
    if (!sheet || rowIndex < 1) return false;
    try {
        const targetRow = sheet.row(rowIndex);
        if (position === 'above') {
            targetRow.addRowsBefore(1);
        } else { // below
            targetRow.addRowsAfter(1);
        }
        if (rendererFn) rendererFn();
        return true;
    } catch (e) { console.error("Error inserting row:", e); return false; }
}

export function deleteSelectedSheetRows(sheet, selectionRange, activeCellElement) {
    if (!sheet) return false;
    let startRow, endRow;

    if (selectionRange && selectionRange.start && selectionRange.end) {
        startRow = selectionRange.start.r;
        endRow = selectionRange.end.r;
    } else if (activeCellElement) {
        startRow = endRow = parseInt(activeCellElement.dataset.row, 10);
    } else {
        return false;
    }
    try {
        // Delete rows one by one from bottom up to avoid index shifting issues
        for (let r = endRow; r >= startRow; r--) {
            sheet.row(r).delete();
        }
        if (rendererFn) rendererFn();
        return true;
    } catch (e) { console.error("Error deleting rows:", e); return false; }
}