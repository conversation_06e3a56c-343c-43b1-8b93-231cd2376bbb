/* clipboard.css - Styles for clipboard functionality */

/* Status message */
#clipboard-status-message {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 10px 15px;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    z-index: 9999;
    transition: opacity 0.3s ease-in-out;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

/* Success message */
#clipboard-status-message.success {
    background-color: #e6f4ea;
    color: #1e8e3e;
    border: 1px solid #d2e3fc;
}

/* Error message */
#clipboard-status-message.error {
    background-color: #fce8e6;
    color: #d93025;
    border: 1px solid #f8ccc8;
}

/* Info message */
#clipboard-status-message.info {
    background-color: #e8f0fe;
    color: #1a73e8;
    border: 1px solid #d2e3fc;
}

/* Clipboard history dialog */
.clipboard-history-container {
    max-height: 400px;
    overflow-y: auto;
    padding: 10px 0;
}

.clipboard-empty-message {
    text-align: center;
    color: #5f6368;
    padding: 20px;
}

.clipboard-recent-message {
    text-align: center;
    color: #1a73e8;
    padding: 10px;
    font-size: 13px;
    font-style: italic;
    border-bottom: 1px solid #e8eaed;
    margin-bottom: 10px;
}

.clipboard-history-item {
    border: 1px solid #e8eaed;
    border-radius: 4px;
    margin-bottom: 10px;
    padding: 10px;
    background-color: #f8f9fa;
}

.clipboard-item-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-size: 12px;
    color: #5f6368;
}

.clipboard-item-content {
    padding: 5px 0;
    word-break: break-word;
}

.clipboard-cells-preview, .clipboard-text-preview {
    background-color: white;
    border: 1px solid #e8eaed;
    border-radius: 4px;
    padding: 8px;
    font-family: monospace;
    max-height: 100px;
    overflow-y: auto;
}

.clipboard-preview-text {
    margin-top: 5px;
    white-space: pre-wrap;
}

.clipboard-item-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 8px;
}

.clipboard-paste-btn, .clipboard-delete-btn {
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #dadce0;
    background-color: white;
    cursor: pointer;
    font-size: 12px;
}

.clipboard-paste-btn {
    background-color: #e8f0fe;
    color: #1a73e8;
}

.clipboard-paste-btn:hover {
    background-color: #d2e3fc;
}

.clipboard-delete-btn:hover {
    background-color: #f1f3f4;
}

/* Clipboard button styles */
#copyBtn:hover, #cutBtn:hover, #clipboardBtn:hover, .past-notes-box:hover {
    background-color: #e8f0fe;
}

#copyBtn:hover svg, #cutBtn:hover svg, #clipboardBtn:hover svg, .past-notes-box:hover svg {
    transform: scale(1.1);
}

#copyBtn:active, #cutBtn:active, #clipboardBtn:active, .past-notes-box:active {
    background-color: #d2e3fc;
}
