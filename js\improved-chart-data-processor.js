/**
 * Improved Chart Data Processor
 * Handles extraction and processing of data for charts
 */

/**
 * Extract data from a cell range with improved detection
 * @param {Object} range - The cell range object with start and end properties
 * @returns {Object} The extracted data with categories and values
 */
export function extractDataFromRange(range) {
    if (!range) {
        console.error('Invalid range provided to extractDataFromRange');
        return { categories: [], values: [], series: [], isValidData: false };
    }

    // Get the current sheet - try multiple ways to ensure we get it
    const sheet = window.currentSheet || (window.workbook ? window.workbook.activeSheet() : null);

    if (!sheet) {
        console.error('No active sheet available for data extraction');
        return { categories: [], values: [], series: [], isValidData: false };
    }

    try {
        // Determine the range boundaries
        const startRow = Math.min(range.start.r, range.end.r);
        const endRow = Math.max(range.start.r, range.end.r);
        const startCol = Math.min(range.start.c, range.end.c);
        const endCol = Math.max(range.start.c, range.end.c);

        console.log(`Extracting data from range: R${startRow}C${startCol}:R${endRow}C${endCol}`);

        // Validate the range is within sheet bounds
        try {
            // Check if the range is valid by trying to access the corner cells
            const cornerCells = [
                sheet.cell(startRow, startCol),
                sheet.cell(startRow, endCol),
                sheet.cell(endRow, startCol),
                sheet.cell(endRow, endCol)
            ];

            // If any of these cells don't exist, it will throw an error
            if (!cornerCells.every(cell => cell !== null && cell !== undefined)) {
                throw new Error('Invalid cell range');
            }
        } catch (e) {
            console.error('Range validation error:', e);
            return {
                categories: ['Invalid Range'],
                values: [0],
                series: [{ name: 'Error', data: [0] }],
                isValidData: false
            };
        }

        // Check if we have enough data to create a chart
        if (startRow === endRow && startCol === endCol) {
            console.warn('Single cell selected, not enough data for a chart');
            return {
                categories: ['Not enough data'],
                values: [0],
                series: [{ name: 'Error', data: [0] }],
                isValidData: false
            };
        }

        // Read all cell values in the range
        const cellValues = [];
        for (let r = startRow; r <= endRow; r++) {
            const rowValues = [];
            for (let c = startCol; c <= endCol; c++) {
                try {
                    const cell = sheet.cell(r, c);
                    const value = cell ? cell.value() : null;
                    rowValues.push(value);
                } catch (e) {
                    console.warn(`Error reading cell at R${r}C${c}:`, e);
                    rowValues.push(null);
                }
            }
            cellValues.push(rowValues);
        }

        // Analyze the data structure
        const dataStructure = analyzeDataStructure(cellValues);
        
        // Process data based on the detected structure
        return processDataByStructure(cellValues, dataStructure, startRow, startCol);
    } catch (error) {
        console.error('Error extracting data from range:', error);
        return {
            categories: ['Error'],
            values: [0],
            series: [{ name: 'Error', data: [0] }],
            isValidData: false,
            error: error.message
        };
    }
}

/**
 * Analyze the structure of the data
 * @param {Array} cellValues - 2D array of cell values
 * @returns {Object} Data structure information
 */
function analyzeDataStructure(cellValues) {
    if (!cellValues || cellValues.length === 0 || cellValues[0].length === 0) {
        return { 
            hasRowHeaders: false, 
            hasColumnHeaders: false,
            orientation: 'column' 
        };
    }

    const rowCount = cellValues.length;
    const colCount = cellValues[0].length;
    
    // Determine if first row looks like headers
    const firstRowLooksLikeHeaders = cellValues[0].every(cell => 
        cell !== null && 
        (typeof cell === 'string' || typeof cell === 'number') &&
        !isNaN(parseFloat(cellValues[1]?.[0]))
    );
    
    // Determine if first column looks like headers
    const firstColLooksLikeHeaders = cellValues.every(row => 
        row[0] !== null && 
        (typeof row[0] === 'string' || typeof row[0] === 'number') &&
        !isNaN(parseFloat(row[1]))
    );
    
    // Determine orientation (more rows than columns suggests column-oriented data)
    const isColumnOriented = rowCount > colCount;
    
    return {
        hasRowHeaders: firstRowLooksLikeHeaders,
        hasColumnHeaders: firstColLooksLikeHeaders,
        orientation: isColumnOriented ? 'column' : 'row'
    };
}

/**
 * Process data based on detected structure
 * @param {Array} cellValues - 2D array of cell values
 * @param {Object} structure - Data structure information
 * @param {number} startRow - Starting row index
 * @param {number} startCol - Starting column index
 * @returns {Object} Processed data for charts
 */
function processDataByStructure(cellValues, structure, startRow, startCol) {
    const { hasRowHeaders, hasColumnHeaders, orientation } = structure;
    
    let categories = [];
    let series = [];
    
    if (orientation === 'column') {
        // Column-oriented data (each column is a series)
        if (hasRowHeaders) {
            // First row contains headers
            categories = cellValues[0].slice(hasColumnHeaders ? 1 : 0);
            
            // Process each row as a series
            for (let r = 1; r < cellValues.length; r++) {
                const rowData = cellValues[r];
                const seriesName = hasColumnHeaders ? rowData[0] : `Series ${r}`;
                const seriesValues = hasColumnHeaders ? rowData.slice(1) : rowData;
                
                series.push({
                    name: seriesName,
                    data: seriesValues.map(v => typeof v === 'number' ? v : parseFloat(v) || 0)
                });
            }
        } else {
            // No row headers, use column indices as categories
            categories = Array.from({ length: cellValues[0].length }, (_, i) => 
                hasColumnHeaders ? `Column ${i}` : `Column ${i + 1}`
            );
            
            // Process each row as a series
            for (let r = 0; r < cellValues.length; r++) {
                const rowData = cellValues[r];
                const seriesName = hasColumnHeaders ? rowData[0] : `Series ${r + 1}`;
                const seriesValues = hasColumnHeaders ? rowData.slice(1) : rowData;
                
                series.push({
                    name: seriesName,
                    data: seriesValues.map(v => typeof v === 'number' ? v : parseFloat(v) || 0)
                });
            }
        }
    } else {
        // Row-oriented data (each row is a series)
        if (hasColumnHeaders) {
            // First column contains headers
            categories = cellValues.slice(hasRowHeaders ? 1 : 0).map(row => row[0]);
            
            // Process each column as a series
            for (let c = 1; c < cellValues[0].length; c++) {
                const seriesName = hasRowHeaders ? cellValues[0][c] : `Series ${c}`;
                const seriesValues = [];
                
                for (let r = hasRowHeaders ? 1 : 0; r < cellValues.length; r++) {
                    const value = cellValues[r][c];
                    seriesValues.push(typeof value === 'number' ? value : parseFloat(value) || 0);
                }
                
                series.push({
                    name: seriesName,
                    data: seriesValues
                });
            }
        } else {
            // No column headers, use row indices as categories
            categories = Array.from({ length: cellValues.length }, (_, i) => 
                hasRowHeaders ? `Row ${i}` : `Row ${i + 1}`
            );
            
            // Process each column as a series
            for (let c = 0; c < cellValues[0].length; c++) {
                const seriesName = hasRowHeaders ? cellValues[0][c] : `Series ${c + 1}`;
                const seriesValues = [];
                
                for (let r = 0; r < cellValues.length; r++) {
                    const value = cellValues[r][c];
                    seriesValues.push(typeof value === 'number' ? value : parseFloat(value) || 0);
                }
                
                series.push({
                    name: seriesName,
                    data: seriesValues
                });
            }
        }
    }
    
    // Ensure we have valid data
    const isValidData = categories.length > 0 && series.length > 0 && 
                        series[0].data.length > 0 && 
                        series[0].data.some(v => v !== 0);
    
    return {
        categories,
        values: series[0]?.data || [],
        series,
        isValidData,
        dataOrientation: orientation,
        hasRowHeaders,
        hasColumnHeaders,
        rangeInfo: {
            startRow,
            startCol
        }
    };
}

/**
 * Suggest appropriate chart types based on data
 * @param {Object} data - The extracted data
 * @returns {Array} Array of suggested chart types
 */
export function suggestChartTypes(data) {
    if (!data || !data.isValidData) {
        return ['column']; // Default to column chart
    }
    
    const { series, categories } = data;
    
    // If we have only one series with few categories, pie chart might be good
    if (series.length === 1 && categories.length <= 8) {
        return ['pie', 'column', 'bar'];
    }
    
    // If we have many categories, bar chart might be better than column
    if (categories.length > 8) {
        return ['bar', 'line', 'area'];
    }
    
    // If we have many series, line or area charts might be better
    if (series.length > 3) {
        return ['line', 'area', 'column'];
    }
    
    // Default suggestions
    return ['column', 'line', 'bar', 'area'];
}

/**
 * Process data for a specific chart type
 * @param {Object} data - The extracted data
 * @param {string} chartType - The chart type
 * @returns {Object} Processed data for the chart
 */
export function processChartData(data, chartType) {
    if (!data || !data.isValidData) {
        return data;
    }
    
    // Clone the data to avoid modifying the original
    const processedData = JSON.parse(JSON.stringify(data));
    
    // Process based on chart type
    switch (chartType) {
        case 'pie':
        case 'doughnut':
            // For pie charts, we only need one series
            processedData.series = [{
                name: processedData.series[0]?.name || 'Series 1',
                data: processedData.values
            }];
            break;
            
        case 'scatter':
            // For scatter charts, we need x and y values
            // If we have multiple series, keep them
            break;
            
        default:
            // For other charts, keep the data as is
            break;
    }
    
    return processedData;
}
