/**
 * Chrome Storage Integration for Chart Data
 * Provides a simple API for storing and retrieving chart data using Chrome storage
 */

class ChromeStorageIntegration {
    constructor() {
        // Check if Chrome storage is available
        this.storageAvailable = typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local;
        
        // Fallback to localStorage if Chrome storage is not available
        if (!this.storageAvailable) {
            console.warn('Chrome storage not available, falling back to localStorage');
        }
    }
    
    /**
     * Save chart data to storage
     * @param {string} key - The storage key
     * @param {Object} data - The data to store
     * @returns {Promise} Promise that resolves when data is saved
     */
    async saveData(key, data) {
        return new Promise((resolve, reject) => {
            try {
                // Add timestamp
                const dataWithTimestamp = {
                    ...data,
                    timestamp: new Date().toISOString()
                };
                
                if (this.storageAvailable) {
                    // Use Chrome storage
                    chrome.storage.local.set({ [key]: dataWithTimestamp }, () => {
                        if (chrome.runtime.lastError) {
                            console.error('Chrome storage error:', chrome.runtime.lastError);
                            reject(chrome.runtime.lastError);
                        } else {
                            resolve();
                        }
                    });
                } else {
                    // Use localStorage
                    localStorage.setItem(key, JSON.stringify(dataWithTimestamp));
                    resolve();
                }
            } catch (error) {
                console.error('Error saving data to storage:', error);
                reject(error);
            }
        });
    }
    
    /**
     * Get chart data from storage
     * @param {string} key - The storage key
     * @returns {Promise} Promise that resolves with the stored data
     */
    async getData(key) {
        return new Promise((resolve, reject) => {
            try {
                if (this.storageAvailable) {
                    // Use Chrome storage
                    chrome.storage.local.get(key, (result) => {
                        if (chrome.runtime.lastError) {
                            console.error('Chrome storage error:', chrome.runtime.lastError);
                            reject(chrome.runtime.lastError);
                        } else {
                            resolve(result[key] || null);
                        }
                    });
                } else {
                    // Use localStorage
                    const data = localStorage.getItem(key);
                    resolve(data ? JSON.parse(data) : null);
                }
            } catch (error) {
                console.error('Error getting data from storage:', error);
                reject(error);
            }
        });
    }
    
    /**
     * Remove chart data from storage
     * @param {string} key - The storage key
     * @returns {Promise} Promise that resolves when data is removed
     */
    async removeData(key) {
        return new Promise((resolve, reject) => {
            try {
                if (this.storageAvailable) {
                    // Use Chrome storage
                    chrome.storage.local.remove(key, () => {
                        if (chrome.runtime.lastError) {
                            console.error('Chrome storage error:', chrome.runtime.lastError);
                            reject(chrome.runtime.lastError);
                        } else {
                            resolve();
                        }
                    });
                } else {
                    // Use localStorage
                    localStorage.removeItem(key);
                    resolve();
                }
            } catch (error) {
                console.error('Error removing data from storage:', error);
                reject(error);
            }
        });
    }
    
    /**
     * Get all chart data from storage
     * @returns {Promise} Promise that resolves with all stored data
     */
    async getAllData() {
        return new Promise((resolve, reject) => {
            try {
                if (this.storageAvailable) {
                    // Use Chrome storage
                    chrome.storage.local.get(null, (result) => {
                        if (chrome.runtime.lastError) {
                            console.error('Chrome storage error:', chrome.runtime.lastError);
                            reject(chrome.runtime.lastError);
                        } else {
                            resolve(result);
                        }
                    });
                } else {
                    // Use localStorage
                    const data = {};
                    for (let i = 0; i < localStorage.length; i++) {
                        const key = localStorage.key(i);
                        if (key.startsWith('chart_')) {
                            data[key] = JSON.parse(localStorage.getItem(key));
                        }
                    }
                    resolve(data);
                }
            } catch (error) {
                console.error('Error getting all data from storage:', error);
                reject(error);
            }
        });
    }
    
    /**
     * Save chart range selection to storage
     * @param {Object} range - The selected range
     * @returns {Promise} Promise that resolves when range is saved
     */
    async saveChartRange(range) {
        return this.saveData('chart_selected_range', range);
    }
    
    /**
     * Get chart range selection from storage
     * @returns {Promise} Promise that resolves with the stored range
     */
    async getChartRange() {
        return this.getData('chart_selected_range');
    }
    
    /**
     * Save chart configuration to storage
     * @param {string} chartId - The chart ID
     * @param {Object} config - The chart configuration
     * @returns {Promise} Promise that resolves when config is saved
     */
    async saveChartConfig(chartId, config) {
        return this.saveData(`chart_config_${chartId}`, config);
    }
    
    /**
     * Get chart configuration from storage
     * @param {string} chartId - The chart ID
     * @returns {Promise} Promise that resolves with the stored config
     */
    async getChartConfig(chartId) {
        return this.getData(`chart_config_${chartId}`);
    }
    
    /**
     * Save chart data to storage
     * @param {string} chartId - The chart ID
     * @param {Object} data - The chart data
     * @returns {Promise} Promise that resolves when data is saved
     */
    async saveChartData(chartId, data) {
        return this.saveData(`chart_data_${chartId}`, data);
    }
    
    /**
     * Get chart data from storage
     * @param {string} chartId - The chart ID
     * @returns {Promise} Promise that resolves with the stored data
     */
    async getChartData(chartId) {
        return this.getData(`chart_data_${chartId}`);
    }
    
    /**
     * Save all chart information to storage
     * @param {string} chartId - The chart ID
     * @param {Object} chartInfo - The complete chart information
     * @returns {Promise} Promise that resolves when info is saved
     */
    async saveChartInfo(chartId, chartInfo) {
        // Save chart ID to list of charts
        const chartList = await this.getChartList();
        if (!chartList.includes(chartId)) {
            chartList.push(chartId);
            await this.saveData('chart_list', chartList);
        }
        
        // Save chart info
        return this.saveData(`chart_${chartId}`, chartInfo);
    }
    
    /**
     * Get all chart information from storage
     * @param {string} chartId - The chart ID
     * @returns {Promise} Promise that resolves with the stored info
     */
    async getChartInfo(chartId) {
        return this.getData(`chart_${chartId}`);
    }
    
    /**
     * Get list of all charts
     * @returns {Promise} Promise that resolves with the list of chart IDs
     */
    async getChartList() {
        const list = await this.getData('chart_list');
        return list || [];
    }
    
    /**
     * Delete a chart
     * @param {string} chartId - The chart ID
     * @returns {Promise} Promise that resolves when chart is deleted
     */
    async deleteChart(chartId) {
        // Remove chart from list
        const chartList = await this.getChartList();
        const newList = chartList.filter(id => id !== chartId);
        await this.saveData('chart_list', newList);
        
        // Remove chart data
        await this.removeData(`chart_${chartId}`);
        await this.removeData(`chart_config_${chartId}`);
        await this.removeData(`chart_data_${chartId}`);
    }
}

// Create and export a singleton instance
const chromeStorageIntegration = new ChromeStorageIntegration();
export default chromeStorageIntegration;
