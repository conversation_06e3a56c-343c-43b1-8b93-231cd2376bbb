/**
 * Chart Storage System
 * Handles persistent storage of chart configurations using IndexedDB
 */

class ChartStorage {
    constructor() {
        this.dbName = 'ChartStorageDB';
        this.dbVersion = 1;
        this.db = null;
        this.dbReady = false;
        this.dbReadyPromise = this.initializeDB();
    }

    /**
     * Initialize the IndexedDB database
     * @returns {Promise} Promise that resolves when the database is ready
     */
    async initializeDB() {
        try {
            return new Promise((resolve, reject) => {
                const request = indexedDB.open(this.dbName, this.dbVersion);

                request.onerror = (event) => {
                    console.error('Chart Storage DB error:', event.target.error);
                    reject(event.target.error);
                };

                request.onsuccess = (event) => {
                    this.db = event.target.result;
                    this.dbReady = true;
                    console.log('Chart Storage DB opened successfully');
                    resolve(this.db);
                };

                request.onupgradeneeded = (event) => {
                    const db = event.target.result;
                    
                    // Create object stores
                    if (!db.objectStoreNames.contains('charts')) {
                        const chartStore = db.createObjectStore('charts', { keyPath: 'id' });
                        chartStore.createIndex('sheetId', 'sheetId', { unique: false });
                        chartStore.createIndex('workbookId', 'workbookId', { unique: false });
                    }
                };
            });
        } catch (error) {
            console.error('Error initializing Chart Storage DB:', error);
            // Fallback to localStorage if IndexedDB fails
            this.useLocalStorage = true;

            // Notify user about degraded functionality
            if (typeof window !== 'undefined' && window.updateStatus) {
                window.updateStatus('Chart storage using fallback mode (localStorage)', 'warning');
            }

            return Promise.resolve();
        }
    }

    /**
     * Ensure database is ready before operations
     * @returns {Promise} Promise that resolves with the database
     */
    async ensureDbReady() {
        if (this.useLocalStorage) {
            return Promise.resolve();
        }
        
        if (!this.dbReady) {
            await this.dbReadyPromise;
        }
        return this.db;
    }

    /**
     * Save a chart configuration
     * @param {Object} chartConfig - The chart configuration to save
     * @returns {Promise} Promise that resolves with the saved chart ID
     */
    async saveChart(chartConfig) {
        // Add timestamp and ensure ID
        const config = {
            ...chartConfig,
            id: chartConfig.id || `chart_${Date.now()}`,
            lastModified: new Date().toISOString()
        };

        try {
            await this.ensureDbReady();
            
            if (this.useLocalStorage) {
                // Fallback to localStorage
                const charts = JSON.parse(localStorage.getItem('charts') || '[]');
                const existingIndex = charts.findIndex(c => c.id === config.id);
                
                if (existingIndex >= 0) {
                    charts[existingIndex] = config;
                } else {
                    charts.push(config);
                }
                
                localStorage.setItem('charts', JSON.stringify(charts));
                return config.id;
            }
            
            return new Promise((resolve, reject) => {
                const transaction = this.db.transaction(['charts'], 'readwrite');
                const store = transaction.objectStore('charts');
                
                const request = store.put(config);
                
                request.onsuccess = () => resolve(config.id);
                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error('Error saving chart:', error);
            throw error;
        }
    }

    /**
     * Get a chart configuration by ID
     * @param {string} chartId - The chart ID
     * @returns {Promise} Promise that resolves with the chart configuration
     */
    async getChart(chartId) {
        try {
            await this.ensureDbReady();
            
            if (this.useLocalStorage) {
                // Fallback to localStorage
                const charts = JSON.parse(localStorage.getItem('charts') || '[]');
                return charts.find(c => c.id === chartId) || null;
            }
            
            return new Promise((resolve, reject) => {
                const transaction = this.db.transaction(['charts'], 'readonly');
                const store = transaction.objectStore('charts');
                
                const request = store.get(chartId);
                
                request.onsuccess = () => resolve(request.result || null);
                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error('Error getting chart:', error);
            return null;
        }
    }

    /**
     * Get all charts for a specific sheet
     * @param {string} sheetId - The sheet ID
     * @returns {Promise} Promise that resolves with an array of chart configurations
     */
    async getChartsForSheet(sheetId) {
        try {
            await this.ensureDbReady();
            
            if (this.useLocalStorage) {
                // Fallback to localStorage
                const charts = JSON.parse(localStorage.getItem('charts') || '[]');
                return charts.filter(c => c.sheetId === sheetId);
            }
            
            return new Promise((resolve, reject) => {
                const transaction = this.db.transaction(['charts'], 'readonly');
                const store = transaction.objectStore('charts');
                const index = store.index('sheetId');
                
                const request = index.getAll(sheetId);
                
                request.onsuccess = () => resolve(request.result || []);
                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error('Error getting charts for sheet:', error);
            return [];
        }
    }

    /**
     * Delete a chart by ID
     * @param {string} chartId - The chart ID
     * @returns {Promise} Promise that resolves when the chart is deleted
     */
    async deleteChart(chartId) {
        try {
            await this.ensureDbReady();
            
            if (this.useLocalStorage) {
                // Fallback to localStorage
                const charts = JSON.parse(localStorage.getItem('charts') || '[]');
                localStorage.setItem('charts', JSON.stringify(charts.filter(c => c.id !== chartId)));
                return true;
            }
            
            return new Promise((resolve, reject) => {
                const transaction = this.db.transaction(['charts'], 'readwrite');
                const store = transaction.objectStore('charts');
                
                const request = store.delete(chartId);
                
                request.onsuccess = () => resolve(true);
                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error('Error deleting chart:', error);
            return false;
        }
    }
}

// Create and export a singleton instance
const chartStorage = new ChartStorage();
export default chartStorage;
