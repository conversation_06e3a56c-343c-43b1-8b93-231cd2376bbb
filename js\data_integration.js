// js/data_integration.js

// Data Integration Manager for handling various data sources

class DataIntegrationManager {
    constructor() {
        this.apiConnections = new Map();
        this.activeConnections = new Set();
    }

    // OData Integration
    async connectOData(config) {
        try {
            const { url, auth, entitySet } = config;
            const headers = this.buildAuthHeaders(auth);

            const response = await fetch(`${url}/${entitySet}`, {
                headers: {
                    ...headers,
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`OData connection failed: ${response.statusText}`);
            }

            const connection = {
                type: 'odata',
                url,
                entitySet,
                auth
            };

            this.apiConnections.set(entitySet, connection);
            this.activeConnections.add(entitySet);

            return connection;
        } catch (error) {
            console.error('OData connection error:', error);
            throw error;
        }
    }

    // REST API Integration
    async connectRestApi(config) {
        try {
            const { url, auth, endpoint } = config;
            const headers = this.buildAuthHeaders(auth);

            const response = await fetch(`${url}${endpoint}`, {
                headers: {
                    ...headers,
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`REST API connection failed: ${response.statusText}`);
            }

            const connection = {
                type: 'rest',
                url,
                endpoint,
                auth
            };

            this.apiConnections.set(endpoint, connection);
            this.activeConnections.add(endpoint);

            return connection;
        } catch (error) {
            console.error('REST API connection error:', error);
            throw error;
        }
    }

    // File System Operations
    async importFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = async (e) => {
                try {
                    const data = await this.parseFileData(file, e.target.result);
                    resolve(data);
                } catch (error) {
                    reject(error);
                }
            };
            
            reader.onerror = () => reject(reader.error);
            
            if (file.type.includes('spreadsheet') || file.name.endsWith('.xlsx')) {
                reader.readAsArrayBuffer(file);
            } else {
                reader.readAsText(file);
            }
        });
    }

    // Export data to file
    async exportFile(data, format) {
        try {
            let content;
            let mimeType;
            let extension;

            switch (format.toLowerCase()) {
                case 'xlsx':
                    content = await this.generateExcel(data);
                    mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                    extension = 'xlsx';
                    break;
                case 'csv':
                    content = this.generateCSV(data);
                    mimeType = 'text/csv';
                    extension = 'csv';
                    break;
                case 'json':
                    content = JSON.stringify(data, null, 2);
                    mimeType = 'application/json';
                    extension = 'json';
                    break;
                default:
                    throw new Error(`Unsupported format: ${format}`);
            }

            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `export-${timestamp}.${extension}`;

            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            link.click();

            URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Export error:', error);
            throw error;
        }
    }

    // Helper Methods
    buildAuthHeaders(auth) {
        const headers = new Headers();

        if (auth.type === 'bearer') {
            headers.append('Authorization', `Bearer ${auth.token}`);
        } else if (auth.type === 'basic') {
            const credentials = btoa(`${auth.username}:${auth.password}`);
            headers.append('Authorization', `Basic ${credentials}`);
        }

        return headers;
    }

    async parseFileData(file, content) {
        if (file.type.includes('spreadsheet') || file.name.endsWith('.xlsx')) {
            return this.parseExcel(content);
        } else if (file.type === 'text/csv' || file.name.endsWith('.csv')) {
            return this.parseCSV(content);
        } else if (file.type === 'application/json' || file.name.endsWith('.json')) {
            return JSON.parse(content);
        }
        throw new Error(`Unsupported file type: ${file.type}`);
    }

    async parseExcel(content) {
        // Using the existing xlsx library
        const workbook = XLSX.read(content, { type: 'array' });
        const result = {};

        workbook.SheetNames.forEach(sheetName => {
            result[sheetName] = XLSX.utils.sheet_to_json(workbook.Sheets[sheetName]);
        });

        return result;
    }

    parseCSV(content) {
        const lines = content.split('\n');
        const headers = lines[0].split(',').map(header => header.trim());
        const data = [];

        for (let i = 1; i < lines.length; i++) {
            if (!lines[i].trim()) continue;
            
            const values = lines[i].split(',').map(value => value.trim());
            const row = {};
            
            headers.forEach((header, index) => {
                row[header] = values[index];
            });
            
            data.push(row);
        }

        return data;
    }

    async generateExcel(data) {
        // Using the existing xlsx library
        const workbook = XLSX.utils.book_new();

        if (typeof data === 'object') {
            Object.entries(data).forEach(([sheetName, sheetData]) => {
                const worksheet = XLSX.utils.json_to_sheet(sheetData);
                XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
            });
        }

        return XLSX.write(workbook, { type: 'array' });
    }

    generateCSV(data) {
        if (!Array.isArray(data)) {
            data = Object.values(data)[0]; // Take first sheet if multiple sheets
        }

        if (!data.length) return '';

        const headers = Object.keys(data[0]);
        const csvRows = [
            headers.join(','),
            ...data.map(row => headers.map(header => row[header]).join(','))
        ];

        return csvRows.join('\n');
    }
}

// Export a singleton instance
const dataIntegration = new DataIntegrationManager();
export default dataIntegration;