// Core connection manager for all external services
export class ConnectionManager {
  constructor() {
    this.connections = {
      acumatica: {
        isConnected: false,
        credentials: null,
        instance: null,
        authExpiration: null
      },
      monday: {
        isConnected: false,
        token: null
      },
      outlook: {
        isConnected: false,
        token: null
      },
      teams: {
        isConnected: false,
        token: null
      },
      freightSample: {
        isConnected: false,
        token: null
      },
      shipWave: {
        isConnected: false,
        apiKey: null
      },
      dymo: {
        isConnected: false,
        config: null
      }
    };
  }

  async init() {
    // Load connection states from Chrome storage
    if (chrome?.storage?.local) {
      try {
        const data = await new Promise(resolve => {
          chrome.storage.local.get(['connections'], data => {
            resolve(data?.connections || null);
          });
        });
        
        if (data) {
          // Ensure all connection types exist in the loaded data
          // by merging with default values
          this.connections = {
            acumatica: {
              isConnected: false,
              credentials: null,
              instance: null,
              authExpiration: null,
              ...data.acumatica
            },
            monday: {
              isConnected: false,
              token: null,
              ...data.monday
            },
            outlook: {
              isConnected: false,
              token: null,
              ...data.outlook
            },
            teams: {
              isConnected: false,
              token: null,
              ...data.teams
            },
            freightSample: {
              isConnected: false,
              token: null,
              ...data.freightSample
            },
            shipWave: {
              isConnected: false,
              apiKey: null,
              ...data.shipWave
            },
            dymo: {
              isConnected: false,
              config: null,
              ...data.dymo
            }
          };
          
          // Check if acumatica auth is expired
          if (this.connections.acumatica.isConnected && this.connections.acumatica.authExpiration) {
            const expiration = new Date(this.connections.acumatica.authExpiration);
            if (new Date() > expiration) {
              this.connections.acumatica.isConnected = false;
              await chrome.storage.local.set({ 'connections': this.connections });
              console.log("Acumatica connection expired");
            }
          }
        }
        
        // Check if user has Monday API key from login
        const userData = await new Promise(resolve => {
          chrome.storage.local.get(['user'], data => {
            resolve(data?.user || null);
          });
        });
        
        if (userData && userData["Monday API Key"]) {
          // Auto-connect to Monday using the API key from login
          this.connections.monday = {
            isConnected: true,
            token: userData["Monday API Key"]
          };
          
          // Save to Chrome storage
          await chrome.storage.local.set({ 'connections': this.connections });
          console.log("Monday.com connected using API key from login");
        }
      } catch (error) {
        console.error('Failed to load connection data:', error);
      }
    }
    
    return this.getConnectionStatus();
  }

  // Acumatica connection methods
  async connectToAcumatica(instance, username, password, company) {
    try {
      // SOAP login request
      const soapEnvelope = `
        <soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
          <soap:Body>
            <Login xmlns="http://www.acumatica.com/generic/">
              <name>${username}</name>
              <password>${password}</password>
              <company>${company}</company>
            </Login>
          </soap:Body>
        </soap:Envelope>
      `;
      
      // Make SOAP request
      const response = await fetch(`${instance}/Soap/Default.asmx`, {
        method: 'POST',
        headers: {
          'Content-Type': 'text/xml',
          'SOAPAction': 'http://www.acumatica.com/generic/login'
        },
        body: soapEnvelope,
        credentials: 'include'
      });
      
      // Check response
      if (!response.ok) {
        const text = await response.text();
        throw new Error(`Login failed with status ${response.status}: ${text}`);
      }
      
      const text = await response.text();
      if (text.includes('faultcode') || text.includes('faultstring')) {
        const errorMatch = text.match(/<faultstring>(.*?)<\/faultstring>/);
        if (errorMatch && errorMatch[1]) {
          throw new Error(errorMatch[1]);
        } else {
          throw new Error('SOAP fault occurred');
        }
      }
      
      // Set connection info
      this.connections.acumatica = {
        isConnected: true,
        credentials: { username, password, company },
        instance: instance,
        authExpiration: new Date(Date.now() + 4 * 60 * 60 * 1000) // 4 hours expiration
      };
      
      // Save to Chrome storage
      if (chrome?.storage?.local) {
        await chrome.storage.local.set({ 'connections': this.connections });
      }
      
      return { 
        success: true,
        message: 'Successfully connected to Acumatica'
      };
    } catch (error) {
      console.error('Acumatica connection error:', error);
      return { 
        success: false, 
        error: error.message || 'Unknown error occurred' 
      };
    }
  }

  async disconnectFromAcumatica() {
    try {
      const instance = this.connections.acumatica.instance;
      
      // SOAP logout request
      const soapEnvelope = `
        <soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
          <soap:Body>
            <Logout xmlns="http://www.acumatica.com/generic/" />
          </soap:Body>
        </soap:Envelope>
      `;
      
      // Make SOAP request
      await fetch(`${instance}/Soap/Default.asmx`, {
        method: 'POST',
        headers: {
          'Content-Type': 'text/xml',
          'SOAPAction': 'http://www.acumatica.com/generic/logout'
        },
        body: soapEnvelope,
        credentials: 'include'
      });
      
      // Clear cookies
      if (chrome?.cookies) {
        const url = new URL(instance);
        const cookies = await chrome.cookies.getAll({ domain: url.hostname });
        
        for (const cookie of cookies) {
          await chrome.cookies.remove({
            url: instance,
            name: cookie.name
          });
        }
      }
      
      // Update connection info
      this.connections.acumatica = {
        isConnected: false,
        credentials: null,
        instance: null,
        authExpiration: null
      };
      
      // Save to Chrome storage
      if (chrome?.storage?.local) {
        await chrome.storage.local.set({ 'connections': this.connections });
      }
      
      return { 
        success: true,
        message: 'Successfully disconnected from Acumatica'
      };
    } catch (error) {
      console.error('Acumatica disconnection error:', error);
      return { 
        success: false, 
        error: error.message || 'Unknown error occurred' 
      };
    }
  }

  // Acumatica image retrieval methods
  async getStockItemImage(inventoryID) {
    try {
      if (!this.connections.acumatica.isConnected) {
        throw new Error('Not connected to Acumatica');
      }
      
      const instance = this.connections.acumatica.instance;
      
      // Get cookies for authentication - with safe check for cookies API
      let cookieString = '';
      if (chrome?.cookies?.getAll) {
        try {
          const url = new URL(instance);
          const cookies = await chrome.cookies.getAll({ domain: url.hostname });
          cookieString = cookies.map(c => `${c.name}=${c.value}`).join('; ');
        } catch (cookieError) {
          // Silently continue without cookies - might work with session credentials
        }
      }
      
      // Build REST URL for StockItem
      const apiUrl = `${instance}/entity/Default/20.200.001/StockItem?$filter=InventoryID eq '${inventoryID}'&$select=InventoryID,Files&$expand=Files`;
      
      // Make request
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          ...(cookieString ? { 'Cookie': cookieString } : {})
        }
      });
      
      // Check response
      if (!response.ok) {
        if (response.status === 401) {
          // Handle expired session
          this.connections.acumatica.isConnected = false;
          if (chrome?.storage?.local) {
            await chrome.storage.local.set({ 'connections': this.connections });
          }
          throw new Error('Session expired. Please reconnect to Acumatica.');
        }
        
        const text = await response.text();
        throw new Error(`API call failed (${response.status}): ${text}`);
      }
      
      // Return data
      const jsonData = await response.json();
      return { success: true, data: jsonData };
    } catch (error) {
      console.error('Error retrieving stock item image:', error);
      return { success: false, error: error.message };
    }
  }

  async getMultipleStockItemImages(inventoryIDs) {
    try {
      if (!this.connections.acumatica.isConnected) {
        throw new Error('Not connected to Acumatica');
      }
      
      const instance = this.connections.acumatica.instance;
      
      // Get cookies for authentication - with safe check for cookies API
      let cookieString = '';
      if (chrome?.cookies?.getAll) {
        try {
          const url = new URL(instance);
          const cookies = await chrome.cookies.getAll({ domain: url.hostname });
          cookieString = cookies.map(c => `${c.name}=${c.value}`).join('; ');
        } catch (cookieError) {
          // Silently continue without cookies - might work with session credentials
        }
      }
      
      // Process each inventory ID
      const results = [];
      const batchSize = 5; // Process in smaller batches to avoid overwhelming the server
      
      for (let i = 0; i < inventoryIDs.length; i += batchSize) {
        const batchIds = inventoryIDs.slice(i, i + batchSize);
        
        // Process batch in parallel
        const batchPromises = batchIds.map(async (inventoryID) => {
          try {
            // Build REST URL for StockItem
            const apiUrl = `${instance}/entity/Default/20.200.001/StockItem?$filter=InventoryID eq '${inventoryID}'&$select=InventoryID,Files&$expand=Files`;
            
            // Make request
            const response = await fetch(apiUrl, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                ...(cookieString ? { 'Cookie': cookieString } : {})
              }
            });
            
            // Check response
            if (!response.ok) {
              if (response.status === 401) {
                throw new Error('Session expired.');
              }
              
              throw new Error(`API call failed (${response.status})`);
            }
            
            // Process data
            const jsonData = await response.json();
            
            // Check if we have data
            if (!jsonData.length || !jsonData[0].files || !jsonData[0].files.length) {
              return {
                inventoryID,
                success: true,
                hasImages: false,
                message: 'No images found for this inventory ID'
              };
            }
            
            // Process files
            const fileResults = [];
            for (const file of jsonData[0].files) {
              fileResults.push({
                name: file.name,
                url: `${instance}${file.href}`,
                id: file.id
              });
            }
            
            return {
              inventoryID,
              success: true,
              hasImages: true,
              files: fileResults
            };
          } catch (error) {
            return {
              inventoryID,
              success: false,
              error: error.message
            };
          }
        });
        
        // Wait for batch to complete and add results
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
        
        // Small delay between batches to be nice to the server
        if (i + batchSize < inventoryIDs.length) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
      
      return { success: true, results };
    } catch (error) {
      console.error('Error retrieving multiple stock item images:', error);
      return { success: false, error: error.message };
    }
  }

  // Monday.com connection methods
  async connectToMonday(apiKey) {
    try {
      // Test connection by making a simple API call
      const response = await fetch('https://api.monday.com/v2', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': apiKey
        },
        body: JSON.stringify({
          query: 'query { me { name } }'
        })
      });
      
      if (!response.ok) {
        throw new Error(`API call failed (${response.status})`);
      }
      
      const data = await response.json();
      
      if (data.errors) {
        throw new Error(data.errors[0].message);
      }
      
      // Set connection info
      this.connections.monday = {
        isConnected: true,
        token: apiKey
      };
      
      // Save to Chrome storage
      if (chrome?.storage?.local) {
        await chrome.storage.local.set({ 'connections': this.connections });
      }
      
      // Update the user's Monday API key in SheetDB
      try {
        const userData = await new Promise(resolve => {
          chrome.storage.local.get(['user'], data => {
            resolve(data?.user || null);
          });
        });
        
        if (userData && userData.Email) {
          // Constants for API access
          const SHEETDB_API_URL = "https://sheetdb.io/api/v1/lcon68jvxh23y";
          const SHEETDB_API_TOKEN = "Bearer w5icozbrzltd65ilssma8sk2al9i9zu7xpjkr6cc";
          
          // Update the user's Monday API key in the SheetDB
          const updateResponse = await fetch(`${SHEETDB_API_URL}/Email/${encodeURIComponent(userData.Email)}`, {
            method: "PATCH",
            headers: {
              "Content-Type": "application/json",
              "Authorization": SHEETDB_API_TOKEN,
            },
            body: JSON.stringify({ data: { "Monday API Key": apiKey } }),
          });
          
          if (!updateResponse.ok) {
            console.error(`Failed to update Monday API key in SheetDB: ${updateResponse.status}`);
          } else {
            console.log("Monday API key updated in SheetDB successfully");
            
            // Update local user data
            userData["Monday API Key"] = apiKey;
            await chrome.storage.local.set({ 'user': userData });
          }
        }
      } catch (error) {
        console.error('Error updating Monday API key in SheetDB:', error);
        // Don't block the connection process if updating SheetDB fails
      }
      
      return { 
        success: true,
        message: 'Successfully connected to Monday.com',
        userData: data.data.me
      };
    } catch (error) {
      console.error('Monday.com connection error:', error);
      return { 
        success: false, 
        error: error.message || 'Unknown error occurred' 
      };
    }
  }

  async disconnectFromMonday() {
    try {
      // Update connection info
      this.connections.monday = {
        isConnected: false,
        token: null
      };
      
      // Save to Chrome storage
      if (chrome?.storage?.local) {
        await chrome.storage.local.set({ 'connections': this.connections });
      }
      
      // Update the user's Monday API key in SheetDB (clear it)
      try {
        const userData = await new Promise(resolve => {
          chrome.storage.local.get(['user'], data => {
            resolve(data?.user || null);
          });
        });
        
        if (userData && userData.Email) {
          // Constants for API access
          const SHEETDB_API_URL = "https://sheetdb.io/api/v1/lcon68jvxh23y";
          const SHEETDB_API_TOKEN = "Bearer w5icozbrzltd65ilssma8sk2al9i9zu7xpjkr6cc";
          
          // Clear the user's Monday API key in the SheetDB
          const updateResponse = await fetch(`${SHEETDB_API_URL}/Email/${encodeURIComponent(userData.Email)}`, {
            method: "PATCH",
            headers: {
              "Content-Type": "application/json",
              "Authorization": SHEETDB_API_TOKEN,
            },
            body: JSON.stringify({ data: { "Monday API Key": "" } }),
          });
          
          if (!updateResponse.ok) {
            console.error(`Failed to clear Monday API key in SheetDB: ${updateResponse.status}`);
          } else {
            console.log("Monday API key cleared in SheetDB successfully");
            
            // Update local user data
            userData["Monday API Key"] = "";
            await chrome.storage.local.set({ 'user': userData });
          }
        }
      } catch (error) {
        console.error('Error clearing Monday API key in SheetDB:', error);
        // Don't block the disconnection process if updating SheetDB fails
      }
      
      return { 
        success: true,
        message: 'Successfully disconnected from Monday.com'
      };
    } catch (error) {
      console.error('Monday.com disconnection error:', error);
      return { 
        success: false, 
        error: error.message || 'Unknown error occurred' 
      };
    }
  }

  // Microsoft services connection methods (placeholder implementations)
  async connectToOutlook(token) {
    // Simplified implementation - would use OAuth in production
    this.connections.outlook = {
      isConnected: true,
      token: token
    };
    
    if (chrome?.storage?.local) {
      await chrome.storage.local.set({ 'connections': this.connections });
    }
    
    return { success: true, message: 'Connected to Outlook' };
  }
  
  async connectToTeams(token) {
    // Simplified implementation - would use OAuth in production
    this.connections.teams = {
      isConnected: true,
      token: token
    };
    
    if (chrome?.storage?.local) {
      await chrome.storage.local.set({ 'connections': this.connections });
    }
    
    return { success: true, message: 'Connected to Teams' };
  }

  // ShipWave connection methods
  async connectToShipWave(apiKey) {
    try {
      // Test connection by making a simple API call (placeholder)
      // In production, you'd validate the API key with an actual API call
      if (!apiKey || apiKey.trim() === '') {
        throw new Error('API key cannot be empty');
      }
      
      // Set connection info
      this.connections.shipWave = {
        isConnected: true,
        apiKey: apiKey
      };
      
      // Save to Chrome storage
      if (chrome?.storage?.local) {
        await chrome.storage.local.set({ 'connections': this.connections });
      }
      
      return { 
        success: true,
        message: 'Successfully connected to ShipWave'
      };
    } catch (error) {
      console.error('ShipWave connection error:', error);
      return { 
        success: false, 
        error: error.message || 'Unknown error occurred' 
      };
    }
  }

  async disconnectFromShipWave() {
    try {
      // Update connection info
      this.connections.shipWave = {
        isConnected: false,
        apiKey: null
      };
      
      // Save to Chrome storage
      if (chrome?.storage?.local) {
        await chrome.storage.local.set({ 'connections': this.connections });
      }
      
      return { 
        success: true,
        message: 'Successfully disconnected from ShipWave'
      };
    } catch (error) {
      console.error('ShipWave disconnection error:', error);
      return { 
        success: false, 
        error: error.message || 'Unknown error occurred' 
      };
    }
  }

  // Freight Sample connection methods
  async connectToFreightSample(token) {
    try {
      // Test connection by making a simple API call (placeholder)
      // In production, you'd validate the token with an actual API call
      if (!token || token.trim() === '') {
        throw new Error('Bearer token cannot be empty');
      }
      
      // Set connection info
      this.connections.freightSample = {
        isConnected: true,
        token: token
      };
      
      // Save to Chrome storage
      if (chrome?.storage?.local) {
        await chrome.storage.local.set({ 'connections': this.connections });
      }
      
      return { 
        success: true,
        message: 'Successfully connected to Freight Sample'
      };
    } catch (error) {
      console.error('Freight Sample connection error:', error);
      return { 
        success: false, 
        error: error.message || 'Unknown error occurred' 
      };
    }
  }

  async disconnectFromFreightSample() {
    try {
      // Update connection info
      this.connections.freightSample = {
        isConnected: false,
        token: null
      };
      
      // Save to Chrome storage
      if (chrome?.storage?.local) {
        await chrome.storage.local.set({ 'connections': this.connections });
      }
      
      return { 
        success: true,
        message: 'Successfully disconnected from Freight Sample'
      };
    } catch (error) {
      console.error('Freight Sample disconnection error:', error);
      return { 
        success: false, 
        error: error.message || 'Unknown error occurred' 
      };
    }
  }

  // Dymo Labeler connection methods
  async connectToDymo(config = {}) {
    try {
      // Placeholder implementation until the real one is developed
      // In a real implementation, we would check for Dymo SDK presence 
      // and establish connection to a printer
      
      // Set connection info
      this.connections.dymo = {
        isConnected: true,
        config: config
      };
      
      // Save to Chrome storage
      if (chrome?.storage?.local) {
        await chrome.storage.local.set({ 'connections': this.connections });
      }
      
      return { 
        success: true,
        message: 'Successfully connected to Dymo Labeler'
      };
    } catch (error) {
      console.error('Dymo connection error:', error);
      return { 
        success: false, 
        error: error.message || 'Unknown error occurred' 
      };
    }
  }

  async disconnectFromDymo() {
    try {
      // Placeholder implementation until the real one is developed
      // Update connection info
      this.connections.dymo = {
        isConnected: false,
        config: null
      };
      
      // Save to Chrome storage
      if (chrome?.storage?.local) {
        await chrome.storage.local.set({ 'connections': this.connections });
      }
      
      return { 
        success: true,
        message: 'Successfully disconnected from Dymo Labeler'
      };
    } catch (error) {
      console.error('Dymo disconnection error:', error);
      return { 
        success: false, 
        error: error.message || 'Unknown error occurred' 
      };
    }
  }

  // Utility methods
  getConnectionStatus() {
    return {
      acumatica: {
        isConnected: this.connections.acumatica.isConnected,
        instance: this.connections.acumatica.instance,
        username: this.connections.acumatica.credentials?.username
      },
      monday: {
        isConnected: this.connections.monday.isConnected
      },
      outlook: {
        isConnected: this.connections.outlook.isConnected
      },
      teams: {
        isConnected: this.connections.teams.isConnected
      },
      freightSample: {
        isConnected: this.connections.freightSample.isConnected
      },
      shipWave: {
        isConnected: this.connections.shipWave.isConnected
      },
      dymo: {
        isConnected: this.connections.dymo.isConnected
      }
    };
  }
}

// Create singleton instance
export const connectionManager = new ConnectionManager();
