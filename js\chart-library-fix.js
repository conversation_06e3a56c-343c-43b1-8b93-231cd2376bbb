/**
 * Chart Library Fix
 * This script ensures that chart libraries are properly loaded and available globally
 */

(function() {
    // Check if ApexCharts is available and fix global reference
    if (typeof ApexCharts === 'undefined' && typeof window.ApexCharts !== 'undefined') {
        console.log('Making ApexCharts globally available');
        window.ApexCharts = window.ApexCharts;
        // Also make it available without window prefix
        if (typeof globalThis !== 'undefined') {
            globalThis.ApexCharts = window.ApexCharts;
        }
    }

    // Check if echarts is available and fix global reference
    if (typeof echarts === 'undefined' && typeof window.echarts !== 'undefined') {
        console.log('Making echarts globally available');
        window.echarts = window.echarts;
        // Also make it available without window prefix
        if (typeof globalThis !== 'undefined') {
            globalThis.echarts = window.echarts;
        }
    }

    // Log the status of chart libraries
    console.log('Chart libraries status:');
    console.log('- ApexCharts available:', typeof window.ApexCharts !== 'undefined');
    console.log('- ECharts available:', typeof window.echarts !== 'undefined');

    // If libraries are not available, try to load them
    if (typeof window.ApexCharts === 'undefined') {
        console.warn('ApexCharts not found, attempting to load it');
        
        // Create a script element to load ApexCharts
        const apexScript = document.createElement('script');
        apexScript.src = 'lib/apexcharts.min.js';
        apexScript.onload = function() {
            console.log('ApexCharts loaded successfully');
        };
        apexScript.onerror = function() {
            console.error('Failed to load ApexCharts');
        };
        document.head.appendChild(apexScript);
    }

    if (typeof window.echarts === 'undefined') {
        console.warn('ECharts not found, attempting to load it');
        
        // Create a script element to load ECharts
        const echartsScript = document.createElement('script');
        echartsScript.src = 'lib/echarts.js';
        echartsScript.onload = function() {
            console.log('ECharts loaded successfully');
        };
        echartsScript.onerror = function() {
            console.error('Failed to load ECharts');
        };
        document.head.appendChild(echartsScript);
    }
})();
