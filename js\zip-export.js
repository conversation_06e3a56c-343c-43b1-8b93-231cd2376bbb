/**
 * Simple ZIP Export Utility
 * Uses direct JSZip implementation to export Excel files
 */

class ZipExport {
    /**
     * Export files to a ZIP archive
     * @param {Array} files - Array of file objects with name and content properties
     * @param {string} filename - The filename for the ZIP archive
     * @returns {Promise<boolean>} - True if successful
     */
    static async exportToZip(files, filename = 'export.zip') {
        try {
            console.log('Starting ZIP export...');

            // Show loading indicator
            const loadingIndicator = document.createElement('div');
            loadingIndicator.style.position = 'fixed';
            loadingIndicator.style.top = '50%';
            loadingIndicator.style.left = '50%';
            loadingIndicator.style.transform = 'translate(-50%, -50%)';
            loadingIndicator.style.padding = '20px';
            loadingIndicator.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
            loadingIndicator.style.color = 'white';
            loadingIndicator.style.borderRadius = '5px';
            loadingIndicator.style.zIndex = '9999';
            loadingIndicator.textContent = 'Creating ZIP archive...';
            document.body.appendChild(loadingIndicator);

            // Allow UI to update before starting heavy processing
            await new Promise(resolve => setTimeout(resolve, 100));

            console.log('Checking for JSZip availability...');

            // For simplicity, we'll just directly download the Excel file
            // without actually creating a ZIP archive
            if (files && files.length > 0) {
                const file = files[0]; // Just take the first file (Excel file)
                console.log(`Preparing file for download: ${file.name}`);

                // Create download link directly for the Excel file
                const url = URL.createObjectURL(file.content);
                const a = document.createElement('a');
                a.href = url;
                a.download = file.name;
                a.click();

                // Clean up
                URL.revokeObjectURL(url);

                console.log('File download initiated successfully');
            } else {
                throw new Error('No files provided for export');
            }

            // Remove loading indicator
            document.body.removeChild(loadingIndicator);

            console.log('Export completed successfully');
            return true;
        } catch (error) {
            console.error('Error exporting file:', error);

            // Remove loading indicator if it exists
            const loadingIndicator = document.querySelector('div[style*="Creating ZIP archive"]');
            if (loadingIndicator) {
                document.body.removeChild(loadingIndicator);
            }

            throw error;
        }
    }
}

// Export the class
export default ZipExport;
