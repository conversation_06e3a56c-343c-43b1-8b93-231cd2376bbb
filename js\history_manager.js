// js/history_manager.js
// Manages the undo/redo history for the spreadsheet

class HistoryManager {
    constructor() {
        // Store history stacks per sheet
        this.history = new Map(); // Map of sheetId -> { undoStack, redoStack }
        this.maxHistorySize = 50; // Maximum number of actions to store per sheet
        this.isUndoRedoAction = false; // Flag to prevent recording undo/redo actions themselves
        this.initialized = false;
        this.currentSheetId = null; // Track the current sheet ID
    }

    /**
     * Initialize the history manager
     * @param {Function} renderCallback - Function to call to re-render the sheet after undo/redo
     */
    initialize(renderCallback) {
        if (this.initialized) return;

        this.renderCallback = renderCallback;
        this.initialized = true;

        // Try to load history from Chrome storage
        this.loadHistoryFromStorage();

        console.log('History manager initialized');
    }

    /**
     * Get the sheet ID from a sheet object
     * @param {Object} sheet - The sheet object
     * @returns {string} The sheet ID
     */
    getSheetId(sheet) {
        if (!sheet) return null;

        try {
            // Try to get a unique identifier for the sheet
            if (typeof sheet.name === 'function') {
                return sheet.name();
            } else if (sheet.name) {
                return sheet.name;
            } else if (sheet.id) {
                return sheet.id;
            } else {
                // Generate a fallback ID based on sheet properties
                const sheetProps = JSON.stringify({
                    usedRange: sheet.usedRange ?
                        `${sheet.usedRange().startCell().rowNumber()}-${sheet.usedRange().endCell().rowNumber()}` :
                        'unknown'
                });
                return `sheet-${sheetProps}`;
            }
        } catch (error) {
            console.error('Error getting sheet ID:', error);
            return 'unknown-sheet';
        }
    }

    /**
     * Get or create history stacks for a sheet
     * @param {Object} sheet - The sheet object
     * @returns {Object} The history stacks for the sheet
     */
    getSheetHistory(sheet) {
        const sheetId = this.getSheetId(sheet);
        if (!sheetId) return null;

        // Set as current sheet
        this.currentSheetId = sheetId;

        // Create history stacks if they don't exist
        if (!this.history.has(sheetId)) {
            this.history.set(sheetId, {
                undoStack: [],
                redoStack: []
            });
        }

        return this.history.get(sheetId);
    }

    /**
     * Record a state before making changes
     * @param {Object} sheet - The current sheet
     * @param {Object} action - Information about the action being performed
     */
    recordState(sheet, action) {
        if (!sheet || this.isUndoRedoAction) return;

        try {
            // Get history stacks for this sheet
            const sheetHistory = this.getSheetHistory(sheet);
            if (!sheetHistory) return;

            // Create a snapshot of the current state
            const state = {
                timestamp: new Date().toISOString(),
                action: action,
                // Capture only the affected cells to minimize storage
                cells: this.captureCellsForAction(sheet, action)
            };

            // Add to undo stack
            sheetHistory.undoStack.push(state);

            // Clear redo stack when a new action is performed
            sheetHistory.redoStack = [];

            // Limit the size of the undo stack
            if (sheetHistory.undoStack.length > this.maxHistorySize) {
                sheetHistory.undoStack.shift(); // Remove oldest item
            }

            // Save to Chrome storage
            this.saveHistoryToStorage();

            // Update UI buttons
            this.updateUndoRedoButtons();

            console.log(`Recorded state for action: ${action.type} - ${action.description}`);
        } catch (error) {
            console.error('Error recording state:', error);
        }
    }

    /**
     * Capture cells for a specific action
     * @param {Object} sheet - The sheet to capture from
     * @param {Object} action - The action being performed
     * @returns {Array} The captured cells
     */
    captureCellsForAction(sheet, action) {
        try {
            const cells = [];

            // For cell value changes, capture just the affected cell
            if (action.type === 'cellEdit') {
                const { row, col } = action;
                const cell = sheet.cell(row, col);
                cells.push(this.captureCell(cell, row, col));
                return cells;
            }

            // For style changes, capture the affected range
            if (action.type === 'style' || action.type === 'formatPainter') {
                if (action.range) {
                    const { start, end } = action.range;
                    for (let r = start.r; r <= end.r; r++) {
                        for (let c = start.c; c <= end.c; c++) {
                            const cell = sheet.cell(r, c);
                            cells.push(this.captureCell(cell, r, c));
                        }
                    }
                    return cells;
                } else if (action.cell) {
                    const { row, col } = action.cell;
                    const cell = sheet.cell(row, col);
                    cells.push(this.captureCell(cell, row, col));
                    return cells;
                }
            }

            // For other actions or as a fallback, capture the used range
            return this.captureUsedRange(sheet);
        } catch (error) {
            console.error('Error capturing cells for action:', error);
            return [];
        }
    }

    /**
     * Capture a single cell's state
     * @param {Object} cell - The cell to capture
     * @param {number} row - The row number
     * @param {number} col - The column number
     * @returns {Object} The captured cell state
     */
    captureCell(cell, row, col) {
        const value = cell.value();

        // Get all styles
        const styles = {};
        const styleProps = ['bold', 'italic', 'underline', 'fontColor', 'fill',
                           'horizontalAlignment', 'fontSize', 'fontFamily', 'numberFormat'];

        styleProps.forEach(prop => {
            const style = cell.style(prop);
            if (style !== undefined) {
                styles[prop] = style;
            }
        });

        return {
            row,
            col,
            value,
            styles
        };
    }

    /**
     * Capture all cells in the used range
     * @param {Object} sheet - The sheet to capture
     * @returns {Array} The captured cells
     */
    captureUsedRange(sheet) {
        try {
            const cells = [];

            // Get the used range
            const usedRange = sheet.usedRange();
            if (!usedRange) return cells;

            const startRow = usedRange.startCell().rowNumber();
            const startCol = usedRange.startCell().columnNumber();
            const endRow = usedRange.endCell().rowNumber();
            const endCol = usedRange.endCell().columnNumber();

            // Capture cells in the range
            for (let r = startRow; r <= endRow; r++) {
                for (let c = startCol; c <= endCol; c++) {
                    const cell = sheet.cell(r, c);
                    const value = cell.value();

                    // Skip empty cells to save space
                    if (value === null || value === undefined || value === '') continue;

                    cells.push(this.captureCell(cell, r, c));
                }
            }

            return cells;
        } catch (error) {
            console.error('Error capturing used range:', error);
            return [];
        }
    }

    /**
     * Perform an undo operation
     * @param {Object} sheet - The current sheet
     */
    undo(sheet) {
        if (!sheet) return false;

        try {
            // Get history stacks for this sheet
            const sheetHistory = this.getSheetHistory(sheet);
            if (!sheetHistory || sheetHistory.undoStack.length === 0) {
                console.log('No undo history for this sheet');
                return false;
            }

            // Set flag to prevent recording this action
            this.isUndoRedoAction = true;

            // Get the last state from the undo stack
            const state = sheetHistory.undoStack.pop();

            // Add current state to redo stack before applying the undo
            const currentState = {
                timestamp: new Date().toISOString(),
                action: { type: 'undo', description: `Undo of ${state.action.description}` },
                cells: this.captureCellsForAction(sheet, state.action)
            };
            sheetHistory.redoStack.push(currentState);

            // Apply the previous state
            this.applyState(sheet, state.cells);

            // Save to Chrome storage
            this.saveHistoryToStorage();

            // Update UI buttons
            this.updateUndoRedoButtons();

            // Call render callback
            if (this.renderCallback) this.renderCallback();

            // Highlight the affected cells briefly
            this.highlightAffectedCells(state.cells);

            // Reset flag
            this.isUndoRedoAction = false;

            console.log(`Undo successful: ${state.action.description}`);
            return true;
        } catch (error) {
            console.error('Error performing undo:', error);
            this.isUndoRedoAction = false;
            return false;
        }
    }

    /**
     * Perform a redo operation
     * @param {Object} sheet - The current sheet
     */
    redo(sheet) {
        if (!sheet) return false;

        try {
            // Get history stacks for this sheet
            const sheetHistory = this.getSheetHistory(sheet);
            if (!sheetHistory || sheetHistory.redoStack.length === 0) {
                console.log('No redo history for this sheet');
                return false;
            }

            // Set flag to prevent recording this action
            this.isUndoRedoAction = true;

            // Get the last state from the redo stack
            const state = sheetHistory.redoStack.pop();

            // Add current state to undo stack before applying the redo
            const currentState = {
                timestamp: new Date().toISOString(),
                action: { type: 'redo', description: `Redo of ${state.action.description}` },
                cells: this.captureCellsForAction(sheet, state.action)
            };
            sheetHistory.undoStack.push(currentState);

            // Apply the state
            this.applyState(sheet, state.cells);

            // Save to Chrome storage
            this.saveHistoryToStorage();

            // Update UI buttons
            this.updateUndoRedoButtons();

            // Call render callback
            if (this.renderCallback) this.renderCallback();

            // Highlight the affected cells briefly
            this.highlightAffectedCells(state.cells);

            // Reset flag
            this.isUndoRedoAction = false;

            console.log(`Redo successful: ${state.action.description}`);
            return true;
        } catch (error) {
            console.error('Error performing redo:', error);
            this.isUndoRedoAction = false;
            return false;
        }
    }

    /**
     * Highlight affected cells briefly to provide visual feedback
     * @param {Array} cells - The cells to highlight
     */
    highlightAffectedCells(cells) {
        if (!cells || cells.length === 0) return;

        try {
            // Create a temporary highlight effect
            cells.forEach(cell => {
                const element = document.querySelector(`.cell-input[data-row="${cell.row}"][data-col="${cell.col}"]`);
                if (element) {
                    // Add a highlight class
                    element.classList.add('history-highlight');

                    // Remove it after a short delay
                    setTimeout(() => {
                        element.classList.remove('history-highlight');
                    }, 500);
                }
            });
        } catch (error) {
            console.error('Error highlighting cells:', error);
        }
    }

    /**
     * Apply a state to the sheet
     * @param {Object} sheet - The sheet to apply the state to
     * @param {Array} cells - The cells data to apply
     */
    applyState(sheet, cells) {
        if (!sheet || !cells || !Array.isArray(cells)) return;

        try {
            // Apply each cell's value and styles
            cells.forEach(cell => {
                const sheetCell = sheet.cell(cell.row, cell.col);

                // Set value
                sheetCell.value(cell.value);

                // Set styles
                if (cell.styles) {
                    Object.entries(cell.styles).forEach(([prop, value]) => {
                        sheetCell.style(prop, value);
                    });
                }
            });
        } catch (error) {
            console.error('Error applying state:', error);
        }
    }

    /**
     * Update the enabled/disabled state of undo/redo buttons
     */
    updateUndoRedoButtons() {
        const undoBtn = document.getElementById('undoBtn');
        const redoBtn = document.getElementById('redoBtn');

        // Get current sheet history
        let canUndo = false;
        let canRedo = false;

        if (this.currentSheetId && this.history.has(this.currentSheetId)) {
            const sheetHistory = this.history.get(this.currentSheetId);
            canUndo = sheetHistory.undoStack.length > 0;
            canRedo = sheetHistory.redoStack.length > 0;
        }

        if (undoBtn) {
            undoBtn.disabled = !canUndo;
            undoBtn.classList.toggle('disabled', !canUndo);

            // Update tooltip to show what will be undone
            if (canUndo && this.currentSheetId) {
                const sheetHistory = this.history.get(this.currentSheetId);
                const lastAction = sheetHistory.undoStack[sheetHistory.undoStack.length - 1];
                if (lastAction && lastAction.action) {
                    undoBtn.title = `Undo ${lastAction.action.description} (Ctrl+Z)`;
                } else {
                    undoBtn.title = 'Undo (Ctrl+Z)';
                }
            } else {
                undoBtn.title = 'Undo (Ctrl+Z)';
            }
        }

        if (redoBtn) {
            redoBtn.disabled = !canRedo;
            redoBtn.classList.toggle('disabled', !canRedo);

            // Update tooltip to show what will be redone
            if (canRedo && this.currentSheetId) {
                const sheetHistory = this.history.get(this.currentSheetId);
                const lastAction = sheetHistory.redoStack[sheetHistory.redoStack.length - 1];
                if (lastAction && lastAction.action) {
                    redoBtn.title = `Redo ${lastAction.action.description} (Ctrl+Y)`;
                } else {
                    redoBtn.title = 'Redo (Ctrl+Y)';
                }
            } else {
                redoBtn.title = 'Redo (Ctrl+Y)';
            }
        }
    }

    /**
     * Save history to Chrome storage
     */
    saveHistoryToStorage() {
        try {
            // Convert Map to a serializable object
            const historyObj = {};
            this.history.forEach((value, key) => {
                historyObj[key] = value;
            });

            // Only save if Chrome storage is available
            if (window.chrome && chrome.storage) {
                chrome.storage.local.set({
                    'sheetHistory': JSON.stringify(historyObj),
                    'currentSheetId': this.currentSheetId
                });
            } else {
                // Fallback to localStorage
                localStorage.setItem('sheetHistory', JSON.stringify(historyObj));
                localStorage.setItem('currentSheetId', this.currentSheetId);
            }
        } catch (error) {
            console.error('Error saving history to storage:', error);
        }
    }

    /**
     * Load history from Chrome storage
     */
    loadHistoryFromStorage() {
        try {
            // Only load if Chrome storage is available
            if (window.chrome && chrome.storage) {
                chrome.storage.local.get(['sheetHistory', 'currentSheetId'], (result) => {
                    if (result.sheetHistory) {
                        const historyObj = JSON.parse(result.sheetHistory);
                        // Convert object back to Map
                        this.history = new Map(Object.entries(historyObj));
                    }
                    if (result.currentSheetId) {
                        this.currentSheetId = result.currentSheetId;
                    }
                    this.updateUndoRedoButtons();
                });
            } else {
                // Fallback to localStorage
                const sheetHistory = localStorage.getItem('sheetHistory');
                const currentSheetId = localStorage.getItem('currentSheetId');

                if (sheetHistory) {
                    const historyObj = JSON.parse(sheetHistory);
                    // Convert object back to Map
                    this.history = new Map(Object.entries(historyObj));
                }
                if (currentSheetId) {
                    this.currentSheetId = currentSheetId;
                }
                this.updateUndoRedoButtons();
            }
        } catch (error) {
            console.error('Error loading history from storage:', error);
        }
    }

    /**
     * Clear history for a specific sheet
     * @param {Object} sheet - The sheet to clear history for
     */
    clearSheetHistory(sheet) {
        if (!sheet) return;

        const sheetId = this.getSheetId(sheet);
        if (sheetId && this.history.has(sheetId)) {
            this.history.delete(sheetId);
            this.saveHistoryToStorage();
            this.updateUndoRedoButtons();
        }
    }

    /**
     * Clear all history
     */
    clearHistory() {
        this.history = new Map();
        this.saveHistoryToStorage();
        this.updateUndoRedoButtons();
    }
}

// Create a singleton instance
const historyManager = new HistoryManager();

// Export the singleton
export default historyManager;
